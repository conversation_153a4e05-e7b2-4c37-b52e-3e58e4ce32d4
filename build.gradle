buildscript {
    repositories {
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
    }


    dependencies {
        classpath('se.transmode.gradle:gradle-docker:1.2')
    }
}

plugins {
    id 'org.springframework.boot' version '2.3.3.RELEASE'
    id 'io.spring.dependency-management' version '1.0.10.RELEASE'
    id 'java'
}

apply plugin: 'docker'


group = 'cn.abcyun.cis'
version = '0.0.1-SNAPSHOT'
sourceCompatibility = '1.8'

ext {
    set('springCloudVersion', "Hoxton.SR7")
    set('dockerApplication', 'abc-cis-sc-clinic-service')
    set('dockerRegistry', 'registry.cn-shanghai.aliyuncs.com')
    set('dockerGroup', 'byteflow')
    set('dockerVersion', 'latest')
    set('nexusSnapShotUrl', "https://packages.aliyun.com/maven/repository/105566-snapshot-k87VEs/")
    set('nexusReleaseUrl', 'https://packages.aliyun.com/maven/repository/105566-release-Sy2Ug0/')
    set('nexusUsername', 'ZLmZuu')
    set('nexusPassword', 'Nl4rmLzuy7')
}

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

configurations.all {
    resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
}

repositories {
    mavenCentral()
//    maven {
//        credentials {
//            username 'ZLmZuu'
//            password 'Nl4rmLzuy7'
//        }
//        url 'https://repo.rdc.aliyun.com/repository/105566-release-Sy2Ug0/'
//    }
//
//    maven {
//        credentials {
//            username "${nexusUsername}"
//            password "${nexusPassword}"
//        }
//        url "${nexusSnapShotUrl}"
//    }
    jcenter()
    maven { url 'https://mirrors.tencent.com/nexus/repository/maven-public' }
    maven { url 'https://repo.spring.io/milestone' }
    maven {
        credentials {
            username 'ZLmZuu'
            password 'Nl4rmLzuy7'
        }
        url 'https://packages.aliyun.com/maven/repository/105566-release-Sy2Ug0/'
    }
    maven {
        credentials {
            username 'ZLmZuu'
            password 'Nl4rmLzuy7'
        }
        url "https://packages.aliyun.com/maven/repository/105566-snapshot-k87VEs/"

    }
}

dependencies {
    implementation 'cn.abcyun.bis:abc-bis-rpc-sdk:3.00.97'
    implementation 'cn.abcyun.cis:abc-cis-core:0.2.31'

    implementation 'io.micrometer:micrometer-registry-prometheus'
    implementation 'org.springframework.boot:spring-boot-starter-amqp'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.cloud:spring-cloud-starter-config'
    implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'
    implementation 'org.springframework.cloud:spring-cloud-starter-netflix-hystrix'
    implementation('org.apache.rocketmq:rocketmq-spring-boot-starter:2.2.3') {
        exclude group: 'org.apache.tomcat', module: 'annotations-api'
    }

    implementation 'org.springframework.cloud:spring-cloud-starter-sleuth'
    implementation 'com.vladmihalcea:hibernate-types-52:2.9.11'
    implementation 'org.mybatis.spring.boot:mybatis-spring-boot-starter:2.1.3'
    implementation 'net.javacrumbs.shedlock:shedlock-spring:4.23.0'
    implementation 'net.javacrumbs.shedlock:shedlock-provider-redis-spring:4.23.0'
    implementation 'io.springfox:springfox-boot-starter:3.0.0'
    implementation 'com.aliyun.openservices:aliyun-log-logback-appender:0.1.19'

    implementation 'cn.abcyun.cis:abc-cis-id-generator:0.0.8'
    implementation 'cn.abcyun.cis:abc-cis-commons:2.1.6.34'
    implementation 'cn.abcyun.bis:abc-bis-message:1.0.61'
    implementation 'cn.abcyun.common:abc-common-log:0.0.7'
    implementation 'cn.abcyun.common:abc-common-model:1.0.6'
//    implementation 'cn.abcyun.cis:abc-cis-property-sdk:0.0.73'
    implementation 'com.belerweb:pinyin4j:2.5.1'
    implementation 'org.apache.logging.log4j:log4j-api:2.15.0'
    implementation 'org.apache.logging.log4j:log4j-to-slf4j:2.15.0'
    implementation 'com.aliyun.oss:aliyun-sdk-oss:3.10.2'
    implementation 'com.aliyun:aliyun-java-sdk-core:4.5.20'
    implementation 'com.aliyun:aliyun-java-sdk-dysmsapi:1.1.0'
    implementation 'com.tencentcloudapi:tencentcloud-sdk-java:3.1.779'
    implementation 'io.jsonwebtoken:jjwt:0.9.1'
    implementation 'com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.11.2'
    implementation 'cn.abcyun.scrm:abc-scrm-rpc-sdk:0.0.24'
    implementation 'org.apache.commons:commons-pool2:2.8.0'
    implementation 'org.redisson:redisson:3.11.6'
    implementation 'net.sf.ehcache:ehcache:2.10.9.2'
    implementation 'redis.clients:jedis:3.1.0'
    implementation 'cn.hutool:hutool-all:5.8.20'
    implementation 'com.googlecode.libphonenumber:libphonenumber:8.13.27'

    compile 'net.javacrumbs.shedlock:shedlock-spring:4.16.0'
    compile 'net.javacrumbs.shedlock:shedlock-provider-redis-spring:4.16.0'

    compileOnly 'org.projectlombok:lombok'
    runtimeOnly 'mysql:mysql-connector-java'
    annotationProcessor 'org.projectlombok:lombok'
    testImplementation('org.springframework.boot:spring-boot-starter-test') {
        exclude group: 'org.junit.vintage', module: 'junit-vintage-engine'
    }
    testImplementation 'org.springframework.amqp:spring-rabbit-test'
}

dependencyManagement {
    imports {
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
    }
}

test {
    useJUnitPlatform()
}

task buildUnpackJar(type: Copy) {
    dependsOn clean
    dependsOn bootJar
    tasks.findByName('bootJar').mustRunAfter('clean')

    from(zipTree(tasks.bootJar.outputs.files.singleFile))
    into("build/dependency")
}

task buildDocker(type: Docker) {
    dependsOn buildUnpackJar
    tag = "${dockerRegistry}/${dockerGroup}/${dockerApplication}"
    tagVersion = "${dockerVersion}"
    dockerfile = file('Dockerfile')

    doFirst {
        copy {
            from "build/dependency"
            into "${stageDir}/build/dependency"
        }
    }
}

task deployDocker(type: Exec) {
    dependsOn buildDocker
    commandLine "docker", "push", "${dockerRegistry}/${dockerGroup}/${dockerApplication}:${dockerVersion}"
}
