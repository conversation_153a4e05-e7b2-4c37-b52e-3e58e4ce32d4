package cn.abcyun.bis.core.message.data;

import lombok.Data;

@Data
public class ClinicCreatedMessage {
    private String id;
    private String parentId;
    private String shortId;
    private String parentShortId;
    private int nodeType;
    private int status;
    private String shortName;
    private String shortNamePyFirst;
    private String name;
    private String namePy;
    private String namePyFirst;

    private String addressDetail;
    private String contactPhone;
    private String addressGeo;

    private String addressProvinceId;
    private String addressProvinceName;
    private String addressCityId;
    private String addressCityName;
    private String addressDistrictId;
    private String addressDistrictName;

    public static class NodeType {
        public static final int INDEPENDENT_CLINIC = 0;     //独立门店
        public static final int CHAIN_HEAD_CLINIC = 1;      //连锁总部
        public static final int CHAIN_BRANCH_CLINIC = 2;    //连锁子店
    }

    public static class Status {
        public static final int NORMAL = 1;
        public static final int NOT_PURCHASED = 70;         //销售创建，但是尚未绑定版本的诊所
        public static final int DISABLE = 80;
    }
}
