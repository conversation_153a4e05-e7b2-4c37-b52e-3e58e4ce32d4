package cn.abcyun.bis.core.message.data;

import cn.abcyun.cis.clinic.model.ClinicEmployee;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 门店员工模块权限变更消息
 *
 * <AUTHOR>
 * @date 2023/6/16 11:36
 **/
@Data
public class ClinicEmployeeModuleUpdateMessage {

    /**
     * 连锁ID
     */
    private String chainId;

    /**
     * 门店ID
     */
    private String clinicId;

    /**
     * 员工ID
     */
    private String employeeId;

    /**
     * 员工状态 {@link cn.abcyun.cis.clinic.model.ClinicEmployee.Status}
     */
    private int status;

    /**
     * 原来的模块权限
     */
    private List<EmployeeModule> oldVal;

    /**
     * 新的模块权限
     */
    private List<EmployeeModule> newVal;

    public ClinicEmployeeModuleUpdateMessage() {
    }

    public ClinicEmployeeModuleUpdateMessage(ClinicEmployee clinicEmployee, List<String> moduleIdsBefore, List<String> moduleIdsAfter) {
        this.chainId = clinicEmployee.getChainId();
        this.clinicId = clinicEmployee.getClinicId();
        this.employeeId = clinicEmployee.getEmployeeId();
        this.status = clinicEmployee.getStatus();
        this.oldVal = Optional.ofNullable(moduleIdsBefore).orElseGet(ArrayList::new).stream().map(EmployeeModule::new).collect(Collectors.toList());
        this.newVal = Optional.ofNullable(moduleIdsAfter).orElseGet(ArrayList::new).stream().map(EmployeeModule::new).collect(Collectors.toList());
    }

    /**
     * 员工模块权限
     */
    @Data
    public static class EmployeeModule {

        /**
         * 模块ID {@link cn.abcyun.cis.clinic.model.Constants.ClinicModule}
         */
        String moduleId;

        public EmployeeModule(String moduleId) {
            this.moduleId = moduleId;
        }
    }

}
