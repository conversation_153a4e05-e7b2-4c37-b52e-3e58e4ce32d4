package cn.abcyun.cis.clinic.mybatis.mapper;

import cn.abcyun.bis.rpc.sdk.cis.model.clinic.GetRegionEnvZoneCountRsp;
import cn.abcyun.cis.clinic.api.view.*;
import cn.abcyun.cis.clinic.api.view.organ.QueryOrganByRegionReq;
import cn.abcyun.cis.clinic.model.Organ;
import io.lettuce.core.dynamic.annotation.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface OrganMapper {
    List<QWOrganAbstract> findOrganForQW(
            @Param("offset") int offset,
            @Param("limit") int limit,
            @Param("nodeType") Integer nodeType,
            @Param("keyword") String keyword,
            @Param("abcEmployeeId") String abcEmployeeId,
            @Param("editionStatus") Integer editionStatus
    );

    int findOrganForQWCount(
            @Param("nodeType") Integer nodeType,
            @Param("keyword") String keyword,
            @Param("abcEmployeeId") String abcEmployeeId,
            @Param("editionStatus") Integer editionStatus
    );

    List<OperationOrganAbstract> findOrganForOperation(
            @Param("offset") int offset,
            @Param("limit") int limit,
            @Param("keyword") String keyword,
            @Param("editionId") String editionId,
            @Param("editionStatus") Integer editionStatus,
            @Param("addressId") String addressId,
            @Param("nodeType") Integer nodeType,
            @Param("nodeTypeFilter") Integer nodeTypeFilter,
            @Param("source") String source,
            @Param("isTrial") Integer isTrial,
            @Param("clinicIds") List<String> clinicIds,
            @Param("hisType") Integer hisType);

    int findOrganForOperationCount(
            @Param("keyword") String keyword,
            @Param("editionId") String editionId,
            @Param("editionStatus") Integer editionStatus,
            @Param("addressId") String addressId,
            @Param("nodeType") Integer nodeType,
            @Param("nodeTypeFilter") Integer nodeTypeFilter,
            @Param("source") String source,
            @Param("isTrial") Integer isTrial,
            @Param("clinicIds") List<String> clinicIds,
            @Param("hisType") Integer hisType);

    List<ClinicAirPharmacyOperationView> findOpenedAirPharmacyClinics(@Param("offset") int offset,
                                                                      @Param("limit") int limit,
                                                                      @Param("keyword") String keyword);

    int findOpenedAirPharmacyClinicsCount(@Param("offset") int offset,
                                          @Param("limit") int limit,
                                          @Param("keyword") String keyword);

    int countMallOrgan(QueryMallOrganReq req);

    List<MallOrganView> findMallOrgan(QueryMallOrganReq req);

    int countPageListOrganByTag(@Param("areaIds") List<String> areaIds,
                                @Param("groupList") List<PageQueryClinicByTagReq.TagGroup> groupList,
                                @Param("excludeTagKeyList") List<String> excludeTagKeyList,
                                @Param("excludeClinicIdList") List<String> excludeClinicIdList,
                                @Param("organShortIds") List<String> organShortIds,
                                @Param("excludeClinicShortIdList") List<String> excludeClinicShortIdList);

    List<OrganBasicView> pageListOrganByTag(@Param("cursor") String cursor,
                                            @Param("offset") int offset,
                                            @Param("limit") int limit,
                                            @Param("areaIds") List<String> areaIds,
                                            @Param("groupList") List<PageQueryClinicByTagReq.TagGroup> groupList,
                                            @Param("excludeTagKeyList") List<String> excludeTagKeyList,
                                            @Param("excludeClinicIdList") List<String> excludeClinicIdList,
                                            @Param("organShortIds") List<String> organShortIds,
                                            @Param("excludeClinicShortIdList") List<String> excludeClinicShortIdList);


    List<GrayOrganView> pageFindGrayOrganListByRegion(@Param("offset") Integer offset,
                                                      @Param("limit") Integer limit,
                                                      @Param("regionId") String regionId);

    int countGrayOrganListByRegion(@Param("regionId") String regionId);
    /**
     * 获取指定分区，可用区下总的连锁数量
     * */
    int countEnvRegionCount(@Param("regionId") int regionId,
                            @Param("env") int env,
                            @Param("fromZone") String fromZone);
    /**
     * 更新指定分区，可用区下总的连锁数量
     * */
    int updateEnvRegionZoneLimitCount(@Param("regionId") int regionId,
                                      @Param("env") int env,
                                      @Param("normalChainOnly") int normalChainOnly, //不拉大门店
                                      @Param("zoneTaskId") Long zoneTaskId,
                                      @Param("zoneDetailId") Long zoneDetailId,
                                      @Param("fromZone") String fromZone,
                                      @Param("toZone") String toZone,
                                      @Param("limitCount") Integer limitCount);

    List<String> getRollBackChainIdList(@Param("zoneTaskId") Long zoneTaskId,
                                        @Param("toZone") String toZone,
                                        @Param("env") int env);

    int rollBackZoneTaskIdByChainIds(@Param("regionId") int regionId,
                                     @Param("zoneTaskId") Long zoneTaskId,
                                     @Param("env") int env,
                                     @Param("fromZone") String fromZone,
                                     @Param("toZone") String toZone,
                                     @Param("chainIds") List<String> chainIds);

    int rollBackZoneTaskId(@Param("regionId") int regionId,
                           @Param("zoneTaskId") Long zoneTaskId,
                           @Param("fromZone") String fromZone,
                           @Param("toZone") String toZone
    );
    List<GetRegionEnvZoneCountRsp> getRegionChainCount();

    List<String> findExpiredOrganList(@Param("regionId") String regionId);

    List<Organ> findOrgansByArea(@Param("supervisionFlag") int supervisionFlag,
                                 @Param("status") int status,
                                 @Param("addressProvinceIds") List<String> addressProvinceIds,
                                 @Param("addressCityIds") List<String> addressCityIds,
                                 @Param("addressDistrictIds") List<String> addressDistrictIds,
                                 @Param("regionId") String regionId);

    List<Organ> queryOrganByRegion(QueryOrganByRegionReq req);

    int queryOrganCountByRegion(QueryOrganByRegionReq req);

    int countExpireClinic(@Param("sellerIds") List<String> sellerIds,
                          @Param("comingExpireDate") LocalDateTime comingExpireDate,
                          @Param("comingExpireMonth") int comingExpireMonth);

    List<String> listExpireClinic(@Param("sellerIds") List<String> sellerIds,
                                  @Param("comingExpireDate") LocalDateTime comingExpireDate,
                                  @Param("comingExpireMonth") int comingExpireMonth,
                                  @Param("offset") int offset,
                                  @Param("limit") int limit);

    List<String> listClinicIds(@Param("chainIds") List<String> chainIds);

    int countOrganWithEdition(@Param("clinicIds") List<String> clinicIds);

    List<Organ> findOrganWithEdition(@Param("clinicIds") List<String> clinicIds,
                                     @Param("offset") int offset,
                                     @Param("limit") int limit);
}
