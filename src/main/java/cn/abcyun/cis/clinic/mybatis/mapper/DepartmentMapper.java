package cn.abcyun.cis.clinic.mybatis.mapper;

import cn.abcyun.cis.clinic.api.view.DepartmentEmployeeAbstract;
import cn.abcyun.cis.clinic.api.view.DepartmentWithEmployeeView;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/15 18:01
 */
@Mapper
public interface DepartmentMapper {

    List<DepartmentEmployeeAbstract> findDepartmentEmployeeAbstractList(@Param("clinicId") String clinicId);

    List<DepartmentWithEmployeeView> findDepartmentEmployeeByClinicId(@Param("clinicId") String clinicId);

    List<DepartmentWithEmployeeView> findDepartmentEmployeeByChainId(@Param("chainId") String chainId);
}
