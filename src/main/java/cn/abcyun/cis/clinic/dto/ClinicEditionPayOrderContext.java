package cn.abcyun.cis.clinic.dto;

import cn.abcyun.bis.rpc.sdk.oa.model.SupportTicketSheetView;
import cn.abcyun.bis.rpc.sdk.oa.model.sop.SopSheetStaffBriefVo;
import cn.abcyun.cis.clinic.api.view.QWClinicEditionOrderAbstract;
import cn.abcyun.cis.clinic.api.view.SupportItemCalculateReq;
import cn.abcyun.cis.clinic.model.*;
import cn.abcyun.cis.clinic.repository.ClinicEditionOrderRepository;
import cn.abcyun.cis.commons.exception.CisCustomException;
import cn.abcyun.cis.commons.exception.ParamNotValidException;
import cn.abcyun.cis.commons.util.DateUtils;
import cn.abcyun.cis.commons.util.MathUtils;
import cn.abcyun.cis.commons.util.TextUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.*;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static cn.abcyun.cis.clinic.model.ClinicEditionOrder.Type.SOP_RENEW_PURCHASE;

@Data
public class ClinicEditionPayOrderContext {
    private static final Logger sLogger = LoggerFactory.getLogger(ClinicEditionPayOrderContext.class);
    /**
     * 使用多少天 升级按新购算 (包括)
     */
    private static final int USED_DAYS_UPGRADE_TREATE_AS_NEWPURCHASE = 90;
    //========================= input ==========================
    private String id;
    private int isTrial;                // 是否试用
    private String editionId;           // 购买店版本id
    private String beginDate;           // 生效开始时间
    private String endDate;             // 生效结束时间
    private int unitCount;              // 单位数量

    private String bindClinicId;
    private Organ bindClinic;
    private String customerId;
    private List<String> promotionIds;
    private AbcEmployee seller;
    private String operatorId;

    private List<ClinicEditionAttachment> attachments;
    private String remarks;
    private int source;

    private BigDecimal reqReceivableFee;   // 应收费用 销售下单时有效
    //======================== 中间辅助变量 =========================
    /**
     * 当前门店 已经支付状态的 所有版本订单列表
     */
    private List<ClinicEditionOrder> historyOrders = new ArrayList<>();
    /**
     * 已经使用的时长 天数
     */
    private long newPurchaseUsedDays;

    /**
     * 版本首次使用 三个月内升级可以按新购优惠的天数。(三个月的天数)
     */
    private long havingNewPurchasePromotionMaxDays;

    /***
     * 是否在新购的使用中
     * 默认是0
     * */
    private int dontHaveNewPurchaseOrder;

    //========================= result =========================
    private int type;                   // 订单类型（1: 首购；2: 续费；3: 升级）
    /**
     * 是否是SOP阶段续费 0:否 1:是
     */
    private int isSopRenewPurchase;
    private ClinicCurrentEdition clinicCurrentEdition;
    private ClinicEditionOrder currentClinicEditionOrder;
    /**
     * SOP阶段续费关联的版本订单（即需要"合并"支付的订单，合在一起享受最低优惠）
     */
    private List<ClinicEditionOrder> sopRenewRelateClinicEditionOrders;
    /**
     * SOP阶段续费关联的版本订单（即需要"合并"支付的订单，合在一起享受最低优惠）
     */
    private List<QWClinicEditionOrderAbstract> sopRenewRelateClinicEditionOrderAbstracts;

    private int additionalAccountCount;
    private int additionalCooperationAccountCount;
    private LocalDate beginLocalDate;           // 生效开始时间
    private LocalDate endLocalDate;             // 生效结束时间
    private String maxBeginDate;                // 最晚生效时间
    private String maxEndDate;                  // 最晚结束时间
    private BigDecimal discountPrice;
    private BigDecimal receivableFee;
    private BigDecimal minReceivableFee;        // 最少应收金额 receivableFee = totalPrice + discountPrice + maxAdjustmentPrice + deductionFee
    private BigDecimal maxAdjustmentFee;        // 销售最大可调节金额
    private BigDecimal liJianFee = BigDecimal.ZERO;              // 立减金额
    private BigDecimal prePaidFee = BigDecimal.ZERO;              // 预付金额
    private ClinicEditionMaxAdjustment maxAdjustment; // 配置中的折扣
    //    private List<ClinicEditionOrder.DeductionOrder> deductionOrders;
//    private BigDecimal deductionFee;

    private OrderFeeInfo editionOrderFeeInfo;
    private ClinicEditionOrder.DeductionOrderInfo editionDeductionOrderInfo;

    private OrderFeeInfo accountOrderFeeInfo;
    private ClinicEditionOrder.DeductionOrderInfo accountDeductionOrderInfo;
    private ClinicEditionOrder.DeductionOrderInfo supportDeductionOrderInfo;

    private OrderFeeInfo cooperationAccountOrderFeeInfo;

    private List<SupportOrderFeeInfo> supportOrderFeeInfos;

    private int maxEmployeeCount;
    private Integer maxCooperateClinicCount;
    private long giftDays;

    private List<ClinicEditionPromotionObtained> availablePromotionObtainedList;
    private List<ClinicEditionPromotionObtained> usedPromotionObtainedList;

    private List<ClinicEditionDTO> availableEditions;

    private ClinicEditionDTO clinicEdition;

    /**
     * 是否可以按历史价格续费
     */
    private int isCanReBuyWithHistoryPrice;

    /**
     * 首购订单
     */
    private ClinicEditionOrder firstNewPurchaseOrder;

    // ============= SOP阶段续费特有字段 ==================
    // 之前所有的 SOP 阶段续费订单单位数量
    private Integer beforeAllSopRenewPurchaseOrderUnitCount;
    /**
     * 版本新购SOP单
     */
    private SopSheetStaffBriefVo buyNewStaffSopSheet;
    /**
     * 版本新购实施SOP单
     */
    private SupportTicketSheetView buyNewSupportSheetView;

    // ============= SOP阶段续费特有字段 ==================

    /**
     * 支付账号 0:ABC 10:楚天云
     */
    private Integer payAccount;

    /**
     * 开通项目的 key
     */
    private List<SupportItemCalculateReq.SupportItem> supportItems;

    public boolean isTrialToFirstPurchase() {
        return clinicCurrentEdition != null && clinicCurrentEdition.getIsTrial() == 1;
    }

    public BigDecimal getDeductionFee() {
        BigDecimal deductionFee = BigDecimal.ZERO;
        if (editionDeductionOrderInfo != null && editionDeductionOrderInfo.getDeductionFee() != null) {
            deductionFee = deductionFee.add(editionDeductionOrderInfo.getDeductionFee());
        }

        if (accountDeductionOrderInfo != null && accountDeductionOrderInfo.getDeductionFee() != null) {
            deductionFee = deductionFee.add(accountDeductionOrderInfo.getDeductionFee());
        }
        if (supportDeductionOrderInfo != null && supportDeductionOrderInfo.getDeductionFee() != null) {
            deductionFee = deductionFee.add(supportDeductionOrderInfo.getDeductionFee());
        }
        return deductionFee;
    }

    public BigDecimal getAccountDeductionFee() {
        BigDecimal deductionFee = BigDecimal.ZERO;
        if (accountDeductionOrderInfo != null && accountDeductionOrderInfo.getDeductionFee() != null) {
            deductionFee = accountDeductionOrderInfo.getDeductionFee();
        }
        return deductionFee;
    }

    public BigDecimal getEditionDeductionFee() {
        BigDecimal deductionFee = BigDecimal.ZERO;
        if (editionDeductionOrderInfo != null && editionDeductionOrderInfo.getDeductionFee() != null) {
            deductionFee = editionDeductionOrderInfo.getDeductionFee();
        }
        return deductionFee;
    }

    public BigDecimal getTotalPrice() {
        BigDecimal totalPrice = BigDecimal.ZERO;
        // 当前版本订单费用
        if (editionOrderFeeInfo != null && editionOrderFeeInfo.getTotalPrice() != null) {
            totalPrice = totalPrice.add(editionOrderFeeInfo.getTotalPrice());
        }
        // SOP阶段续费关联的版本订单费用
        if (isSopRenewPurchase == 1 && !CollectionUtils.isEmpty(sopRenewRelateClinicEditionOrders)) {
            totalPrice = sopRenewRelateClinicEditionOrders.stream().map(ClinicEditionOrder::getTotalPrice).reduce(totalPrice, BigDecimal::add);
        }

        if (accountOrderFeeInfo != null && accountOrderFeeInfo.getTotalPrice() != null) {
            totalPrice = totalPrice.add(accountOrderFeeInfo.getTotalPrice());
        }
        // 付费实施项目
        if (!CollectionUtils.isEmpty(supportOrderFeeInfos)) {
            totalPrice = supportOrderFeeInfos.stream().map(SupportOrderFeeInfo::getTotalPrice).reduce(totalPrice, BigDecimal::add);
        }

        // 药诊互通账号费用
        if (cooperationAccountOrderFeeInfo != null && cooperationAccountOrderFeeInfo.getTotalPrice() != null) {
            totalPrice = totalPrice.add(cooperationAccountOrderFeeInfo.getTotalPrice());
        }

        return totalPrice;
    }

    /**
     * 修正后的 unitCount
     */
    public int fixMaxAdjustmentUnitCount() {
        int unitCount = getUnitCount();
        Integer beforeAllSopRenewPurchaseOrderUnitCount = getBeforeAllSopRenewPurchaseOrderUnitCount();
        if (getType() == SOP_RENEW_PURCHASE) {
            // 在SOP阶段的续费享受首购续费的折扣，折扣的范围不仅仅只是当前订单，还包括新购订单和之前的SOP阶段续费订单的年数加起来
            if (beforeAllSopRenewPurchaseOrderUnitCount == null) {
                throw new ParamNotValidException("不支持SOP阶段续费");
            }
            Integer firstNewPurchaseOrderUnitCount = Optional.ofNullable(getFirstNewPurchaseOrder()).map(ClinicEditionOrder::getUnitCount).orElse(0);
            unitCount += beforeAllSopRenewPurchaseOrderUnitCount + firstNewPurchaseOrderUnitCount;
        }
        return unitCount;
    }

    public BigDecimal getSupportDeductionFee() {
        BigDecimal deductionFee = BigDecimal.ZERO;
        if (supportDeductionOrderInfo != null && supportDeductionOrderInfo.getDeductionFee() != null) {
            deductionFee = supportDeductionOrderInfo.getDeductionFee();
        }
        return deductionFee;
    }

    @Data
    public static class OrderFeeInfo {
        private int years;                  // 年数
        private int count;                  // 数量
        private String unit;                // 单位（year）
        private BigDecimal unitPrice;       // 单位定价
        private BigDecimal totalPrice;

        //------为pc基础版调价显示------
        @ApiModelProperty(value = "当前单位定价/最新定价eg：2599")
        private BigDecimal unitPriceCurrent;
        @ApiModelProperty(value = "按最新定价计算出的总价eg：2599*1")
        private BigDecimal totalPriceCurrent;
        //------为pc基础版调价显示------


        public BigDecimal getUnitPriceCurrent() {
            if (unitPriceCurrent == null) {
                return unitPrice;
            }
            return unitPriceCurrent;
        }

        public BigDecimal getTotalPriceCurrent() {
            if (totalPriceCurrent == null) {
                return totalPrice;
            }
            return totalPriceCurrent;
        }
    }

    @Data
    public static class SupportOrderFeeInfo {
        private String key;
        private BigDecimal totalPrice;
    }

    /**
     * 加载门店的所有支付的历史版本订单 & 计算总时长和当前使用时长
     *
     * @param clinicEditionOrderRepository
     * @return 影响  historyOrders / totalDays / usedDays
     */
    public void loadHistoryOrdersAndCalculateUsedDays(ClinicEditionOrderRepository clinicEditionOrderRepository) {
        /**
         * init or reInit
         * */
        this.historyOrders.clear();
        this.newPurchaseUsedDays = 0;

        /**
         * bindClinicId为空是首购，历史 版本和usedDays是 null 0
         * */
        if (StringUtils.isEmpty(bindClinicId)) {
            return;
        }

        /**
         * 诊所当前版本
         * */
        ClinicCurrentEdition clinicCurrentEdition = this.getClinicCurrentEdition();
        Instant now = Instant.now();

        this.historyOrders.addAll(clinicEditionOrderRepository.findByBindClinicIdAndStatusAndIsDeleted(
                bindClinicId,
                ClinicEditionPayOrder.Status.PAID,
                0));
        /**
         * 1。当前版本id的
         * 2。未过期的
         * 3。订单未被抵扣过的
         * */

        List<ClinicEditionOrder> canDeductionOrders = historyOrders.stream()
                .filter(clinicEditionOrder -> TextUtils.equals(clinicEditionOrder.getEditionId(), clinicCurrentEdition.getEditionId()))
                .filter(clinicEditionOrder -> clinicEditionOrder.getEndDate().isAfter(now))
                .filter(clinicEditionOrder -> TextUtils.isEmpty(clinicEditionOrder.getDeductionByOrderId()))
                .collect(Collectors.toList());
        this.firstNewPurchaseOrder = historyOrders.stream().filter(clinicEditionOrder ->
                clinicEditionOrder.getType() == ClinicEditionOrder.Type.FIRST_PURCHASE
                        && clinicEditionOrder.getIsDeleted() == 0
        ).sorted(new Comparator<ClinicEditionOrder>() {
            /**
             * 用一年 停一年 继续购买
             * 存在两个首购订单的情况 这种取第一个算
             * */
            @Override
            public int compare(ClinicEditionOrder o1, ClinicEditionOrder o2) {
                if (o1.getBeginDate() == null && o2.getBeginDate() == null) {
                    return 0;
                }
                if (o1.getBeginDate() != null && o2.getBeginDate() == null) {
                    return -1;
                }
                if (o1.getBeginDate() == null && o2.getBeginDate() != null) {
                    return 1;
                }
                return o1.getBeginDate().compareTo(o2.getBeginDate());
            }
        }).findFirst().orElse(null);
        sLogger.info("loadHistoryOrdersAndCalculateUsedDays 总版本订单数量={},可以抵扣版本订单数量={} 第一个首购订单={}", historyOrders.size(), canDeductionOrders.size(), firstNewPurchaseOrder);

        /**
         * 默认为空
         * */
        if (this.firstNewPurchaseOrder == null || this.firstNewPurchaseOrder.getBeginDate() == null) {
            dontHaveNewPurchaseOrder = 1;
            return;
        }
        /**
         * 这里可能是负数 因为当前在使用期间 买了版本 版本没开始就升级
         * */
        long usedDaysThisOrder = Duration.between(this.firstNewPurchaseOrder.getBeginDate(), Instant.now()).toDays();
        Instant minStartEditionInstant = this.firstNewPurchaseOrder.getBeginDate();
        this.newPurchaseUsedDays = usedDaysThisOrder;

        /**
         * 使用开始三个月的自然天数
         *
         */
        try {
            if (minStartEditionInstant != null) {
                LocalDateTime minStartLocalDate = DateUtils.beginOfDay(minStartEditionInstant.atZone(ZoneId.systemDefault()).toLocalDate());
                Instant beginInstant = minStartLocalDate.atZone(ZoneId.systemDefault()).toInstant();
                LocalDateTime endLocalDate = DateUtils.endOfDay(LocalDate.of(minStartLocalDate.getYear(), minStartLocalDate.getMonth(), minStartLocalDate.getDayOfMonth()).plusMonths(3));
                Instant endInstant = endLocalDate.atZone(ZoneId.systemDefault()).toInstant();
                havingNewPurchasePromotionMaxDays = Duration.between(beginInstant, endInstant).toDays();
                sLogger.info("loadHistoryOrdersAndCalculateUsedDays 选择最早开始的版本订单开始Instant={}, 转成本地时间的版本开始使用时间=({},{},)本地时间的版本开始使用3月后的时间=({},{}),版本开始三月的日期天数={}天,不存在收购订单={}",
                        minStartEditionInstant, minStartLocalDate, beginInstant, endLocalDate, endInstant, havingNewPurchasePromotionMaxDays, dontHaveNewPurchaseOrder);
            } else {
                sLogger.info("loadHistoryOrdersAndCalculateUsedDays minStartEditionInstant is null  havingPromotionUsedDays={}", havingNewPurchasePromotionMaxDays);

            }
        } catch (Exception exp) {
            sLogger.info("loadHistoryOrdersAndCalculateUsedDays exp", exp);
            throw new CisCustomException(400, "计算三月后天数异常");
        }

        /**
         * 三月加起来 不可能超过 90天
         * */
        if (havingNewPurchasePromotionMaxDays > 100) {
            havingNewPurchasePromotionMaxDays = USED_DAYS_UPGRADE_TREATE_AS_NEWPURCHASE;
        }
    }


    /**
     * 最后算打折类型
     * 解决问题：使用三月内升级 按升级版本的收购进行打折
     * 只在最终找折扣的时候调用，其他路径上该是什么类型还是设么类型
     */
    public int fixMaxAdjustmentType() {
        /**
         * 当前使用版本不是新购
         * type ---是本次购买版本的操作
         * */
        if (dontHaveNewPurchaseOrder == 1) {
            return type;
        }
        if (havingNewPurchasePromotionMaxDays > 0 &&
                type == ClinicEditionOrder.Type.UPGRADE_PURCHASE  //升级
                && newPurchaseUsedDays <= havingNewPurchasePromotionMaxDays) { //使用多少天 包括
            return ClinicEditionOrder.Type.FIRST_PURCHASE;
        }
        return type;
    }

    /**
     * 获取所有版本总金额，包含当前购买的和关联的版本
     */
    public BigDecimal getEditionOrderTotalPrice() {
        BigDecimal totalPrice = editionOrderFeeInfo.getTotalPrice();
        if (isSopRenewPurchase == 1 && !CollectionUtils.isEmpty(sopRenewRelateClinicEditionOrders)) {
            totalPrice = sopRenewRelateClinicEditionOrders.stream().map(ClinicEditionOrder::getTotalPrice)
                    .reduce(totalPrice, MathUtils::wrapBigDecimalAdd);
        }
        return totalPrice;
    }


}
