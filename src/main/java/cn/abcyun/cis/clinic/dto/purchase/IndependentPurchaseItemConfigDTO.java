package cn.abcyun.cis.clinic.dto.purchase;

import cn.abcyun.cis.clinic.enums.PurchaseItemPostProcessorEnum;
import cn.abcyun.cis.clinic.enums.PurchaseItemScopeEnum;
import cn.abcyun.cis.clinic.model.ClinicPurchaseItemMaxAdjustment;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/14 下午9:20
 * @description 独立购买项的配置信息
 */
@Getter
@Setter
public class IndependentPurchaseItemConfigDTO {

    /**
     * 独立购买项的key
     */
    private String key;

    /**
     * 是否是子级购买项(0:否 1:是)
     */
    private int children;

    /**
     * 能够购买的最大购买年数
     */
    private int maxBuyYears;

    /**
     * 还可以购买的数量
     */
    private int canBuyNum;

    /**
     * 可以续费的数量
     */
    private int canRenewNum;

    /**
     * 是否拆分订单(0:否 1:是)
     */
    @JsonIgnore
    private int splitOrder;

    /**
     * 赠送数量
     */
    private int giftNum;

    /**
     * 是否跟随大版本续费(0:否 1:是)
     */
    private int followMajorVersionRenew;

    /**
     * 是否支持时间跟随大版本(0:否 1:是):如果该参数为1时,是否跟随大版本续费参数也必须是1,且前端只能选择根据大版本时间来续费
     */
    private int timeFollowMajorVersion;

    /**
     * 大版本的开始时间(当timeFollowMajorVersion=1时才存在值)
     */
    private LocalDate majorVersionStartDate;

    /**
     * 大版本的结束时间(当timeFollowMajorVersion=1时才存在值)
     */
    private LocalDate majorVersionEndDate;

    /**
     * key对应的价格配置信息
     */
    private List<ClinicPurchaseItemMaxAdjustment> maxAdjustments;

    /**
     * 独立购买项的维度
     */
    @JsonIgnore
    private PurchaseItemScopeEnum scopeEnm;

    /**
     * 独立购买项后置处理器
     */
    @JsonIgnore
    private PurchaseItemPostProcessorEnum processorEnum;

}
