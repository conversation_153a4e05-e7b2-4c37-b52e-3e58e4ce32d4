package cn.abcyun.cis.clinic.enums;

public enum OnlineDoctorBusinessEnum {
    ONLINE_CONSULTATION(0, "微诊所模块在线咨询设置中网诊医生添加是否展示按钮-业务"),
    ONLINE_CONSULTATION_CLOSE(0, "微诊所模块在线咨询设置中网诊医生添加是否展示按钮-关闭"),
    ONLINE_CONSULTATION_OPEN(1, "微诊所模块在线咨询设置中网诊医生添加是否展示按钮-打开"),
    ;
    //业务类型
    private Integer businessType;

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    //描述
    private String desc;


    OnlineDoctorBusinessEnum(Integer businessType, String desc) {
        this.businessType = businessType;
        this.desc = desc;
    }

}
