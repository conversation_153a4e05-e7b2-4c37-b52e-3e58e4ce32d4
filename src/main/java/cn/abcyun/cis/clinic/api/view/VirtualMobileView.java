package cn.abcyun.cis.clinic.api.view;

import cn.abcyun.cis.clinic.dto.ChainEmployeeDto;
import cn.abcyun.cis.clinic.model.ClinicVirtualPhoneNumber;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/12/4 17:21
 */
@Data
public class VirtualMobileView {
    /**
     * 虚拟手机号
     */
    private String phoneNumber;
    /**
     * 绑定人员
     */
    private ChainEmployeeDto employee;
    /**
     * 虚拟账号创建时间
     */
    private LocalDateTime createTime;
    /**
     * 默认密码
     */
    private String defaultPassword;

    public String getDefaultPassword() {
        if (getEmployee() == null) {
            return defaultPassword;
        }
        return "******";
    }

    public static VirtualMobileView from(ClinicVirtualPhoneNumber virtualPhoneNumber, ChainEmployeeDto chainEmployeeDto) {
        if (virtualPhoneNumber == null) {
            return null;
        }
        VirtualMobileView view = new VirtualMobileView();
        view.setPhoneNumber(virtualPhoneNumber.getPhoneNumber());
        view.setEmployee(chainEmployeeDto);
        view.setCreateTime(virtualPhoneNumber.getCreated());
        view.setDefaultPassword(virtualPhoneNumber.getPassword());
        return view;
    }
}
