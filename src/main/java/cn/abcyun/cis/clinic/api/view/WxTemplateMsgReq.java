package cn.abcyun.cis.clinic.api.view;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 微信模板消息
 *
 * <AUTHOR>
 * @date 2023/3/2 21:38
 */
@Data
public class WxTemplateMsgReq {
    /**
     * 接收者openid: 必填
     */
    @JsonProperty("touser")
    private String touser;

    /**
     * 模板ID：必填
     */
    @JsonProperty("template_id")
    private String templateId;

    /**
     * 模板跳转链接（海外帐号没有跳转能力）：非必填
     */
    private String url;

    /**
     * 模板data: 必填
     */
    private BaseWxTemplateData data;


    public static class BaseWxTemplateData {

    }

    @Data
    public static class ValueDefine {
        private String value;

        public ValueDefine() {

        }

        public ValueDefine(String value) {
            this.value = value;
        }
    }

    /**
     * 门店续费提醒
     */
    @EqualsAndHashCode(callSuper = false)
    @Data
    public static class ClinicEditionExpiredWxTemplateData extends BaseWxTemplateData {
        private ValueDefine first;
        private ValueDefine keyword1;
        private ValueDefine keyword2;
        private ValueDefine keyword3;
        private ValueDefine remark;
    }

    /**
     * 审批成员加入门店提醒
     */
    @EqualsAndHashCode(callSuper = false)
    @Data
    public static class AuditJoinUserWxTemplateData extends BaseWxTemplateData {
        private ValueDefine first;
        private ValueDefine keyword1;
        private ValueDefine keyword2;
        private ValueDefine remark;
    }

    /**
     * 工单完成提醒
     * 工单号
     * {{character_string18.DATA}}
     * 工单标题
     * {{thing33.DATA}}
     * 客户名称
     * {{thing34.DATA}}
     * 工单状态
     * {{phrase15.DATA}}
     * 处理时间
     * {{time46.DATA}}
     */
    @EqualsAndHashCode(callSuper = false)
    @Data
    public static class ClinicTicketFinishWxTemplateData extends BaseWxTemplateData {
        @JsonProperty("character_string18")
        private ValueDefine code;
        @JsonProperty("thing33")
        private ValueDefine title;
        @JsonProperty("thing34")
        private ValueDefine clinicName;
        @JsonProperty("phrase15")
        private ValueDefine status;
        @JsonProperty("time46")
        private ValueDefine finishTime;
    }

    /**
     * 前置机收货提醒(客户侧)
     * 工单标题
     * {{thing33.DATA}}
     * 接单人
     * {{phrase14.DATA}}
     * 联系电话
     * {{phone_number3.DATA}}
     */
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class FrontEndMachineReceiptedForCustomerWxTemplateData extends BaseWxTemplateData {
        @JsonProperty("thing33")
        private ValueDefine title;

        @JsonProperty("phrase14")
        private ValueDefine name;

        @JsonProperty("phone_number3")
        private ValueDefine mobile;
    }

    /**
     * 前置机收货提醒(客户经理侧)
     * 商品名称
     * {{thing6.DATA}}
     * 客户名称
     * {{thing1.DATA}}
     * 联系人
     * {{thing10.DATA}}
     * 联系人电话
     * {{phone_number11.DATA}}
     * 收货状态 (空串或指定文本)
     * {{const8.DATA}}
     *      待实施对接
     *      ""
     */
    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class FrontEndMachineReceiptedForSellerWxTemplateData extends BaseWxTemplateData {
        @JsonProperty("thing6")
        private ValueDefine goodsName;

        @JsonProperty("thing1")
        private ValueDefine clinicName;

        @JsonProperty("thing10")
        private ValueDefine contactName;

        @JsonProperty("phone_number11")
        private ValueDefine contactMobile;

        @JsonProperty("const8")
        private ValueDefine status;

        public static class Status {
            public static final String WAIT_IMPLEMENT = "已收货，待实施对接";
        }
    }
}
