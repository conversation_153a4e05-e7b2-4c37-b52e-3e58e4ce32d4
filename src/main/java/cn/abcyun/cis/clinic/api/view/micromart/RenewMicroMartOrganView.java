package cn.abcyun.cis.clinic.api.view.micromart;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2024/6/25 下午6:20
 * @description 付费开通商场基础信息
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("付费开通微商城续费的基础信息")
public class RenewMicroMartOrganView extends MicroMartOrganBasicView {

    @ApiModelProperty("是否可以续费微商城(虚拟门店)")
    private int isRenewCloudOrgan;

}
