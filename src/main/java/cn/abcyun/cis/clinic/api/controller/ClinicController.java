package cn.abcyun.cis.clinic.api.controller;

import cn.abcyun.bis.rpc.sdk.cis.model.ec.EcAuthRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.external.agency.AliyunAfsAuthSignReq;
import cn.abcyun.bis.rpc.sdk.cis.model.external.agency.AliyunAfsRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.wallet.QueryPayOrderStatusRsp;
import cn.abcyun.bis.rpc.sdk.oa.model.ClinicTicketListView;
import cn.abcyun.bis.rpc.sdk.oa.model.ClinicTicketReplyView;
import cn.abcyun.bis.rpc.sdk.oa.model.cs.CsChatConfigAbstract;
import cn.abcyun.cis.clinic.api.view.*;
import cn.abcyun.cis.clinic.api.view.login.QuerySecurityLoginHitRsp;
import cn.abcyun.cis.clinic.api.view.map.QuerySuggestionRsp;
import cn.abcyun.cis.clinic.api.view.organ.UpdateOrganConfigReq;
import cn.abcyun.cis.clinic.api.view.organ.UpdateOrganConfigRsp;
import cn.abcyun.cis.clinic.api.view.scrm.ClinicScrmOpenApplicationView;
import cn.abcyun.cis.clinic.client.OaCsFeignClient;
import cn.abcyun.cis.clinic.client.ScrmCorpClient;
import cn.abcyun.cis.clinic.exception.ClinicError;
import cn.abcyun.cis.clinic.exception.ClinicException;
import cn.abcyun.cis.clinic.model.ClinicPharmacyConfig;
import cn.abcyun.cis.clinic.model.Constants;
import cn.abcyun.cis.clinic.model.Organ;
import cn.abcyun.cis.clinic.service.*;
import cn.abcyun.cis.clinic.utils.HttpUtils;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.exception.ParamRequiredException;
import cn.abcyun.cis.commons.jwt.CisJWTUtils;
import cn.abcyun.cis.commons.util.TextUtils;
import cn.abcyun.cis.core.aop.LogReqAndRsp;
import cn.abcyun.common.model.AbcListPage;
import cn.abcyun.common.model.AbcServiceResponse;
import cn.abcyun.scrm.rpc.sdk.rpc.model.corp.CorpView;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.node.NullNode;
import io.swagger.annotations.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/v3/clinics")
@Api(value = "ClinicController", produces = "application/json")
public class ClinicController {

    @Autowired
    private AddressRegionService addressRegionService;

    @Value("${abc.region-id}")
    private int regionId;

    @Autowired
    private OrganService organService;

    @Autowired
    private ClinicEditionService clinicEditionService;

    @Autowired
    private EmployeeService employeeService;

    @Autowired
    private ClinicThemeConfigService clinicThemeConfigService;

    @Autowired
    private ScrmCorpClient scrmCorpClient;

    @Autowired
    private ClinicScrmOpenApplicationService clinicScrmOpenApplicationService;

    @Autowired
    private ExternalAgencyService externalAgencyService;

    @Autowired
    private ClinicRegionService clinicRegionService;

    @Autowired
    private ClinicPharmacyService clinicPharmacyService;
    @Autowired
    private ClinicService clinicService;
    @Autowired
    private LbsQQMapService lbsQQMapService;
    @Autowired
    private OaCsFeignClient oaCsFeignClient;

    @GetMapping("/{id}")
    @ApiOperation(value = "根据id查询组织信息", produces = "application/json")
    public AbcServiceResponse<OrganView> getOrganById(@PathVariable("id") String id) {
        Organ organ = organService.getOrganById(id);
        OrganView rsp = OrganView.from(organ);
        if (rsp == null) {
            throw new NotFoundException();
        }
        return new AbcServiceResponse<>(rsp);
    }

    @GetMapping("/open/{id}")
    @ApiOperation(value = "根据id查询组织信息开放接口", produces = "application/json")
    public AbcServiceResponse<OrganView> getOrganByIdOpen(@PathVariable("id") String id) {
        Organ organ = organService.getOrganById(id);
        OrganView rsp = OrganView.from(organ);
        if (rsp == null) {
            throw new NotFoundException();
        }
        return new AbcServiceResponse<>(rsp);
    }

    /***
     *  获取当前门店的版本信息
     *  返回当前门店登录员工可以查看的版本视图
     * */
    @GetMapping("/edition/current")
    @ApiOperation(value = "获取当前门店的版本信息", produces = "application/json")
    public AbcServiceResponse<ClinicCurrentEditionComposeView> getClinicEditionById(@RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                    @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId) {
        ClinicCurrentEditionComposeView currentEditionCompose = clinicEditionService.getClinicCurrentEditionComposeViewByEmployee(clinicId, employeeId);
        if (currentEditionCompose == null) {
            throw new NotFoundException();
        }
        return new AbcServiceResponse<>(currentEditionCompose);
    }

    @PutMapping("/edition/mark-reminder-read")
    @ApiOperation(value = "版本标记提醒已读", produces = "application/json")
    public AbcServiceResponse<String> markClinicExpireReminderRead(@RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                   @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId) {
        return new AbcServiceResponse<>(clinicEditionService.markClinicExpireReminderRead(clinicId, employeeId));
    }

    @GetMapping("/edition/order/{orderId}")
    public AbcServiceResponse<ClinicEditionOrderView> getClinicEditionOrder(@PathVariable("orderId") String id,
                                                                            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID, required = false) String chainId,
                                                                            @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId) {
        ClinicEditionOrderView clinicEditionOrderView = clinicEditionService.getClinicEditionOrderView(id, chainId, employeeId);
        return new AbcServiceResponse<>(clinicEditionOrderView);
    }

    @PostMapping("/edition/order")
    public AbcServiceResponse<ClinicEditionOrderView> createClinicEditionOrder(@RequestBody @Valid ClinicEditionOrderCreateReq reqBody,
                                                                               @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                               @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID, required = false) String chainId,
                                                                               @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID, required = false) String clinicId) {
        ClinicEditionOrderView clinicEditionOrderView = clinicEditionService.createClinicEditionOrderByCustomer(reqBody, chainId, clinicId, employeeId);
        return new AbcServiceResponse<>(clinicEditionOrderView);
    }


    @PostMapping("/edition/order/calculate")
    public AbcServiceResponse<ClinicEditionOrderCalculateRsp> calculateClinicEditionOrder(@RequestBody @Valid ClinicEditionOrderCalculateReq reqBody,
                                                                                          @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                                          @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID, required = false) String chainId,
                                                                                          @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID, required = false) String clinicId) {
        ClinicEditionOrderCalculateRsp rsp = clinicEditionService.calculateClinicEditionOrder(reqBody, chainId, clinicId, employeeId);
        return new AbcServiceResponse<>(rsp);
    }


    @PutMapping("/edition/order/{orderId}/pay")
    public AbcServiceResponse<QWPayForClinicEditionOrderRsp> payClinicEditionOrder(@PathVariable("orderId") String id,
                                                                                   @RequestBody QWPayForClinicEditionOrderReq reqBody,
                                                                                   HttpServletRequest httpServletRequest,
                                                                                   @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId) {
        String clientIp = HttpUtils.getClientIpAddress(httpServletRequest);
        QWPayForClinicEditionOrderRsp rsp = clinicEditionService.payClinicEditionOrder(id, reqBody.getPayMode(), null, clientIp, employeeId, null);
        return new AbcServiceResponse<>(rsp);
    }

    @GetMapping("/edition/order/{orderId}/status")
    public AbcServiceResponse<QueryPayOrderStatusRsp> queryClinicEditionOrderStatus(@PathVariable("orderId") String id) {
        QueryPayOrderStatusRsp rsp = clinicEditionService.queryClinicEditionOrderStatus(id);
        return new AbcServiceResponse<>(rsp);
    }


    @PutMapping("/edition/order/{orderId}/close")
    public AbcServiceResponse<ClinicEditionOrderCloseRsp> closeClinicEditionOrder(@PathVariable("orderId") String id,
                                                                                  @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                                  @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID, required = false) String chainId) {
        ClinicEditionOrderCloseRsp rsp = clinicEditionService.closeClinicEditionOrder(id, chainId, employeeId);
        return new AbcServiceResponse<>(rsp);
    }

    @GetMapping("/edition/order/waiting-by-customer")
    public AbcServiceResponse<Object> getWaitingByCustomerClinicEditionOrderView(@RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId) {
        ClinicEditionOrderView rsp = clinicEditionService.getWaitingByCustomerClinicEditionOrderView(employeeId);
        if (rsp == null) {
            return new AbcServiceResponse<>(NullNode.getInstance());
        }
        return new AbcServiceResponse<>(rsp);
    }

    @GetMapping("/edition/order/waiting-by-clinic")
    public AbcServiceResponse<Object> getWaitingByBindClinicIdClinicEditionOrderView(@RequestParam("clinicId") String clinicId,
                                                                                     @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                                     @RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId) {
        ClinicEditionOrderView rsp = clinicEditionService.getWaitingByBindClinicIdClinicEditionOrderView(clinicId, chainId, employeeId);
        if (rsp == null) {
            return new AbcServiceResponse<>(NullNode.getInstance());
        }
        return new AbcServiceResponse<>(rsp);
    }


    @GetMapping("/edition/center")
    public AbcServiceResponse<ClinicEditionCenterView> getClinicEditionCenter(@RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) {
        ClinicEditionCenterView clinicEditionCenterView = clinicEditionService.getClinicEditionCenter(clinicId);
        return new AbcServiceResponse<>(clinicEditionCenterView);
    }

    @Deprecated
    @GetMapping("/chain/clinics")
    @ApiOperation(value = "查询连锁店之下的子店列表信息", produces = "application/json")
    public AbcServiceResponse<ChainClinicListView> getChainClinicListWithEdition(@RequestParam(required = false, defaultValue = "") String name,
                                                                                 @RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                 @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                 @RequestHeader(CisJWTUtils.CIS_HEADER_VIEW_MODE) String viewMode) {
        ChainClinicListView chainClinicList = clinicEditionService.getChainClinicListWithEdition(chainId, clinicId, viewMode, false);
        // yangjie 新增需求可以根据名字或者内部简称过滤
        if (StrUtil.isNotBlank(name) && CollUtil.isNotEmpty(chainClinicList.getRows())) {
            chainClinicList.setRows(chainClinicList.getRows().stream()
                    .filter(clinic -> StrUtil.contains(clinic.getName(), name.trim()) || StrUtil.contains(clinic.getShortName(), name.trim()))
                    .sorted(Comparator.comparing(ChainClinicListView.Clinic::getName))
                    .collect(Collectors.toList()));
        }
        return new AbcServiceResponse<>(chainClinicList);
    }

    @GetMapping("/chain/clinics/search")
    @ApiOperation(value = "分页查询药店连锁店之下的子店列表信息", produces = "application/json")
    public AbcServiceResponse<AbcListPage<OrganListView>> getOrganPageByParentId(@RequestParam(value = "keyword", required = false) String keyword,
                                                                                 @RequestParam(value = "offset", defaultValue = "0") Integer offset,
                                                                                 @RequestParam(value = "limit", defaultValue = "20") Integer limit,
                                                                                 @RequestParam(value = "busMode", required = false) Integer busMode,
                                                                                 @RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                 @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                 @RequestHeader(CisJWTUtils.CIS_HEADER_VIEW_MODE) int viewMode) {
        return new AbcServiceResponse<>(organService.getOrganPageByParentId(chainId, clinicId, viewMode,
                Organ.HisType.PHARMACY, Organ.NodeType.CHAIN_BRANCH_CLINIC, keyword, offset, limit, busMode));
    }

    @GetMapping("/chain/same-city-clinics")
    @ApiOperation(value = "查询与连锁所在城市相同的子店", produces = "application/json")
    public AbcServiceResponse<AbcListPage<OrganBasicView>> getChainSameCityClinics(@RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId) {
        AbcListPage<OrganBasicView> rsp = new AbcListPage<>();
        List<OrganBasicView> rows = organService.getChainSameCityClinics(chainId);
        rsp.setRows(rows);
        return new AbcServiceResponse<>(rsp);
    }

    @GetMapping("/chain/clinic-employees")
    public AbcServiceResponse<ChainClinicListView> getChainClinicListWithEditionAndEmployee(@RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                            @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                            @RequestHeader(CisJWTUtils.CIS_HEADER_VIEW_MODE) String viewMode) {
        ChainClinicListView chainClinicListView = clinicEditionService.getChainClinicListWithEdition(chainId, clinicId, viewMode, true);
        return new AbcServiceResponse<>(chainClinicListView);
    }

    @GetMapping("/modules")
    public AbcServiceResponse<ClinicModuleTreeNode> getClinicModuleTree(@RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                        @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) {
        Organ organ = organService.getOrganById(clinicId);
        if (organ == null) {
            throw new ClinicException(ClinicError.ORGAN_NOT_EXISTED);
        }
        List<ClinicModuleTreeNode> moduleTreeNodeList = employeeService.getMatchModules(organ, chainId, clinicId)
                .stream().map(ClinicModuleTreeNode::from).collect(Collectors.toList());

        ClinicModuleTreeNode rootNode = new ClinicModuleTreeNode();
        rootNode.setId(0).setName(Constants.ALL_MODULES);
        if (moduleTreeNodeList.isEmpty()) {
            return new AbcServiceResponse<>(rootNode);
        }
        rootNode.setChildren(employeeService.buildModuleTree(moduleTreeNodeList));
        return new AbcServiceResponse<>(rootNode);
    }

    @GetMapping("/roles")
    @ApiOperation(value = "获取角色列表", produces = "application/json")
    public AbcServiceResponse<AbcListPage<ClinicRoleView>> listClinicRoles(@RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                           @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) {
        List<ClinicRoleView> rows = organService.listClinicRoles(chainId, clinicId, 0);
        AbcListPage<ClinicRoleView> result = new AbcListPage<>();
        result.setRows(rows);
        return new AbcServiceResponse<>(result);
    }

    @GetMapping("/roles-tree")
    @ApiOperation(value = "获取树形结构的角色列表", produces = "application/json")
    public AbcServiceResponse<AbcListPage<ClinicRoleView>> listClinicRolesTree(@RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                               @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) {
        List<ClinicRoleView> rows = organService.listClinicRoles(chainId, clinicId, 1);
        AbcListPage<ClinicRoleView> result = new AbcListPage<>();
        result.setRows(rows);
        return new AbcServiceResponse<>(result);
    }

    @PostMapping("/role/add")
    @ApiOperation(value = "添加角色", produces = "application/json")
    public AbcServiceResponse<ClinicRoleView> addClinicRole(@RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                            @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                            @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                            @Valid @RequestBody AddClinicRoleReq req) {
        ClinicRoleView clinicRoleView = organService.addClinicRole(chainId, clinicId, employeeId, req);
        return new AbcServiceResponse<>(clinicRoleView);
    }

    @PutMapping("/role/{roleId}/update")
    @ApiOperation(value = "更新角色", produces = "application/json")
    public AbcServiceResponse<ClinicRoleView> updateClinicRole(@RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                               @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                               @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                               @RequestHeader(CisJWTUtils.CIS_HEADER_HIS_TYPE) Integer hisType,
                                                               @PathVariable("roleId") Integer roleId,
                                                               @Valid @RequestBody AddClinicRoleReq req) {
        ClinicRoleView clinicRoleView = organService.updateClinicRole(chainId, clinicId, hisType, employeeId, roleId, req);
        return new AbcServiceResponse<>(clinicRoleView);
    }

    @DeleteMapping("/role/{roleId}/delete")
    @ApiOperation(value = "删除角色", produces = "application/json")
    public AbcServiceResponse<BaseSuccessRsp> deleteClinicRole(@RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                               @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                               @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                               @RequestHeader(CisJWTUtils.CIS_HEADER_HIS_TYPE) Integer hisType,
                                                               @PathVariable("roleId") Integer roleId) {
        organService.deleteClinicRole(chainId, clinicId, hisType, employeeId, roleId);
        return new AbcServiceResponse<>(new BaseSuccessRsp());
    }

    @GetMapping("/theme-config")
    @ApiOperation(value = "获取主题列表", produces = "application/json")
    public AbcServiceResponse<AbcListPage<ClinicThemeConfigView>> getThemeConfig() {
        List<ClinicThemeConfigView> rows = clinicThemeConfigService.listClinicThemeConfig();
        AbcListPage<ClinicThemeConfigView> result = new AbcListPage<>();
        result.setRows(rows);
        return new AbcServiceResponse<>(result);
    }

    @GetMapping
    @ApiOperation(value = "查询诊所设置信息", produces = "application/json")
    public AbcServiceResponse<OrganDetailView> getOrganDetail(@RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) {
        OrganDetailView organDetailView = organService.getOrganDetail(clinicId);
        CorpView qwCorpInfo = scrmCorpClient.getCorpByQwCorpId(organDetailView.getQwCorpId());
        organDetailView.setQwCorpInfo(qwCorpInfo);
        return new AbcServiceResponse<>(organDetailView);
    }

    @PutMapping
    @ApiOperation(value = "更新诊所设置信息", produces = "application/json")
    public AbcServiceResponse<OrganFullView> updateOrgan(@RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                         @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                         @Valid @RequestBody cn.abcyun.cis.clinic.api.view.UpdateOrganReq req) {
        OrganFullView organFullView = organService.updateOrgan(clinicId, req, employeeId);
        return new AbcServiceResponse<>(organFullView);
    }

    @PutMapping("/{clinicId}/bus-mode/{busMode}")
    @ApiOperation(value = "更新诊所直营/加盟状态信息")
    public AbcServiceResponse<OrganFullView> updateOrganBusMode(@PathVariable("clinicId") String clinicId,
                                                                @PathVariable("busMode") int busMode,
                                                                @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                @RequestBody UpdateClinicPermissionReq req) {
        OrganFullView organFullView = organService.updateOrganBusMode(clinicId, busMode, employeeId, req);
        return new AbcServiceResponse<>(organFullView);
    }

    @PutMapping("/certs")
    @ApiOperation(value = "更新诊所证照资质", produces = "application/json")
    public AbcServiceResponse<OrganFullView> updateOrganCerts(@RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                              @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                              @Valid @RequestBody UpdateOrganCertsReq req) {
        req.setClinicId(clinicId);
        req.setOperatorId(employeeId);
        req.setSourceForm(UpdateOrganCertsReq.SourceForm.PC);
        OrganFullView organFullView = organService.updateOrganCerts(req);
        return new AbcServiceResponse<>(organFullView);
    }

    /**
     * 接口返回数据量过大，弃用
     */
    @Deprecated
    @GetMapping("/list-with-employees")
    @ApiOperation(value = "查询门店及其员工列表", produces = "application/json")
    public AbcServiceResponse<AbcListPage<ClinicWithEmployeesView>> listClinicsWithEmployeesView(@RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                                 @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                                 @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_TYPE) String clinicType,
                                                                                                 @RequestParam(required = false, defaultValue = "0") @ApiParam(value = "非必传 不填的时候返回诊所所有人， 填1的时候，只返回医生") Integer isDoctor,
                                                                                                 @RequestParam(required = false, defaultValue = "0") @ApiParam(value = "非必传 不填的时候返回诊所所有人， 填1的时候，只返微诊所可见的人") Integer showInWeClinic) {
        List<ClinicWithEmployeesView> rows = organService.listClinicsWithEmployeesView(chainId, clinicId, clinicType, isDoctor, showInWeClinic);

        AbcListPage<ClinicWithEmployeesView> result = new AbcListPage<>();
        result.setRows(rows);
        return new AbcServiceResponse<>(result);
    }


    @GetMapping("/chain")
    @ApiOperation(value = "查询总部信息", produces = "application/json")
    public AbcServiceResponse<ChainOrganView> getChainOrganView(@RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId) {
        ChainOrganView chainOrganView = organService.getChainOrganView(chainId);
        return new AbcServiceResponse<>(chainOrganView);
    }

    @GetMapping("/air-pharmacy/support")
    @ApiOperation(value = "查询支持空中药房的门店", produces = "application/json")
    public AbcServiceResponse<AbcListPage<ClinicAirPharmacyView>> listClinicsSupportAirPharmacy(@RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId) {
        List<ClinicAirPharmacyView> rows = organService.listClinicsSupportAirPharmacy(chainId);
        AbcListPage<ClinicAirPharmacyView> result = new AbcListPage<>();
        result.setRows(rows);
        return new AbcServiceResponse<>(result);
    }

    @GetMapping("/air-pharmacy/support/vendors")
    @ApiOperation(value = "查询空中药房供应商", produces = "application/json")
    public AbcServiceResponse<AbcListPage<AirPharmacyVendorView>> listVendorsSupportAirPharmacy(@RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                                @RequestParam(required = false, defaultValue = "0") Integer offset,
                                                                                                @RequestParam(required = false, defaultValue = "10") Integer limit) {
        AbcListPage<AirPharmacyVendorView> result = organService.listVendorsSupportAirPharmacy(clinicId, offset, limit);
        return new AbcServiceResponse<>(result);
    }

    @GetMapping("/air-pharmacy/config")
    @ApiOperation(value = "查询门店空中药房配置", produces = "application/json")
    public AbcServiceResponse<ClinicAirPharmacyConfig> getClinicAirPharmacyConfig(@RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                  @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                  @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_TYPE) String clinicType,
                                                                                  @RequestHeader(CisJWTUtils.CIS_HEADER_VIEW_MODE) String viewMode) {
        return new AbcServiceResponse<>(organService.getClinicAirPharmacyConfig(chainId, clinicId, clinicType, viewMode));
    }

    @PutMapping("/air-pharmacy/switch")
    @ApiOperation(value = "开启/关闭空中药房", produces = "application/json")
    @LogReqAndRsp
    public AbcServiceResponse<ClinicAirPharmacySwitchRsp> updateClinicAirPharmacyConfig(@RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                        @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicid,
                                                                                        @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                                        @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_TYPE) String clinicType,
                                                                                        @RequestHeader(CisJWTUtils.CIS_HEADER_VIEW_MODE) String viewMode,
                                                                                        @RequestBody @Valid ClinicAirPharmacySwitchReq req) {
        if (StringUtils.isEmpty(req.getClinicId())) {
            req.setClinicId(clinicid);
        }
        return new AbcServiceResponse<>(organService.updateClinicAirPharmacyConfig(chainId, employeeId, req, clinicType, viewMode));
    }

    @ApiImplicitParams({
            @ApiImplicitParam(value = "服务名", name = "serviceName", required = false, paramType = "query", dataType = "string", dataTypeClass = String.class, defaultValue = "abc-cis-static-pc-service")
    })
    @ApiOperation(value = "查询当前门店前端版本", notes = "", httpMethod = "GET")
    @GetMapping("/frontend-version")
    public AbcServiceResponse<FrontendVersionView> getFrontendVersion(@RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                      @RequestParam(required = false, defaultValue = "abc-cis-static-pc-service") String serviceName) {
        return new AbcServiceResponse<>(organService.getFrontendVersion(chainId, regionId, serviceName));
    }


    @GetMapping("/data-permission")
    @ApiOperation(value = "查询当前门店数据权限", produces = "application/json")
    public AbcServiceResponse<ClinicDataPermissionView> getDataPermission(@RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                          @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) {
        return new AbcServiceResponse<>(organService.getDataPermission(chainId, clinicId));
    }


    @PostMapping("/data-permission")
    @ApiOperation(value = "新增或修改前门店数据权限", produces = "application/json")
    public AbcServiceResponse<ClinicDataPermissionView> upsertDataPermission(@RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                             @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                             @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                             @Valid @RequestBody ClinicDataPermissionUpdateReq saveReq) {
        return new AbcServiceResponse<>(organService.upsertDataPermission(chainId, clinicId, saveReq, employeeId));
    }

    /**
     * 创建开通 SCRM 申请
     */
    @ApiOperation("创建开通SCRM申请")
    @PostMapping("/scrm/open-application")
    public AbcServiceResponse<ClinicScrmOpenApplicationView> createOpenScrmApplication(@RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                       @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                       @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId) {
        ClinicScrmOpenApplicationView scrmOpenApplicationView = clinicScrmOpenApplicationService.createScrmOpenApplication(chainId, clinicId, employeeId);
        return new AbcServiceResponse<>(scrmOpenApplicationView);
    }

    /**
     * 查询当前员工开通 SCRM 申请
     */
    @ApiOperation("查询当前员工开通SCRM申请")
    @GetMapping("/scrm/open-application")
    public AbcServiceResponse<ClinicScrmOpenApplicationView> getOpenScrmApplication(@RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                    @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                    @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId) {
        ClinicScrmOpenApplicationView scrmOpenApplicationView = clinicScrmOpenApplicationService.getScrmOpenApplication(chainId, clinicId, employeeId);
        return new AbcServiceResponse<>(scrmOpenApplicationView);
    }

    /**
     * 阿里云 AFS 签名认证
     */
    @ApiOperation("阿里云AFS签名认证")
    @PostMapping("/aliyun/afs/auth-sign")
    public AbcServiceResponse<AliyunAfsRsp> aliyunAfsAuthSign(@RequestBody AliyunAfsAuthSignReq req) {
        AliyunAfsRsp aliyunAfsRsp = externalAgencyService.aliyunAfsAuthSign(req);
        return new AbcServiceResponse<>(aliyunAfsRsp);
    }

    /**
     * 生成 ABC Tool OSS token
     */
    @ApiOperation("生成 ABC Tool OSS token")
    @PostMapping("/abc-tool/oss-token")
    public AbcServiceResponse<QueryAbcToolOssTokenRsp> getAbcToolOssToken(HttpServletRequest request) {
        QueryAbcToolOssTokenRsp abcToolOssTokenRsp = organService.getAbcToolOssToken(request);
        return new AbcServiceResponse<>(abcToolOssTokenRsp);
    }

    @ApiOperation("门店升级最小年数")
    @GetMapping("/edition/min-upgrade-years")
    public AbcServiceResponse<QueryClinicUpgradeMinYearsRsp> getMinUpgradeYears(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID, required = false) String chainId,
                                                                                @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID, required = false) String clinicid,
                                                                                @RequestParam(required = false) String clinicId) {

        if (TextUtils.equals(chainId, clinicid)) {
            // 总部
            if (StringUtils.isEmpty(clinicId)) {
                throw new ParamRequiredException("clinicId 不能为空");
            }
        } else {
            // 子店
            clinicId = clinicid;
        }

        QueryClinicUpgradeMinYearsRsp rsp = clinicEditionService.getMinUpgradeYears(clinicId);
        return new AbcServiceResponse<>(rsp);
    }

    @ApiOperation(value = "查询切换门店方式", produces = "application/json")
    @GetMapping("/switch-clinic/mode")
    public AbcServiceResponse<SwitchClinicModeRsp> getSwitchClinicMode(@RequestParam String clinicId) {
        SwitchClinicModeRsp rsp = clinicRegionService.getSwitchClinicMode(clinicId);
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 202406已经废弃，从scGoods出
     * 经营范围 诊所 药品 供应商
     */
    @ApiOperation(value = "查询药店经营范围", produces = "application/json")
    @GetMapping("/pharmacy/config/business_scope")
    public AbcServiceResponse<AbcListPage<ClinicPharmacyConfigView>> listPharmacyConfigBusinessScope() {
        AbcListPage<ClinicPharmacyConfigView> rsp = new AbcListPage<>();
        List<ClinicPharmacyConfigView> rows = clinicPharmacyService.listPharmacyConfig(ClinicPharmacyConfig.Type.BUSINESS_SCOPE);
        rsp.setRows(rows);
        return new AbcServiceResponse<>(rsp);
    }

    @ApiOperation(value = "查询药店资质类型", produces = "application/json")
    @GetMapping("/pharmacy/config/qualification_type")
    public AbcServiceResponse<AbcListPage<ClinicPharmacyConfigView>> listPharmacyConfigQualificationType() {
        AbcListPage<ClinicPharmacyConfigView> rsp = new AbcListPage<>();
        List<ClinicPharmacyConfigView> rows = clinicPharmacyService.listPharmacyConfig(ClinicPharmacyConfig.Type.QUALIFICATION_TYPE);
        rsp.setRows(rows);
        return new AbcServiceResponse<>(rsp);
    }

    @ApiOperation(value = "处理中工单计数")
    @GetMapping("/ticket/processing-count")
    public AbcServiceResponse<TicketCountView> processingCount(@RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) {

        return new AbcServiceResponse<>(clinicService.processingCount(clinicId));
    }


    @ApiOperation(value = "根据门店ID查工单列表")
    @GetMapping("/list/by-clinic-id")
    public AbcServiceResponse<AbcListPage<ClinicTicketListView>> listClinicTicketByClinicId(@RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                            @RequestParam(value = "offset") int offset,
                                                                                            @RequestParam(value = "limit") int limit) {
        AbcListPage<ClinicTicketListView> rsp = clinicService.listClinicTicketByClinicId(clinicId, offset, limit);
        return new AbcServiceResponse<>(rsp);
    }

    @ApiOperation(value = "查询门店是否可以开通医保")
    @GetMapping("/support/shebao")
    public AbcServiceResponse<SupportShebaoItemRsp> supportShebao(@RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) {
        return new AbcServiceResponse<>(clinicEditionService.supportShebao(clinicId));
    }


    @ApiOperation(value = "电商授权", produces = "application/json")
    @PostMapping("/ec/auth")
    @LogReqAndRsp
    public AbcServiceResponse<EcAuthRsp> ecAuth(@RequestBody @Valid ClinicEcAuthReq req) {
        EcAuthRsp rsp = organService.ecAuth(req, "ec-auth");
        return new AbcServiceResponse<>(rsp);
    }

    @ApiOperation(value = "查询当前连锁药诊互通账号列表", produces = "application/json")
    @GetMapping("/co-clinic/list-by-chain")
    public AbcServiceResponse<AbcListPage<CooperationClinicView>> listCoClinicByChain(@RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                      @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_TYPE) String nodeType,
                                                                                      @RequestHeader(CisJWTUtils.CIS_HEADER_VIEW_MODE) String viewMode,
                                                                                      @RequestParam int type,
                                                                                      @RequestParam(required = false, defaultValue = "0") int businessType) {
        AbcListPage<CooperationClinicView> rsp = new AbcListPage<>();
        if (String.valueOf(Organ.ViewMode.NORMAL).equals(viewMode) && String.valueOf(Organ.NodeType.CHAIN_BRANCH_CLINIC).equals(nodeType)) {
            throw new ClinicException(ClinicError.SERVICE_ERROR.getCode(), "连锁子店不允许调用该接口");
        }
        rsp.setRows(clinicEditionService.listCooperationClinicByChain(chainId, type, businessType));
        return new AbcServiceResponse<>(rsp);
    }

    @ApiOperation(value = "查询当前门店药诊互通账号列表", produces = "application/json")
    @GetMapping("/co-clinic/list-by-clinic")
    public AbcServiceResponse<AbcListPage<CooperationClinicView>> listCoClinicByClinic(@RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                       @RequestParam int type,
                                                                                       @RequestParam(required = false, defaultValue = "0") int businessType) {
        AbcListPage<CooperationClinicView> rsp = new AbcListPage<>();
        rsp.setRows(clinicEditionService.listCooperationClinicByClinic(clinicId, type,businessType));
        return new AbcServiceResponse<>(rsp);
    }

    @ApiOperation(value = "删除合作诊所", produces = "application/json")
    @DeleteMapping("/co-clinic/{coClinicId}/clinic/{clinicId}")
    @LogReqAndRsp
    public AbcServiceResponse<BaseSuccessRsp> deleteCoClinic(@PathVariable("coClinicId") String coClinicId,
                                                             @PathVariable("clinicId") String clinicId,
                                                             @RequestParam(required = false, defaultValue = "0") int businessType) {
        clinicEditionService.deleteCooperationClinic(coClinicId, clinicId, businessType);
        return new AbcServiceResponse<>(new BaseSuccessRsp());
    }

    @ApiOperation(value = "合作诊所统计详情", produces = "application/json")
    @GetMapping("/co-clinic/count-info")
    public AbcServiceResponse<CooperationClinicCountInfo> getCoClinicCountInfo(@RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                               @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                               @RequestParam int type) {
        CooperationClinicCountInfo rsp = clinicEditionService.getCoClinicCountInfo(chainId, clinicId, type);
        return new AbcServiceResponse<>(rsp);
    }

    @ApiOperation(value = "保存安全登录设置", produces = "application/json")
    @PostMapping("/security-login")
    @LogReqAndRsp
    public AbcServiceResponse<ClinicSecurityLoginView> saveClinicSecurityLogin(@RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                               @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                               @Valid @RequestBody ClinicSecurityLoginReq req) {
        ClinicSecurityLoginView rsp = organService.saveClinicSecurityLogin(clinicId, employeeId, req);
        return new AbcServiceResponse<>(rsp);
    }

    @ApiModelProperty(value = "查询门店安全登录设置")
    @GetMapping("/security-login")
    public AbcServiceResponse<ClinicSecurityLoginView> getClinicSecurityLogin(@RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                              @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) {
        ClinicSecurityLoginView rsp = organService.getClinicSecurityLogin(chainId, clinicId);
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 查询人员是否在安全登录限制名单
     */
    @ApiOperation(value = "查询人员是否在安全登录限制名单", produces = "application/json")
    @GetMapping("/security-login/hit")
    public AbcServiceResponse<QuerySecurityLoginHitRsp> hitSecurityLogin(@RequestParam String clinicId,
                                                                         @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId) {
        QuerySecurityLoginHitRsp rsp = organService.hitSecurityLogin(clinicId, employeeId);
        return new AbcServiceResponse<>(rsp);
    }

    @ApiOperation(value = "腾讯地图地址搜索", produces = "application/json")
    @ApiImplicitParams({
            @ApiImplicitParam(value = "搜索关键字", name = "keyword", required = true, paramType = "query", dataType = "string", dataTypeClass = String.class),
            @ApiImplicitParam(value = "城市名", name = "cityName", required = true, paramType = "query", dataType = "string", dataTypeClass = String.class),
            @ApiImplicitParam(value = "页码:如第一页、第二页", name = "pageIndex", required = true, paramType = "query", dataType = "int", dataTypeClass = Integer.class),
            @ApiImplicitParam(value = "每页数量：最大每页100条", name = "pageSize", required = true, paramType = "query", dataType = "int", dataTypeClass = Integer.class)
    })
    @GetMapping("/map/address-search")
    @LogReqAndRsp
    public AbcServiceResponse<QuerySuggestionRsp> queryMapSuggestion(@RequestParam String keyword,
                                                                     @RequestParam String cityName,
                                                                     @RequestParam int pageIndex,
                                                                     @RequestParam int pageSize) {
        return new AbcServiceResponse<>(lbsQQMapService.querySuggestion(keyword, cityName, pageIndex, pageSize, 1));
    }
    @GetMapping("/ticket/reply/{ticketId}")
    @ApiOperation("查询最新一条OA工单回复")
    public AbcServiceResponse<ClinicTicketReplyView> getReply(@PathVariable("ticketId") String ticketId) {
        return new AbcServiceResponse<>(clinicService.getClinicTicketReply(ticketId));
    }

    /**
     * 根据行政区划名称查询行政区划
     */
    @GetMapping("/region/by-name")
    @LogReqAndRsp
    public AbcServiceResponse<RegionDetailView> getRegionsByName(@RequestParam String regionName) {
        RegionDetailView regions = addressRegionService.getRegionsByName(regionName);
        return new AbcServiceResponse<>(regions);
    }

    /**
     * 获取连锁客服群配置
     */
    @GetMapping("/chat-config")
    @ApiOperation(value = "获取连锁客服群配置", produces = "application/json")
    public AbcServiceResponse<CsChatConfigAbstract> getChatConfigByChainId(@RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                           @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) {
        CsChatConfigAbstract chatConfig = oaCsFeignClient.getChatConfigByChainId(chainId, clinicId);
        return new AbcServiceResponse<>(chatConfig);
    }


    /**
     * 更新门店配置信息
     */
    @PutMapping("/config")
    @ApiOperation(value = "更新门店配置信息", produces = "application/json")
    public AbcServiceResponse<UpdateOrganConfigRsp> updateClinicConfig(@RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                       @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                       @RequestBody UpdateOrganConfigReq req) {
        return new AbcServiceResponse<>(organService.updateClinicConfig(clinicId, employeeId, req));
    }
}
