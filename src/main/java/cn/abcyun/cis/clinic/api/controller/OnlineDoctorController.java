package cn.abcyun.cis.clinic.api.controller;

import cn.abcyun.cis.clinic.api.view.*;
import cn.abcyun.cis.clinic.service.OnlineDoctorService;
import cn.abcyun.cis.commons.jwt.CisJWTUtils;
import cn.abcyun.common.model.AbcListPage;
import cn.abcyun.common.model.AbcServiceResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api/v3/clinics/online-doctors")
@Api(value = "网诊医生前端接口", produces = "application/json")
public class OnlineDoctorController {

    private final OnlineDoctorService onlineDoctorService;

    public OnlineDoctorController(OnlineDoctorService onlineDoctorService) {
        this.onlineDoctorService = onlineDoctorService;
    }

    /**
     * 获取网诊医生 是否在线配置
     *
     * @param employeeId
     * @param chainId
     * @param clinicId
     * @return
     */
    @GetMapping("/status")
    @ApiOperation(value = "获取网诊医生 是否在线配置", produces = "application/json")
    public AbcServiceResponse<OnlineDoctorStatusView> getDoctorOnlineStatusView(@RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                                @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID, required = false) String chainId,
                                                                                @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID, required = false) String clinicId,
                                                                                @RequestParam(value = "doctorId", required = false) String doctorId) {
        employeeId = StringUtils.hasText(doctorId) ? doctorId : employeeId;
        OnlineDoctorStatusView onlineStatusView = onlineDoctorService.getDoctorOnlineStatusView(chainId, clinicId, employeeId);
        return new AbcServiceResponse<>(onlineStatusView);
    }

    /**
     * 更新 网诊医生 自动上/下线配置
     *
     * @param employeeId
     * @param chainId
     * @param clinicId
     * @return
     */
    @PutMapping("/auto/config")
    @ApiOperation(value = "更新 网诊医生 自动上/下线配置", produces = "application/json")
    public AbcServiceResponse<OnlineDoctorStatusView> updateDoctorAutoOnlineConfig(@RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                                   @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID, required = false) String chainId,
                                                                                   @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID, required = false) String clinicId,
                                                                                   @RequestBody DoctorAutoOnlineConfigReq req) {
        OnlineDoctorStatusView onlineStatusView = onlineDoctorService.updateDoctorAutoOnlineConfig(chainId, employeeId, req);
        return new AbcServiceResponse<>(onlineStatusView);
    }

    /**
     * 更新 网诊医生 上/下线状态
     *
     * @param employeeId
     * @param chainId
     * @param clinicId
     * @return
     */
    @PutMapping("/status")
    @ApiOperation(value = "更新 网诊医生 上/下线状态", produces = "application/json")
    public AbcServiceResponse<OnlineDoctorStatusView> updateDoctorOnlineStatus(@RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                               @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID, required = false) String chainId,
                                                                               @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID, required = false) String clinicId,
                                                                               @RequestBody DoctorOnlineStatusReq req) {
        OnlineDoctorStatusView onlineStatusView = onlineDoctorService.updateDoctorOnlineStatus(chainId, employeeId, req);
        return new AbcServiceResponse<>(onlineStatusView);
    }

    /**
     * 修改是否使用电子签名开具处方配置
     */
    @PutMapping("/prescription-use-ca")
    @ApiOperation(value = "修改是否使用电子签名开具处方配置", produces = "application/json")
    public AbcServiceResponse<OnlineDoctorStatusView> updateDoctorPrescriptionUseCa(@RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                                    @RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                    @RequestBody DoctorPrescriptionUseCaReq req) {
        OnlineDoctorStatusView onlineStatusView = onlineDoctorService.updateDoctorPrescriptionUseCa(chainId, employeeId, req);
        return new AbcServiceResponse<>(onlineStatusView);
    }

    @PostMapping("/add")
    @ApiOperation(value = "批量保存网诊医生", produces = "application/json")
    public AbcServiceResponse<OnlineDoctorSaveRsp> batchSaveOnlineDoctor(@RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                         @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                         @RequestBody OnlineDoctorSaveReq req) {
        OnlineDoctorSaveRsp rsp = onlineDoctorService.batchSaveOnlineDoctor(chainId, employeeId, req);
        return new AbcServiceResponse<>(rsp);
    }

    @PostMapping()
    @ApiOperation(value = "新增或修改网诊医生信息", produces = "application/json")
    public AbcServiceResponse<OnlineDoctorView> addOrUpdateOnlineDoctor(@RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                        @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                        @Valid @RequestBody OnlineDoctorUpdateReq req) {
        OnlineDoctorView rsp = onlineDoctorService.addOrUpdateOnlineDoctor(chainId, employeeId, req);
        return new AbcServiceResponse<>(rsp);
    }

    @GetMapping()
    @ApiOperation(value = "网诊医生列表", produces = "application/json")
    public AbcServiceResponse<OnlineDoctorPageView> listOnlineDoctors(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                      @RequestParam(required = false, defaultValue = "0") Integer offset,
                                                                      @RequestParam(required = false, defaultValue = "10") Integer limit,
                                                                      @RequestParam(required = false) String keyword,
                                                                      @RequestParam(required = false) String orgId,
                                                                      @RequestParam(required = false) Integer viewPermission,
                                                                      @RequestParam(required = false) Integer webConsultPermission) {
        OnlineDoctorPageView result = onlineDoctorService.listOnlineDoctors(chainId, offset, limit, keyword,orgId,viewPermission,webConsultPermission);
        return new AbcServiceResponse<>(result);
    }

    @GetMapping("list-by-chain")
    @ApiOperation(value = "连锁所有网诊医生列表", produces = "application/json")
    public AbcServiceResponse<AbcListPage<OnlineDoctorView>> listOnlineDoctorsByChain(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId) {
        List<OnlineDoctorView> rows = onlineDoctorService.listOnlineDoctorsByChain(chainId);
        AbcListPage<OnlineDoctorView> rsp = new AbcListPage<>();
        rsp.setRows(rows);
        return new AbcServiceResponse<>(rsp);
    }

    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除网诊医生", produces = "application/json")
    public AbcServiceResponse<String> deleteOnlineDoctor(@PathVariable String id,
                                                         @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                         @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId) {
        onlineDoctorService.deleteOnlineDoctor(id, chainId, employeeId);
        return new AbcServiceResponse<>(id);
    }

    @GetMapping("/default")
    @ApiOperation(value = "查询连锁的默认网诊信息", produces = "application/json")
    public AbcServiceResponse<OnlineDoctorPageView.DefaultSet> queryDefaultSet(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId) {
        return new AbcServiceResponse<>(onlineDoctorService.getDefaultSet(chainId));
    }

    @PutMapping("/default")
    @ApiOperation(value = "设置默认网诊门店和网诊费用", produces = "application/json")
    public AbcServiceResponse<OnlineDoctorSettingRsp> updateOnlineDoctorDefaultSet(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                   @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                                   @Valid @RequestBody OnlineDoctorSettingReq req) {
        OnlineDoctorSettingRsp rsp = onlineDoctorService.updateOnlineDoctorDefaultSet(chainId, employeeId, req);
        return new AbcServiceResponse<>(rsp);
    }
}
