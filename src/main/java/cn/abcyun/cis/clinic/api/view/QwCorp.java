package cn.abcyun.cis.clinic.api.view;

import cn.abcyun.scrm.rpc.sdk.rpc.model.corp.CorpView;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/9/27 18:10
 */
@ApiModel(value = "QwCorp")
@Data
public class QwCorp {
    @ApiModelProperty(value = "企业微信id")
    private String corpId;
    @ApiModelProperty(value = "企业微信名称")
    private String name;
    @ApiModelProperty(value = "scrm应用id")
    private int agentId;

    public static QwCorp from(CorpView corp) {
        if (corp == null) {
            return null;
        }
        QwCorp qwCorp = new QwCorp();
        qwCorp.setCorpId(corp.getQwCorpId());
        qwCorp.setName(corp.getName());
        qwCorp.setAgentId(corp.getScrmAgentId());
        return qwCorp;
    }
}
