package cn.abcyun.cis.clinic.api.controller;

import cn.abcyun.cis.clinic.api.view.reminder.ClinicReminderReadReq;
import cn.abcyun.cis.clinic.api.view.reminder.ClinicReminderReadView;
import cn.abcyun.cis.clinic.model.ClinicReminderRead;
import cn.abcyun.cis.clinic.service.ClinicReminderReadService;
import cn.abcyun.cis.commons.jwt.CisJWTUtils;
import cn.abcyun.common.model.AbcServiceResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2023/6/28 17:15
 **/
@RestController
@RequestMapping("/api/v3/clinics/reminder")
@Api(value = "ClinicReminderReadController", tags = "提醒已读", produces = "application/json")
public class ClinicReminderReadController {

    @Autowired
    private ClinicReminderReadService clinicReminderReaderService;

    /**
     * 查询提醒是否已读
     *
     * @param scene      {@link ClinicReminderRead.Scene}
     * @param employeeId 指定某个成员已读，不传默认为当前登录人
     */
    @ApiOperation(value = "查询提醒是否已读", httpMethod = "GET")
    @GetMapping("/{scene}/read")
    public AbcServiceResponse<ClinicReminderReadView> getReminderRead(@PathVariable("scene") String scene,
                                                                      @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String operatorId,
                                                                      @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID, required = false) String chainId,
                                                                      @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID, required = false) String clinicId,
                                                                      @RequestParam(value = "employeeId", required = false) String employeeId) {
        employeeId = StringUtils.hasText(employeeId) ? employeeId : operatorId;
        ClinicReminderReadView reminderReadView = clinicReminderReaderService.getReminderRead(scene, employeeId, chainId, clinicId);
        return new AbcServiceResponse<>(reminderReadView);
    }

    /**
     * 标识提醒为已读
     *
     * @param scene {@link ClinicReminderRead.Scene}
     * @param req   {@link ClinicReminderReadReq}
     */
    @ApiOperation(value = "标识提醒为已读", httpMethod = "POST")
    @PostMapping("/{scene}/read")
    public AbcServiceResponse<ClinicReminderReadView> reminderRead(@PathVariable("scene") String scene,
                                                                   @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                   @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID, required = false) String chainId,
                                                                   @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID, required = false) String clinicId,
                                                                   @RequestBody(required = false) ClinicReminderReadReq req) {
        employeeId = (req != null && StringUtils.hasText(req.getEmployeeId())) ? req.getEmployeeId() : employeeId;
        ClinicReminderReadView reminderReadView = clinicReminderReaderService.markReminderRead(scene, employeeId, chainId, clinicId);
        return new AbcServiceResponse<>(reminderReadView);
    }


}
