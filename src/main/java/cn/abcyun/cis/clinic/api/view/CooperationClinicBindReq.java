package cn.abcyun.cis.clinic.api.view;

import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @date 2024/8/1 19:36
 */
@Data
public class CooperationClinicBindReq {

    @NotEmpty(message = "clinicId不能为空")
    private String clinicId;

    @NotEmpty(message = "coClinicId不能为空")
    private String coClinicId;

    /**
     * 0药诊互通
     * 1中心检查检验
     * {@link cn.abcyun.cis.clinic.model.ClinicCooperationClinic.Type}
     */
    private int type;

}
