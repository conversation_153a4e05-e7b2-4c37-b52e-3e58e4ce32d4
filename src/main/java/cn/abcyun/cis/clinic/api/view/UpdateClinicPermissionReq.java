package cn.abcyun.cis.clinic.api.view;

import cn.abcyun.bis.rpc.sdk.cis.model.clinic.ClinicConfig;
import cn.abcyun.cis.clinic.exception.ClinicError;
import cn.abcyun.cis.clinic.exception.ClinicException;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-01 20:27:41
 */
@Data
public class UpdateClinicPermissionReq {

    @ApiModelProperty(value = "新建药品档案")
    @NotNull(message = "新建药品档案配置 为空关闭")
    private ClinicDataPermissionInventory.GoodsArchivesConfig goodsCreateArchivesConfig;
    @ApiModelProperty(value = "编辑药品档案")
    @NotNull(message = "编辑药品档案配置 为空关闭")
    private ClinicDataPermissionInventory.GoodsArchivesConfig goodsModifyArchivesConfig;
    @ApiModelProperty(value = "删除药品档案")
    @NotNull(message = "删除药品档案配置 为空关闭")
    private ClinicDataPermissionInventory.GoodsArchivesConfig goodsDeleteArchivesConfig;

    @ApiModelProperty(value = "定价/调价")
    @NotNull(message = "定价/调价配置 为空关闭")
    private ClinicDataPermissionInventory.GoodsAdjustPriceConfig goodsAdjustPriceConfig;

    @ApiModelProperty("关闭子店定价-null 无意义  0 保留原来子店定价  1 删除子店定价")
    private Integer clearFlag;

}
