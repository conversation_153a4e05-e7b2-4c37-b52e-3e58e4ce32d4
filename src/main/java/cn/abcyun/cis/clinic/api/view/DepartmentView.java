package cn.abcyun.cis.clinic.api.view;

import cn.abcyun.cis.clinic.model.Department;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.beans.BeanUtils;

import java.time.Instant;

@EqualsAndHashCode(callSuper = true)
@Data
public class DepartmentView extends DepartmentBasicView{
    private String chainId;         // 连锁id
    private String clinicId;        // 诊所id
    private String secondMedical;   // 二级科目
    private String secondMedicalName;
    private String secondMedicalCode;
    private Instant startDate;      // 成立时间
    private String principal;       // 负责人
    private Instant created;        // 创建时间
    private int status;             // 状态（1：正常；99：已删除）
    private Instant lastModified;

    public static DepartmentView from(Department department) {
        if (department == null) {
            return null;
        }
        DepartmentView departmentView = new DepartmentView();
        BeanUtils.copyProperties(department, departmentView);
        return departmentView;
    }

    public static class Type {
        public static final int NORMAL_DEPARTMENT = 0;      // 普通科室
        public static final int OUTPATIENT_DEPARTMENT = 1;  // 门诊科室
    }
}
