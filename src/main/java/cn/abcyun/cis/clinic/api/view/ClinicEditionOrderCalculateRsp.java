package cn.abcyun.cis.clinic.api.view;

import cn.abcyun.bis.rpc.sdk.cis.model.clinic.ClinicConstants;
import cn.abcyun.cis.clinic.dto.ClinicEditionDTO;
import cn.abcyun.cis.clinic.dto.ClinicEditionPayOrderContext;
import cn.abcyun.cis.clinic.model.ClinicEditionMaxAdjustment;
import cn.abcyun.cis.clinic.model.ClinicEditionOrder;
import cn.abcyun.cis.clinic.model.Constants;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Data
public class ClinicEditionOrderCalculateRsp {
    @ApiModelProperty(value = "订单id")
    private String id;
    @ApiModelProperty(value = "订单类型（1: 首购；2: 续费；3: 升级；4: 降级）")
    private int type;                   // 订单类型（1: 首购；2: 续费；3: 升级）
    @ApiModelProperty(value = "购买店版本id")
    private String editionId;           // 购买店版本id
    @ApiModelProperty(value = "开始时间")
    private String beginDate;           // 生效开始时间
    @ApiModelProperty(value = "结束时间")
    private String endDate;             // 生效结束时间
    @ApiModelProperty(value = "最晚生效时间")
    private String maxBeginDate;        // 最晚生效时间
    @ApiModelProperty(value = "最晚结束时间")
    private String maxEndDate;          // 最晚结束时间
    @ApiModelProperty(value = "单位")
    private String unit;                // 单位（year）
    @ApiModelProperty(value = "单位定价")
    private BigDecimal unitPrice;       // 单位定价
    @ApiModelProperty(value = "单位数量")
    private int unitCount;              // 单位数量

    @ApiModelProperty("SOP阶段续费订单关联的版本订单")
    private List<QWClinicEditionOrderAbstract> sopRenewRelateEditionOrders;
    @ApiModelProperty(value = "版本费用详情")
    private OrderFeeInfo editionOrderFeeInfo;
    /**
     * 版本订单总费用
     */
    @ApiModelProperty("版本总费用")
    private BigDecimal editionOrderTotalPrice;

    @ApiModelProperty(value = "增购账号费用详情")
    private OrderFeeInfo accountOrderFeeInfo;
    @ApiModelProperty(value = "版本费用抵扣金额")
    private BigDecimal editionDeductionFee;
    @ApiModelProperty(value = "增购账号抵扣金额")
    private BigDecimal accountDeductionFee;

    @ApiModelProperty(value = "实施项抵扣金额")
    private BigDecimal supportDeductionFee;

    @ApiModelProperty(value = "增购药诊互通账号费用详情")
    private OrderFeeInfo cooperationAccountOrderFeeInfo;

    @ApiModelProperty(value = "总价")
    private BigDecimal totalPrice;      // 总价
    @ApiModelProperty(value = "折扣")
    private BigDecimal discountPrice;   // 折扣
    @ApiModelProperty(value = "抵扣费用")
    private BigDecimal deductionFee;    // 抵扣费用
    @ApiModelProperty(value = "立减金额")
    private BigDecimal liJianFee;       // 立减金额
    @ApiModelProperty(value = "预先支付的金额")
    private BigDecimal prePaidFee;
    @ApiModelProperty(value = "应收费用")
    private BigDecimal receivableFee;   // 应收费用 receivableFee = totalPrice + discountPrice + adjustmentPrice + deductionFee
    @ApiModelProperty(value = "最少应收金额")
    private BigDecimal minReceivableFee;// 最少应收金额 receivableFee = totalPrice + discountPrice + maxAdjustmentPrice + deductionFee
    @ApiModelProperty(value = "赠送天数")
    private long giftDays;
    @ApiModelProperty(value = "可以购买的版本id列表")
    private List<String> availableEditionIds;
    @ApiModelProperty(value = "可以使用的优惠券列表")
    private List<Promotion> availablePromotions;

    @ApiModelProperty(value = "版本基础账号数")
    private int basicEmployeeCount;

    @ApiModelProperty(value = "合作诊所账号数")
    private Integer basicCooperateClinicCount;

    @ApiModelProperty(value = "bpENtrance 需要诊所信息")
    private ClinicEditionOrderView.BindOrganView clinic;

    @ApiModelProperty(value = "是否可以按历史价格续费 2023年2月1日前购买的门店并且未使用一次原价续费机会")
    private int isCanReBuyWithHistoryPrice;

    private BigDecimal maxAdjustmentFee;

    private ClinicEditionMaxAdjustment maxAdjustment;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @ApiModelProperty(value = "最低升级年数：仅升级返回该字段")
    private Integer minUpgradeYears;

    @ApiModelProperty(value = "实施项目费用")
    private List<SupportItemCalculateRsp.SupportItem> supportItems;


    public static ClinicEditionOrderCalculateRsp from(ClinicEditionPayOrderContext payOrderContext, cn.abcyun.cis.clinic.model.Organ organ) {
        List<String> promotionIds = payOrderContext.getPromotionIds() != null ? payOrderContext.getPromotionIds() : new ArrayList<>();

        ClinicEditionOrderCalculateRsp rsp = new ClinicEditionOrderCalculateRsp();
        rsp.setId(payOrderContext.getId());
        rsp.setType(payOrderContext.getType());
        rsp.setEditionId(payOrderContext.getEditionId());
        rsp.setBeginDate(payOrderContext.getBeginDate());
        rsp.setEndDate(payOrderContext.getEndDate());
        rsp.setMaxBeginDate(payOrderContext.getMaxBeginDate());
        rsp.setMaxEndDate(payOrderContext.getMaxEndDate());
        rsp.setUnit(payOrderContext.getEditionOrderFeeInfo().getUnit());
        rsp.setUnitPrice(payOrderContext.getEditionOrderFeeInfo().getUnitPrice());
        rsp.setUnitCount(payOrderContext.getUnitCount());
        rsp.setEditionOrderTotalPrice(payOrderContext.getEditionOrderTotalPrice());
        rsp.setTotalPrice(payOrderContext.getTotalPrice());
        rsp.setDiscountPrice(payOrderContext.getDiscountPrice());
        rsp.setDeductionFee(payOrderContext.getDeductionFee());
        rsp.setLiJianFee(payOrderContext.getLiJianFee());
        rsp.setPrePaidFee(payOrderContext.getPrePaidFee());
        rsp.setAccountDeductionFee(payOrderContext.getAccountDeductionFee());
        rsp.setEditionDeductionFee(payOrderContext.getEditionDeductionFee());
        rsp.setSupportDeductionFee(payOrderContext.getSupportDeductionFee());
        rsp.setReceivableFee(payOrderContext.getReceivableFee());
        rsp.setMinReceivableFee(payOrderContext.getMinReceivableFee());
        rsp.setGiftDays(payOrderContext.getGiftDays());
        rsp.setBasicEmployeeCount(payOrderContext.getMaxEmployeeCount());
        rsp.setBasicCooperateClinicCount(payOrderContext.getMaxCooperateClinicCount());
        rsp.setClinic(ClinicEditionOrderView.BindOrganView.from(organ));
        if (payOrderContext.getAvailablePromotionObtainedList() != null) {
            rsp.setAvailablePromotions(payOrderContext.getAvailablePromotionObtainedList().stream().map(promotion -> {
                ClinicEditionOrderCalculateRsp.Promotion rspPromotion = new ClinicEditionOrderCalculateRsp.Promotion();
                BeanUtils.copyProperties(promotion, rspPromotion);
                rspPromotion.setIsUsed(promotionIds.contains(rspPromotion.getId()) ? 1 : 0);
                return rspPromotion;
            }).collect(Collectors.toList()));
        } else {
            rsp.setAvailablePromotions(new ArrayList<>());
        }

        if (payOrderContext.getEditionOrderFeeInfo() != null) {
            OrderFeeInfo editionOrderFeeInfo = new OrderFeeInfo();
            editionOrderFeeInfo.setTotalPrice(payOrderContext.getEditionOrderFeeInfo().getTotalPrice());
            editionOrderFeeInfo.setUnitPrice(payOrderContext.getEditionOrderFeeInfo().getUnitPrice());
            editionOrderFeeInfo.setCount(payOrderContext.getEditionOrderFeeInfo().getCount());
            editionOrderFeeInfo.setYears(payOrderContext.getEditionOrderFeeInfo().getYears());
            editionOrderFeeInfo.setUnitPriceCurrent(payOrderContext.getEditionOrderFeeInfo().getUnitPriceCurrent());
            editionOrderFeeInfo.setTotalPriceCurrent(payOrderContext.getEditionOrderFeeInfo().getTotalPriceCurrent());
            rsp.setEditionOrderFeeInfo(editionOrderFeeInfo);
        }

        if (payOrderContext.getCooperationAccountOrderFeeInfo() != null) {
            OrderFeeInfo cooperationAccountOrderFeeInfo = new OrderFeeInfo();
            cooperationAccountOrderFeeInfo.setTotalPrice(payOrderContext.getCooperationAccountOrderFeeInfo().getTotalPrice());
            cooperationAccountOrderFeeInfo.setUnitPrice(payOrderContext.getCooperationAccountOrderFeeInfo().getUnitPrice());
            cooperationAccountOrderFeeInfo.setCount(payOrderContext.getCooperationAccountOrderFeeInfo().getCount());
            cooperationAccountOrderFeeInfo.setYears(payOrderContext.getCooperationAccountOrderFeeInfo().getYears());
            cooperationAccountOrderFeeInfo.setUnitPriceCurrent(payOrderContext.getCooperationAccountOrderFeeInfo().getUnitPriceCurrent());
            cooperationAccountOrderFeeInfo.setTotalPriceCurrent(payOrderContext.getCooperationAccountOrderFeeInfo().getTotalPriceCurrent());
            rsp.setCooperationAccountOrderFeeInfo(cooperationAccountOrderFeeInfo);
        }

        if (payOrderContext.getAccountOrderFeeInfo() != null) {
            OrderFeeInfo accountOrderFeeInfo = new OrderFeeInfo();
            accountOrderFeeInfo.setTotalPrice(payOrderContext.getAccountOrderFeeInfo().getTotalPrice());
            accountOrderFeeInfo.setUnitPrice(payOrderContext.getAccountOrderFeeInfo().getUnitPrice());
            accountOrderFeeInfo.setCount(payOrderContext.getAccountOrderFeeInfo().getCount());
            accountOrderFeeInfo.setYears(payOrderContext.getAccountOrderFeeInfo().getYears());
            accountOrderFeeInfo.setUnitPriceCurrent(payOrderContext.getAccountOrderFeeInfo().getUnitPriceCurrent());
            accountOrderFeeInfo.setTotalPriceCurrent(payOrderContext.getAccountOrderFeeInfo().getTotalPriceCurrent());
            rsp.setAccountOrderFeeInfo(accountOrderFeeInfo);
        }

        if (!CollectionUtils.isEmpty(payOrderContext.getSopRenewRelateClinicEditionOrders())) {
            rsp.setSopRenewRelateEditionOrders(payOrderContext.getSopRenewRelateClinicEditionOrderAbstracts());
        }

        /**
         * 保护下，避免配置文件里面没配置版本导致的空指针
         * */
        if (payOrderContext.getAvailableEditions() != null) {
            rsp.setAvailableEditionIds(payOrderContext.getAvailableEditions().stream().map(ClinicEditionDTO::getId).collect(Collectors.toList()));
        }
        rsp.setMaxAdjustmentFee(payOrderContext.getMaxAdjustmentFee());
        rsp.setIsCanReBuyWithHistoryPrice(payOrderContext.getIsCanReBuyWithHistoryPrice());
        rsp.setMaxAdjustment(payOrderContext.getMaxAdjustment());

        if (payOrderContext.getType() == ClinicEditionOrder.Type.UPGRADE_PURCHASE) {
            long leftDays = Duration.between(Instant.now(), payOrderContext.getClinicCurrentEdition().getEndDate()).toDays() + 1;
            int minUpgradeYears = Math.toIntExact((leftDays % 365 == 0) ? (leftDays / 365) : (leftDays / 365 + 1));
            if (String.valueOf(Constants.ClinicEditionId.CLOUD_EXAM).equals(payOrderContext.getEditionId())) {
                // 云检版本, 最低升级年限可以为1年，因为其是免费永久的，升级为付费的可以只升级1年
                minUpgradeYears = 1;
            }
            rsp.setMinUpgradeYears(minUpgradeYears);
        }

        if (!CollectionUtils.isEmpty(payOrderContext.getSupportOrderFeeInfos())) {
            rsp.setSupportItems(payOrderContext.getSupportOrderFeeInfos().stream().map(i -> {
                SupportItemCalculateRsp.SupportItem item = new SupportItemCalculateRsp.SupportItem();
                item.setKey(i.getKey());
                item.setTotalPrice(i.getTotalPrice());
                return item;
            }).collect(Collectors.toList()));
        }

        return rsp;
    }

    @Data
    @ApiModel(value = "ClinicEditionOrderCalculateRsp.Promotion")
    public static class Promotion {
        private String id;
        private String name;
        private BigDecimal amount;
        private int isUsed;
        private Instant expiredTime;
    }

    @Data
    @ApiModel(value = "ClinicEditionOrderCalculateRsp.OrderFeeInfo")
    public static class OrderFeeInfo {
        private int years;                  // 年数
        private int count;                  // 数量
        @ApiModelProperty(value = "实际收款的单价：1799")
        private BigDecimal unitPrice;
        @ApiModelProperty(value = "实际收款的总价：1799*1")
        private BigDecimal totalPrice;

        //------为pc基础版调价显示------
        @ApiModelProperty(value = "当前单位定价/最新定价eg：2599")
        private BigDecimal unitPriceCurrent;
        @ApiModelProperty(value = "按最新定价计算出的总价eg：2599*1")
        private BigDecimal totalPriceCurrent;
        //------为pc基础版调价显示------
    }

}
