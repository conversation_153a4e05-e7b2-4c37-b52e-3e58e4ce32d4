package cn.abcyun.cis.clinic.api.controller;

import cn.abcyun.bis.rpc.sdk.cis.model.clinic.ClinicConstants;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.OpsCommonRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.message.SmsSendRsp;
import cn.abcyun.bis.rpc.sdk.oa.model.QWCopLoginByCodeReq;
import cn.abcyun.bis.rpc.sdk.oa.model.QWCopLoginByCodeRsp;
import cn.abcyun.cis.clinic.api.view.*;
import cn.abcyun.cis.clinic.api.view.login.*;
import cn.abcyun.cis.clinic.enums.EmployeeActionEnum;
import cn.abcyun.cis.clinic.model.ClinicRole;
import cn.abcyun.cis.clinic.model.Constants;
import cn.abcyun.cis.clinic.model.Employee;
import cn.abcyun.cis.clinic.service.*;
import cn.abcyun.cis.clinic.service.factory.EmployeeBusinessAuthServiceFactory;
import cn.abcyun.cis.commons.jwt.CisJWTUtils;
import cn.abcyun.cis.core.aop.LogReqAndRsp;
import cn.abcyun.common.model.AbcListPage;
import cn.abcyun.common.model.AbcServiceResponse;
import cn.abcyun.scrm.rpc.sdk.rpc.model.corp.CorpUserView;
import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/v3/clinics/employees")
@Api(value = "EmployeeController", description = "诊所雇员接口", produces = "application/json")
@Slf4j
public class EmployeeController {
    @Autowired
    private ClinicEditionService clinicEditionService;
    @Autowired
    private EmployeeService employeeService;
    @Autowired
    private SmsService smsService;
    @Autowired
    private ClinicVirtualPhoneNumberService clinicVirtualPhoneNumberService;
    @Autowired
    private EmployeeShebaoService employeeShebaoService;

    /***
     * @param purchaseItemName 查询加入门店 加一个按支持版本功能返回加入门店功能  为null表示不按功能查
     * */
    @GetMapping("/joined")
    @ApiOperation(value = "查询加入的门店", produces = "application/json")
    public AbcServiceResponse<EmployeeJoinedClinicListView> getEmployeeJoinedClinicList(@RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                                        @RequestParam(required = false) String purchaseItemName) {
        EmployeeJoinedClinicListView rsp = clinicEditionService.getEmployeeJoinedClinicList(employeeId, false, false, purchaseItemName, null);
        return new AbcServiceResponse<>(rsp);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(value = "true：只返回绑定企业微信的门店， false: 返回所有加入的门店", name = "filterBindQw", paramType = "query", dataType = "boolean", dataTypeClass = boolean.class, defaultValue = "false")
    })
    @ApiOperation(value = "SCRM 查询加入的门店", httpMethod = "GET")
    @GetMapping("/scrm/joined")
    public AbcServiceResponse<EmployeeJoinedClinicListView> getEmployeeJoinedScrmClinicList(@RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                                            @RequestParam(required = false, defaultValue = "false") boolean filterBindQw,
                                                                                            @RequestParam(required = false) String qwCorpId) {
        EmployeeJoinedClinicListView rsp = clinicEditionService.getEmployeeJoinedClinicList(employeeId, false, filterBindQw, null, qwCorpId);
        return new AbcServiceResponse<>(rsp);
    }


    @GetMapping("/chain-doctors")
    @ApiOperation(value = "查询连锁下的医生", produces = "application/json")
    public AbcServiceResponse<AbcListPage<EmployeeView>> getDoctorsByChain(@RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId) {
        List<EmployeeView> doctors = employeeService.queryChainDoctors(chainId);
        AbcListPage<EmployeeView> rspList = new AbcListPage<>();
        rspList.setRows(doctors);
        return new AbcServiceResponse<>(rspList);
    }

    @GetMapping("/clinic-doctors")
    @ApiOperation(value = "查询诊所下的医生", produces = "application/json")
    public AbcServiceResponse<AbcListPage<EmployeeView>> listDoctorsByClinic(@RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String headerClinicId,
                                                                             @RequestParam(required = false) String clinicId) {

        clinicId = StringUtils.isEmpty(clinicId) ? headerClinicId : clinicId;
        List<EmployeeView> doctors = employeeService.listDoctorsByClinic(clinicId);
        AbcListPage<EmployeeView> rspList = new AbcListPage<>();
        rspList.setRows(doctors);
        return new AbcServiceResponse<>(rspList);
    }

    @GetMapping("/chain-employees")
    @ApiOperation(value = "查询连锁下的全部雇员", produces = "application/json")
    public AbcServiceResponse<AbcListPage<ChainEmployeeView>> queryChainEmployeeList(@RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                     @RequestParam int withDisable) {
        List<ChainEmployeeView> employeeViews = employeeService.queryChainEmployees(chainId, withDisable);
        AbcListPage<ChainEmployeeView> rspList = new AbcListPage<>();
        rspList.setRows(employeeViews);
        return new AbcServiceResponse<>(rspList);
    }


    @GetMapping("/clinic-employees")
    @ApiOperation(value = "查询诊所下的全部雇员", produces = "application/json")
    public AbcServiceResponse<AbcListPage<ClinicEmployeeView>> getClinicEmployees(@RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                  @RequestParam(value = "isDoctor", required = false) @ApiParam(value = "选择过滤只要医生") Integer isDoctor) {
        List<ClinicEmployeeView> clinicEmployees = employeeService.queryClinicEmployees(clinicId);
        if (isDoctor != null) {
            clinicEmployees = clinicEmployees.stream()
                    .filter(clinicEmployeeView -> clinicEmployeeView.getClinicInfo() != null && clinicEmployeeView.getClinicInfo().getIsDoctor() == isDoctor)
                    .collect(Collectors.toList());
        }
        AbcListPage<ClinicEmployeeView> rspList = new AbcListPage<>();
        rspList.setRows(clinicEmployees);
        return new AbcServiceResponse<>(rspList);
    }

    /**
     * Corresponding to the AbcCisClinic service method findEmployeeById(ctx) in controller/employee.js
     *
     * @param id 雇员ID
     * @return AbcServiceResponse<EmployeeComposeView> 雇员个人信息合集：包括诊所、连锁基本信息以及权限、角色、标签等等
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "查询雇员信息", produces = "application/json")
    public AbcServiceResponse<EmployeeComposeView> getEmployeeComposeInfoById(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                              @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                              @PathVariable("id") String id) {
        EmployeeComposeView employeeComposeView = employeeService.getEmployeeComposeInfoById(chainId, clinicId, false, id, true, true);
        return new AbcServiceResponse<>(employeeComposeView);
    }

    @GetMapping("/me")
    @ApiOperation(value = "查询登录者信息", produces = "application/json")
    public AbcServiceResponse<EmployeeComposeView> getCurrentEmployee(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID, required = false) String chainId,
                                                                      @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID, required = false) String clinicId,
                                                                      @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId) {
        return new AbcServiceResponse<>(employeeService.getEmployeeComposeInfoById(chainId, clinicId, false, employeeId, true, false));
    }

    @GetMapping("/pages")
    @ApiOperation(value = "分页查询雇员列表", produces = "application/json")
    public AbcServiceResponse<AbcListPage<EmployeePageView>> listClinicEmployees(
            @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestParam(value = "queryClinicId", required = false) String queryClinicId,
            @RequestParam(value = "keyword", required = false) String keyword,
            @RequestParam(value = "role", required = false) Integer role,
            @RequestParam(value = "showDisable", required = false, defaultValue = "0") int showDisable,
            @RequestParam(value = "offset", defaultValue = "0") int offset,
            @RequestParam(value = "limit", defaultValue = "20") int limit) {
        clinicId = org.apache.commons.lang3.StringUtils.defaultIfBlank(queryClinicId, clinicId);
        return new AbcServiceResponse<>(employeeService.listClinicEmployees(chainId, clinicId, keyword, role, showDisable, offset, limit));
    }

    @GetMapping("/summary")
    @ApiOperation(value = "查询雇员概要信息", produces = "application/json")
    @LogReqAndRsp
    public AbcServiceResponse<EmployeeSummaryInfoView> getEmployeeSummary(@RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                          @RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                          @RequestParam(value = "keyword", required = false) String keyword,
                                                                          @RequestParam(value = "roleId", required = false) Integer roleId,
                                                                          @RequestParam(value = "showDisable", required = false, defaultValue = "0") int showDisable) {
        return new AbcServiceResponse<>(employeeService.getEmployeeSummary(chainId, clinicId, keyword, roleId, showDisable));
    }

    /**
     * 配置太多了，怕遗漏，没使用结构体
     */
    @PutMapping("/config")
    @ApiOperation(value = "更新员工诊所配置", produces = "application/json")
    public AbcServiceResponse<JsonNode> updateClinicEmployeeConfig(
            @RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
            @RequestBody JsonNode config) {

        return new AbcServiceResponse<>(employeeService.updateClinicEmployeeConfig(chainId, clinicId, employeeId, config));
    }

    @PutMapping("/config/electronic-sig-commit")
    @ApiOperation(value = "更新电子签名承诺书，后台这个签名字段存到了员工门店信息的config里面了", produces = "application/json")
    public AbcServiceResponse<OpsCommonRsp> updateClinicEmployeeElectronicSignatureCommitment(
            @RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
            @RequestBody ElectronicSignatureCommitmentReq req) {

        return new AbcServiceResponse<>(employeeService.updateClinicEmployeeElectronicSignatureCommitment(chainId, clinicId, employeeId, req.getElectronicSignatureCommitmentUrl()));
    }

    @GetMapping("/login-logs")
    @ApiOperation(value = "查询员工登录诊所日志", produces = "application/json")
    public AbcServiceResponse<AbcListPage<EmployeeClinicLoginLog>> listClinicLoginLog(
            @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
            @RequestParam(required = false, defaultValue = "0") @ApiParam(value = "非必填， 是否按连锁总部排序") Integer chainFirst,
            @RequestParam(required = false, defaultValue = "3") @ApiParam(value = "非必填，获取登录历史条数，默认3条") Integer size) {

        List<EmployeeClinicLoginLog> rows = employeeService.listClinicLoginLog(employeeId, chainFirst, size);

        AbcListPage<EmployeeClinicLoginLog> result = new AbcListPage<>();
        result.setRows(rows);
        return new AbcServiceResponse<>(result);
    }

    @GetMapping("/list-by-clinic")
    @ApiOperation(value = "查询门店员工列表", produces = "application/json")
    public AbcServiceResponse<AbcListPage<EmployeeView>> listClinicEmployees(@RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                             @RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                             @RequestParam(required = false) @ApiParam(value = "门店id") String clinicid,
                                                                             @RequestParam(required = false) @ApiParam(value = "模糊查询关键字") String keyword,
                                                                             @RequestParam(required = false, defaultValue = "0") @ApiParam(value = "非必传 不填的时候返回诊所所有人， 填1的时候，只返回医生") Integer isDoctor,
                                                                             @RequestParam(required = false, defaultValue = "0") @ApiParam(value = "非必传 不填的时候返回诊所所有人， 填1的时候，只返微诊所可见的人") Integer showInWeClinic,
                                                                             @RequestParam(required = false, defaultValue = "0") int showDisable,
                                                                             @RequestParam(required = false, defaultValue = "0") int isNeedRoles) {
        if (!StringUtils.isEmpty(clinicid)) {
            clinicId = clinicid;
        }
        List<EmployeeView> viewList = employeeService.listClinicEmployees(clinicId, isDoctor, showInWeClinic, keyword, showDisable, isNeedRoles, chainId);
        AbcListPage<EmployeeView> result = new AbcListPage<>();
        result.setRows(viewList);
        return new AbcServiceResponse<>(result);
    }

    @GetMapping("/list-by-chain")
    @ApiOperation(value = "查询连锁员工列表", produces = "application/json")
    public AbcServiceResponse<AbcListPage<EmployeeView>> listChainEmployees(@RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                            @RequestParam(required = false) @ApiParam(value = "门店id") String chainid,
                                                                            @RequestParam(required = false) @ApiParam(value = "模糊查询关键字") String keyword,
                                                                            @RequestParam(required = false, defaultValue = "0") @ApiParam(value = "非必传 不填的时候返回诊所所有人， 填1的时候，只返回医生") Integer isDoctor,
                                                                            @RequestParam(required = false, defaultValue = "0") @ApiParam(value = "非必传 不填的时候返回诊所所有人， 填1的时候，只返微诊所可见的人") Integer showInWeClinic) {
        if (!StringUtils.isEmpty(chainid)) {
            chainId = chainid;
        }
        List<EmployeeView> viewList = employeeService.listChainEmployees(chainId, isDoctor, showInWeClinic, keyword);
        AbcListPage<EmployeeView> result = new AbcListPage<>();
        result.setRows(viewList);
        return new AbcServiceResponse<>(result);
    }

    @GetMapping("/could-import")
    @ApiOperation(value = "查询能导入的员工列表", produces = "application/json")
    public AbcServiceResponse<AbcListPage<EmployeeImportView>> listEmployeesCouldImport(@RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                        @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) {
        List<EmployeeImportView> viewList = employeeService.listEmployeesCouldImport(chainId, clinicId);
        AbcListPage<EmployeeImportView> result = new AbcListPage<>();
        result.setRows(viewList);
        return new AbcServiceResponse<>(result);
    }

    @GetMapping("/doctors-with-department")
    @ApiOperation(value = "查询门店员工列表包含部门信息", produces = "application/json")
    public AbcServiceResponse<AbcListPage<EmployeeWithDepartmentView>> listDoctorsWithDepartment(@RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                                 @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                                                 @RequestParam(required = false, defaultValue = "0") Integer allClinical,
                                                                                                 @RequestParam(required = false, defaultValue = "0") Integer isChildcare,
                                                                                                 @RequestParam(required = false, defaultValue = "0") Integer isAssistant,
                                                                                                 @RequestParam(required = false, defaultValue = "0") Integer allEmployee,
                                                                                                 @RequestParam(required = false, defaultValue = "0") Integer isInspectionAndExamination,
                                                                                                 @RequestParam(required = false, defaultValue = "0") Integer allDepartment,
                                                                                                 @RequestParam(required = false) Integer businessScope) {
        List<EmployeeWithDepartmentView> viewList = employeeService.listDoctorsWithDepartment(clinicId, allClinical, isChildcare, isAssistant, employeeId, allEmployee, isInspectionAndExamination, allDepartment, businessScope);
        AbcListPage<EmployeeWithDepartmentView> result = new AbcListPage<>();
        result.setRows(viewList);
        return new AbcServiceResponse<>(result);
    }

    @GetMapping("/{id}/doctor-with-department")
    @ApiOperation(value = "查询门店员工部门信息", produces = "application/json")
    public AbcServiceResponse<AbcListPage<EmployeeWithDepartmentView>> getDoctorWithDepartment(@RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                               @RequestParam(required = false, defaultValue = "0") Integer allClinical,
                                                                                               @RequestParam(required = false, defaultValue = "0") Integer isChildcare,
                                                                                               @RequestParam(required = false, defaultValue = "0") Integer allEmployee,
                                                                                               @RequestParam(required = false, defaultValue = "0") Integer isInspectionAndExamination,
                                                                                               @RequestParam(required = false) Integer businessScope,
                                                                                               @PathVariable String id) {
        List<EmployeeWithDepartmentView> viewList = employeeService.getDoctorWithDepartment(clinicId, isChildcare, id, allClinical, allEmployee, isInspectionAndExamination, businessScope);
        AbcListPage<EmployeeWithDepartmentView> result = new AbcListPage<>();
        result.setRows(viewList);
        return new AbcServiceResponse<>(result);
    }

    @PostMapping("/chain-import")
    @ApiOperation(value = "导入成员", produces = "application/json")
    public AbcServiceResponse<Integer> importEmployee(@RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                      @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                      @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                      @RequestBody @Valid ImportEmployeeReq req) {
        employeeService.importEmployee(chainId, clinicId, employeeId, req);
        return new AbcServiceResponse<>(HttpStatus.OK.value());
    }

    @PutMapping("/delete-hand-sign")
    @ApiOperation(value = "删除员工手写签名", produces = "application/json")
    public AbcServiceResponse<Integer> deleteHandSign(@RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                      @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId) {
        employeeService.deleteHandSign(employeeId, chainId);
        return new AbcServiceResponse<>(HttpStatus.OK.value());
    }

    @PutMapping("/set-theme")
    @ApiOperation(value = "设置主题", produces = "application/json")
    public AbcServiceResponse<SetThemeConfigRsp> setTheme(@RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                          @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                          @RequestBody SetThemeConfigReq req
    ) {
        SetThemeConfigRsp rsp = employeeService.setTheme(employeeId, req, chainId);
        return new AbcServiceResponse<>(rsp);
    }

    @GetMapping("/get-assist-doctors")
    @ApiOperation(value = "获取助理医生", produces = "application/json")
    public AbcServiceResponse<QueryAssistDoctorRsp> getAssistDoctors(@RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                     @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId) {
        QueryAssistDoctorRsp rsp = employeeService.getAssistDoctors(clinicId, employeeId);
        return new AbcServiceResponse<>(rsp);
    }

    @GetMapping()
    @ApiOperation(value = "获取当前诊所成员列表", produces = "application/json")
    public AbcServiceResponse<AbcListPage<ClinicEmployeeListView>> listCurClinicEmployee(@RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                         @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                         @RequestParam(value = "queryClinicId", required = false) String queryClinicId,
                                                                                         @RequestParam(required = false) Integer isDoctor,
                                                                                         @RequestParam(required = false) String moduleId,
                                                                                         @RequestParam(required = false) Integer role,
                                                                                         @RequestParam(required = false, defaultValue = "0") int showDisable) {
        List<String> moduleIds = null;
        List<Integer> roles = null;
        if (!StringUtils.isEmpty(moduleId)) {
            moduleIds = new ArrayList<>(Arrays.asList(moduleId.split(Constants.SEPARATOR_COMMA)));
        }
        if (Objects.nonNull(role)) {
            roles = new ArrayList<>();
            roles.add(role);
        }
        clinicId = org.apache.commons.lang3.StringUtils.defaultIfBlank(queryClinicId, clinicId);
        List<ClinicEmployeeListView> rows = employeeService.listCurClinicEmployee(chainId, clinicId, isDoctor, 0, moduleIds, roles, showDisable);
        AbcListPage<ClinicEmployeeListView> result = new AbcListPage<>();
        result.setRows(rows);
        return new AbcServiceResponse<>(result);
    }

    @PostMapping("/list-by-condition")
    @ApiOperation(value = "按条件诊所成员列表", produces = "application/json")
    public AbcServiceResponse<AbcListPage<ClinicEmployeeListView>> listClinicEmployeeByCondition(@RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                                 @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                                 @RequestBody QueryClinicEmployeeReq req) {
        List<ClinicEmployeeListView> rows = employeeService.listCurClinicEmployee(chainId, clinicId, 0, 0, req.getModuleIds(), req.getRoles(), req.getPracticeTitles(), req.getPracticeTypes(), req.getShowDisable());
        AbcListPage<ClinicEmployeeListView> result = new AbcListPage<>();
        result.setRows(rows);
        return new AbcServiceResponse<>(result);
    }

    @PostMapping("/list-by-department")
    @LogReqAndRsp
    @ApiOperation(value = "按科室条件获取成员列表", produces = "application/json")
    public AbcServiceResponse<AbcListPage<EmployeeDepartmentInfo>> listEmployeeByDepartmentCondition(@RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                                     @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                                     @RequestBody QueryEmployeeByDepartmentReq req) {
        List<EmployeeDepartmentInfo> rows = employeeService.listEmployeeByDepartmentCondition(chainId, clinicId, req);
        AbcListPage<EmployeeDepartmentInfo> result = new AbcListPage<>();
        result.setRows(rows);
        return new AbcServiceResponse<>(result);
    }

    @GetMapping("/joined/{clinicId}")
    @ApiOperation(value = "获取员工所在某个门店的信息", produces = "application/json")
    public AbcServiceResponse<JoinedClinicView> getJoinedClinic(@PathVariable String clinicId,
                                                                @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId) {
        JoinedClinicView view = employeeService.getJoinedClinic(clinicId, employeeId);
        return new AbcServiceResponse<>(view);
    }

    @GetMapping("/joined/clinic-current")
    @ApiOperation(value = "获取员工所在当前门店的信息", produces = "application/json")
    public AbcServiceResponse<JoinedClinicView> getJoinedClinicCurrent(@RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                       @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId) {
        JoinedClinicView view = employeeService.getJoinedClinic(clinicId, employeeId);
        return new AbcServiceResponse<>(view);
    }

    @PostMapping("/invite")
    @ApiOperation(value = "新增成员", produces = "application/json")
    public AbcServiceResponse<EmployeeClinicInfo> insertClinicEmployee(@RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                       @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                       @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                       @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int clinicType,
                                                                       @RequestHeader(CisJWTUtils.CIS_HEADER_HIS_TYPE) int hisType,
                                                                       @RequestHeader(CisJWTUtils.CIS_HEADER_VIEW_MODE) int viewMode,
                                                                       @Valid @RequestBody CreateClinicEmployeeReq req) {
        req.setHisType(hisType);
        req.setViewMode(viewMode);
        req.setClinicType(clinicType);
        EmployeeClinicInfo rsp = employeeService.insertClinicEmployee(chainId, clinicId, employeeId, req, EmployeeActionEnum.CREATED_BY_SMS_INVITE);
        return new AbcServiceResponse<>(rsp);
    }

    @PutMapping("/{id}")
    @ApiOperation(value = "修改成员", produces = "application/json")
    @LogReqAndRsp
    public AbcServiceResponse<EmployeeClinicInfo> updateClinicEmployee(@RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                       @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                       @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                       @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int clinicType,
                                                                       @RequestHeader(CisJWTUtils.CIS_HEADER_HIS_TYPE) int hisType,
                                                                       @RequestHeader(CisJWTUtils.CIS_HEADER_VIEW_MODE) int viewMode,
                                                                       @Valid @RequestBody UpdateClinicEmployeeReq req,
                                                                       @PathVariable String id) {
        req.setHisType(hisType);
        req.setViewMode(viewMode);
        req.setClinicType(clinicType);
        EmployeeClinicInfo employeeClinicInfo = employeeService.updateClinicInfo(id, chainId, clinicId, employeeId, req, "修改成员信息", true);
        return new AbcServiceResponse<>(employeeClinicInfo);
    }

    @PutMapping("/{id}/status")
    @LogReqAndRsp
    @ApiOperation(value = "修改成员状态/停用启用", produces = "application/json")
    public AbcServiceResponse<EmployeeClinicInfo> updateClinicEmployeeStatus(@RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                             @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                             @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                             @Valid @RequestBody UpdateClinicEmployeeStatusReq req,
                                                                             @PathVariable String id) {
        EmployeeClinicInfo employeeClinicInfo = employeeService.updateClinicEmployeeStatus(id, chainId, clinicId, employeeId, req);
        return new AbcServiceResponse<>(employeeClinicInfo);
    }

    @DeleteMapping("/{id}")
    @ApiOperation(value = "从诊所移除成员", produces = "application/json")
    public AbcServiceResponse<RemoveClinicEmployeeRsp> removeEmployeeFromClinic(@RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                                @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int clinicType,
                                                                                @RequestHeader(CisJWTUtils.CIS_HEADER_HIS_TYPE) int hisType,
                                                                                @RequestHeader(CisJWTUtils.CIS_HEADER_VIEW_MODE) int viewMode,
                                                                                @PathVariable String id) {
        employeeService.removeEmployeeFromClinic(id, chainId, clinicId, employeeId, clinicType, hisType, viewMode);
        RemoveClinicEmployeeRsp rsp = new RemoveClinicEmployeeRsp();
        rsp.setEmployeeId(id);
        return new AbcServiceResponse<>(rsp);
    }

    @GetMapping("/audit-check")
    @ApiOperation(value = "mb审核成员检查", produces = "application/json")
    public AbcServiceResponse<EmployeeComposeView> auditEmployeeCheck(@RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID, required = false) String headerEmployeeId,
                                                                      @RequestParam String clinicId,
                                                                      @RequestParam String employeeId
    ) {
        EmployeeComposeView rsp = employeeService.queryEmployeeByClinicIdAndEmployeeId(clinicId, headerEmployeeId, employeeId);
        return new AbcServiceResponse<>(rsp);
    }

    @PostMapping("/audit")
    @ApiOperation(value = "手机端(mb)审核用户加入", produces = "application/json")
    public AbcServiceResponse<EmployeeClinicInfo> auditEmployeeJoin(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID, required = false) String clinicId,
                                                                    @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                    @Valid @RequestBody AuditClinicEmployeeReq req
    ) {
        EmployeeClinicInfo rsp = employeeService.auditEmployeeJoin(req, employeeId, clinicId);
        return new AbcServiceResponse<>(rsp);
    }

    @PostMapping("/sendsms")
    @ApiOperation(value = "发送短信", produces = "application/json")
    public AbcServiceResponse<SendSmsRsp> sendSms(@Valid @RequestBody SendSmsReq req,
                                                  @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID, required = false) String chainId,
                                                  @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID, required = false) String clinicId
    ) {
        SendSmsRsp rsp = smsService.sendSms(req, true, chainId, clinicId);
        return new AbcServiceResponse<>(rsp);
    }

    @PutMapping("/me")
    @LogReqAndRsp
    @ApiOperation(value = "修改当前登录人信息", produces = "application/json")
    public AbcServiceResponse<EmployeeView> updateCurEmployee(@RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                              @RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                              @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                              @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int clinicType,
                                                              @RequestHeader(CisJWTUtils.CIS_HEADER_HIS_TYPE) int hisType,
                                                              @RequestHeader(CisJWTUtils.CIS_HEADER_VIEW_MODE) int viewMode,
                                                              @Valid @RequestBody UpdateCurEmployeeReq req) {
        req.paramCheck();//参数检查
        req.setClinicType(clinicType);
        req.setHisType(hisType);
        req.setViewMode(viewMode);
        EmployeeView rsp = employeeService.updateCurEmployee(employeeId, req, chainId, clinicId, employeeId, true, "修改信息");
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 校验密码是否合法
     */
    @PostMapping("/check-password")
    @ApiOperation(value = "校验密码是否合法", produces = "application/json")
    @LogReqAndRsp
    public AbcServiceResponse<CheckEmployeePwdRsp> checkPassword(@RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                 @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                 @Valid @RequestBody CheckEmployeePwdReq req) {
        CheckEmployeePwdRsp rsp = employeeService.checkPassword(employeeId, req, chainId);
        return new AbcServiceResponse<>(rsp);
    }

    @PostMapping("/settings")
    @ApiOperation(value = "修改当前登录人员门店配置", produces = "application/json")
    public AbcServiceResponse<ClinicEmployeeSettingsView> upsertClinicEmployeeSettings(@RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                       @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                       @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                                       @RequestBody UpsertEmployeeSettingsReq req) {
        ClinicEmployeeSettingsView rsp = employeeService.upsertClinicEmployeeSettings(chainId, clinicId, employeeId, req);
        return new AbcServiceResponse<>(rsp);
    }

    @ApiOperation("获取企业微信通讯录列表")
    @GetMapping("/list-qw-user")
    public AbcServiceResponse<AbcListPage<CorpUserView>> getQwUsers(@RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                    @RequestParam("offset") int offset,
                                                                    @RequestParam("limit") int limit,
                                                                    @RequestParam(value = "keyword", required = false) String keyword
    ) {
        AbcListPage<CorpUserView> rsp = employeeService.getQwUsers(clinicId, offset, limit, keyword);
        return new AbcServiceResponse<>(rsp);
    }

    @ApiOperation("获取scrm应用版本信息")
    @GetMapping("/scrm/current-edition")
    public AbcServiceResponse<ScrmCurrentEditionView> getScrmCurrentEdition(@RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                            @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) {
        ScrmCurrentEditionView rsp = employeeService.getScrmCurrentEdition(chainId);
        return new AbcServiceResponse<>(rsp);
    }


    @ApiOperation(value = "查询门店当前销售人员", produces = "application/json")
    @GetMapping("/seller")
    public AbcServiceResponse<AbcEmployeeView> getClinicAbcEmployee(@RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) {
        AbcEmployeeView rsp = employeeService.getClinicAbcEmployee(clinicId);
        return new AbcServiceResponse<>(rsp);
    }

    @ApiOperation(value = "短信验证码登录", produces = "application/json")
    @PostMapping("/login/sms")
    @Deprecated //迁移到RegionAuth服务
    public AbcServiceResponse<LoginRsp> webSmsLogin(@Valid @RequestBody WebSmsLoginReq req,
                                                    HttpServletRequest request,
                                                    HttpServletResponse response) {
        LoginRsp rsp = employeeService.webSmsLogin(req, request, response);
        return new AbcServiceResponse<>(rsp);
    }

    @ApiOperation(value = "密码登录", produces = "application/json")
    @PostMapping("/login/password")
    @Deprecated //迁移到RegionAuth服务
    public AbcServiceResponse<LoginRsp> webPasswordLogin(@Valid @RequestBody WebPasswordLoginReq req,
                                                         HttpServletRequest request,
                                                         HttpServletResponse response) {
        LoginRsp rsp = employeeService.webPasswordLogin(req, request, response);
        return new AbcServiceResponse<>(rsp);
    }

    @ApiOperation(value = "APP登录", produces = "application/json")
    @PostMapping("/app/login")
    @Deprecated //迁移到RegionAuth服务
    public AbcServiceResponse<LoginRsp> appLogin(@Valid @RequestBody AppLoginReq req,
                                                 HttpServletRequest request,
                                                 HttpServletResponse response) {
        LoginRsp rsp = employeeService.appLogin(req, request, response);
        return new AbcServiceResponse<>(rsp);
    }

    @ApiOperation(value = "网页短信验证码注册并登录", produces = "application/json")
    @PostMapping("/login/sms-register")
    @Deprecated //迁移到RegionAuth服务
    public AbcServiceResponse<LoginRsp> webSmsRegisterLogin(@Valid @RequestBody WebSmsLoginReq req,
                                                            HttpServletRequest request,
                                                            HttpServletResponse response) {
        LoginRsp rsp = employeeService.webSmsRegisterLogin(req, request, response);
        return new AbcServiceResponse<>(rsp);
    }

    @ApiOperation(value = "扫描二维码登录", produces = "application/json")
    @PostMapping("/login/scan")
    @Deprecated //迁移到RegionAuth服务
    public AbcServiceResponse<LoginRsp> webQrScanLogin(@Valid @RequestBody WebQrScanLoginReq req,
                                                       HttpServletRequest request,
                                                       HttpServletResponse response) {
        LoginRsp rsp = employeeService.webQrScanLogin(req, request, response);
        return new AbcServiceResponse<>(rsp);
    }

    @ApiOperation(value = "微信公众号code登录", produces = "application/json")
    @PostMapping("/login/wxcode")
    public AbcServiceResponse<LoginRsp> webWxCodeLogin(@Valid @RequestBody WxCodeLoginReq req,
                                                       HttpServletRequest request,
                                                       HttpServletResponse response) {
        LoginRsp rsp = employeeService.webWxCodeLogin(req, request, response);
        return new AbcServiceResponse<>(rsp);
    }

    @ApiOperation(value = "内部人员登录", produces = "application/json")
    @PostMapping("/login/test")
    @Deprecated //迁移到RegionAuth服务
    public AbcServiceResponse<LoginRsp> testUserLogin(@Valid @RequestBody WebTestLoginReq req,
                                                      HttpServletRequest request,
                                                      HttpServletResponse response) {
        LoginRsp rsp = employeeService.testUserLogin(req, request, response);
        return new AbcServiceResponse<>(rsp);
    }

    @ApiOperation(value = "SCRM Web登录", produces = "application/json")
    @PostMapping("/login/scrm-web")
    @Deprecated //迁移到RegionAuth服务
    public AbcServiceResponse<CommonSwitchClinicRsp> scrmWebLogin(@Valid @RequestBody ScrmWebLoginReq req,
                                                                  HttpServletRequest request,
                                                                  HttpServletResponse response) {
        CommonSwitchClinicRsp rsp = employeeService.scrmWebLogin(req, request, response);
        return new AbcServiceResponse<>(rsp);
    }

    @ApiOperation(value = "SCRM企业微信客户端登录", produces = "application/json")
    @PostMapping("/login/scrm-qw-client")
    public AbcServiceResponse<CommonSwitchClinicRsp> scrmQwClientLogin(@Valid @RequestBody QwClientScrmLoginReq req,
                                                                       HttpServletRequest request,
                                                                       HttpServletResponse response) {
        CommonSwitchClinicRsp rsp = employeeService.scrmQwClientLogin(req, request, response);
        return new AbcServiceResponse<>(rsp);
    }

    @ApiOperation(value = "楚天云登录", produces = "application/json")
    @PostMapping("/login/chutianyun")
    @Deprecated //迁移到RegionAuth服务
    public AbcServiceResponse<LoginRsp> chuTianYunLogin(@Valid @RequestBody ChuTianYunLoginReq req,
                                                        HttpServletRequest request,
                                                        HttpServletResponse response) {
        LoginRsp rsp = employeeService.chuTianYunLogin(req, request, response);
        return new AbcServiceResponse<>(rsp);
    }

    @ApiOperation(value = "app切换门店", produces = "application/json")
    @PostMapping("/app/switch")
    @Deprecated //迁移到RegionAuth服务
    public AbcServiceResponse<AppSwitchClinicRsp> appSwitchClinic(@Valid @RequestBody AppSwitchClinicReq req,
                                                                  HttpServletRequest request,
                                                                  HttpServletResponse response) {
        AppSwitchClinicRsp rsp = employeeService.appSwitchClinic(req, request, response);
        return new AbcServiceResponse<>(rsp);
    }

    @ApiOperation(value = "web切换门店", produces = "application/json")
    @PostMapping("/switch/clinic")
    @Deprecated //迁移到RegionAuth服务
    public AbcServiceResponse<CommonSwitchClinicRsp> webSwitchClinic(@Valid @RequestBody BaseLoginReq req,
                                                                     HttpServletRequest request,
                                                                     HttpServletResponse response) {
        CommonSwitchClinicRsp rsp = employeeService.webSwitchClinic(req, request, response);
        return new AbcServiceResponse<>(rsp);
    }

    @ApiOperation(value = "SCRM切换门店", produces = "application/json")
    @PostMapping("/scrm/switch")
    public AbcServiceResponse<CommonSwitchClinicRsp> scrmSwitchClinic(@Valid @RequestBody ScrmSwitchReq req,
                                                                      HttpServletRequest request,
                                                                      HttpServletResponse response) {
        CommonSwitchClinicRsp rsp = employeeService.scrmSwitchClinic(req, request, response);
        return new AbcServiceResponse<>(rsp);
    }

    @ApiOperation(value = "his退出登录", produces = "application/json")
    @PostMapping("/logout")
    @Deprecated //迁移到RegionAuth服务
    public AbcServiceResponse<LogoutRsp> logout(@RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                HttpServletRequest request,
                                                HttpServletResponse response) {
        LogoutRsp rsp = employeeService.logout(employeeId, request, response);
        return new AbcServiceResponse<>(rsp);
    }

    @ApiOperation(value = "scrm退出登录", produces = "application/json")
    @PostMapping("/logout/scrm")
    @Deprecated //迁移到RegionAuth服务
    public AbcServiceResponse<LogoutRsp> logoutScrm(@RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                    HttpServletRequest request,
                                                    HttpServletResponse response) {
        LogoutRsp rsp = employeeService.logoutScrm(employeeId, request, response);
        return new AbcServiceResponse<>(rsp);
    }

    @ApiOperation(value = "刷新token", produces = "application/json")
    @PostMapping("/refresh-token")
    @Deprecated //迁移到RegionAuth服务
    public AbcServiceResponse<ClinicSuccessRsp> refreshToken(@RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                             @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) {
        employeeService.refreshToken(clinicId, employeeId);
        return new AbcServiceResponse<>(new ClinicSuccessRsp());
    }

    /**
     * Goods Excel导入，销售给门店建goods 需要验证销售登录才可以进行
     * 销售一般会要到诊所账号，完成了his账号的登录。
     * 但是能否导入数据是需要销售账号
     * 因为是在用户门店，这里是封装oaManagement的rpc
     */
    @ApiOperation(value = "销售给门店导入goods数据，需要验证销售身份", produces = "application/json")
    @GetMapping("/seller/login/by-code")
    @Deprecated //迁移到RegionAuth服务
    public AbcServiceResponse<QWCopLoginByCodeRsp> qWCorpLoginByCode(@RequestParam(value = "code", required = false) String code) {

        QWCopLoginByCodeReq reqBody = new QWCopLoginByCodeReq();
        reqBody.setCode(code);
        return new AbcServiceResponse<>(employeeService.qWCorpLoginByCode(reqBody));
    }

    @GetMapping("/hospital/outpatient-doctors")
    @ApiOperation(value = "医院管家：查询门诊医生列表", produces = "application/json")
    public AbcServiceResponse<AbcListPage<ClinicEmployeeListView>> listHospitalOutpatientDoctors(@RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                                 @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                                 @RequestParam(required = false, defaultValue = "0") int showDisable) {
        List<ClinicEmployeeListView> rows = employeeService.listCurClinicEmployee(chainId, clinicId, 1, 0, null, Collections.singletonList(ClinicRole.Role.DOCTOR), showDisable);
        AbcListPage<ClinicEmployeeListView> result = new AbcListPage<>();
        result.setRows(rows);
        return new AbcServiceResponse<>(result);
    }


    @ApiOperation(value = "小程序jsCode登录", produces = "application/json")
    @PostMapping("/weapp/login/jscode")
    @Deprecated //迁移到RegionAuth服务
    public AbcServiceResponse<LoginRsp> weappJsCodeLogin(@Valid @RequestBody WeappBaseLoginReq req,
                                                         HttpServletRequest request,
                                                         HttpServletResponse response) {
        LoginRsp rsp = employeeService.weappLoginByJsCode(req, request, response);
        return new AbcServiceResponse<>(rsp);
    }

    @ApiOperation(value = "小程序手机号登录", produces = "application/json")
    @PostMapping("/weapp/login/mobile")
    public AbcServiceResponse<LoginRsp> weappMobileLogin(@Valid @RequestBody WeappMobileLoginReq req,
                                                         HttpServletRequest request,
                                                         HttpServletResponse response) {
        LoginRsp rsp = employeeService.weappLoginByMobile(req, request, response);
        return new AbcServiceResponse<>(rsp);
    }

    @ApiOperation(value = "小程序验证码登录", produces = "application/json")
    @PostMapping("/weapp/login/sms")
    public AbcServiceResponse<LoginRsp> weappSmsLogin(@Valid @RequestBody WeappSmsLoginReq req,
                                                      HttpServletRequest request,
                                                      HttpServletResponse response) {
        LoginRsp rsp = employeeService.weappLoginBySMS(req, request, response);
        return new AbcServiceResponse<>(rsp);
    }

    @ApiOperation(value = "小程序密码登录", produces = "application/json")
    @PostMapping("/weapp/login/password")
    @Deprecated //迁移到RegionAuth服务
    public AbcServiceResponse<LoginRsp> webPasswordLogin(@Valid @RequestBody WeappPasswordLoginReq req,
                                                         HttpServletRequest request,
                                                         HttpServletResponse response) {
        LoginRsp rsp = employeeService.weappPasswordLogin(req, request, response);
        return new AbcServiceResponse<>(rsp);
    }

    @ApiOperation(value = "小程序获取当前用户信息", produces = "application/json")
    @GetMapping("/weapp/user-info")
    public AbcServiceResponse<WeappUserInfo> weappSmsLogin(HttpServletRequest request) {
        WeappUserInfo rsp = employeeService.getWeappUserInfo(request);
        return new AbcServiceResponse<>(rsp);
    }

    @ApiOperation(value = "小程序切换门店", produces = "application/json")
    @PostMapping("/weapp/switch/clinic")
    public AbcServiceResponse<WeappLoginRsp> weappSwitchClinic(@Valid @RequestBody WeappSwitchReq req, HttpServletRequest request,
                                                               HttpServletResponse response) {
        WeappLoginRsp rsp = employeeService.weappSwitchClinic(req, request, response);
        return new AbcServiceResponse<>(rsp);
    }

    @ApiOperation(value = "查询会诊医生列表", produces = "application/json")
    @GetMapping("/hospital/consultation-doctors")
    public AbcServiceResponse<AbcListPage<ConsultationDoctor>> listConsultationDoctors(@RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                       @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) {
        List<ConsultationDoctor> rows = employeeService.listConsultationDoctors(clinicId, chainId);
        AbcListPage<ConsultationDoctor> rsp = new AbcListPage<>();
        rsp.setRows(rows);
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 查询门店管理员列表
     */
    @GetMapping("/admins")
    @ApiOperation("查询门店管理员列表")
    public AbcServiceResponse<QueryOrganAdminRsp> queryOrganAdmin(@RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId) {
        return new AbcServiceResponse<>(new QueryOrganAdminRsp(employeeService.findClinicAdmins(clinicId)));
    }

    /**
     * 修改人员指定模块权限
     */
    @PutMapping("/{id}/module-permissions")
    @ApiOperation("修改人员指定模块权限")
    public AbcServiceResponse<EmployeeClinicInfo> updateModulePermissions(@PathVariable("id") String id,
                                                                          @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                          @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                          @Valid @RequestBody UpdateModulePermissionsReq req) {
        EmployeeClinicInfo rsp = employeeService.updateModulePermissions(clinicId, id, req, employeeId);
        return new AbcServiceResponse<>(rsp);
    }

    @GetMapping("/{id}/brief")
    @ApiOperation("查询人员基本信息")
    public AbcServiceResponse<EmployeeView> queryEmployeeById(@PathVariable("id") String id,
                                                              @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID, required = false) String clinicId,
                                                              @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID, required = false) String chainId) {
        EmployeeView employeeView = employeeService.getEmployeeView(id, clinicId, chainId);
        return new AbcServiceResponse<>(employeeView);
    }

    @GetMapping("/get-by-mobile/{mobile}")
    @ApiOperation(value = "根据手机号查询系统用户", produces = "application/json")
    public AbcServiceResponse<EmployeeView> getEmployeeByMobile(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID, required = false) String chainId,
                                                                @PathVariable(value = "mobile") String mobile) {
        Employee employee = employeeService.findByMobile(mobile, chainId);
        EmployeeView rsp = EmployeeView.from(employee);
        return new AbcServiceResponse<>(rsp);
    }

    @PostMapping("/sms/code-verify")
    @ApiOperation(value = "验证短信码", produces = "application/json")
    public AbcServiceResponse<BaseSuccessRsp> verifySmsCode(@Valid @RequestBody VerifySmsCodeReq req) {
        smsService.checkVerify(req.getMobile(), req.getAction(), req.getCode());
        return new AbcServiceResponse<>(new BaseSuccessRsp());
    }

    @ApiOperation(value = "查询是否是某个门店的管理员", produces = "application/json")
    @GetMapping("/is-admin")
    public AbcServiceResponse<QueryIsAdminRsp> isClinicAdmin(@RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                             @RequestParam String clinicId) {
        QueryIsAdminRsp rsp = employeeService.isClinicAdmin(employeeId, clinicId);
        return new AbcServiceResponse<>(rsp);
    }

    @ApiOperation(value = "查询虚拟手机号详情", produces = "application/json")
    @GetMapping("/virtual-mobile/{mobile}")
    public AbcServiceResponse<VirtualMobileView> getVirtualMobile(@PathVariable String mobile,
                                                                  @RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId) {
        VirtualMobileView rsp = clinicVirtualPhoneNumberService.getVirtualMobile(mobile, chainId);
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 更新医生医保评分
     */
    @PostMapping("/shebao-qualification")
    @ApiOperation("更新医生医保评分")
    @LogReqAndRsp
    public AbcServiceResponse<BaseSuccessRsp> updateShebaoQualification(@RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                        @Valid @RequestBody ShebaoEmployeeQualificationReq req) {
        employeeShebaoService.updateShebaoQualification(clinicId, req);
        return new AbcServiceResponse<>(new BaseSuccessRsp());
    }

    /**
     * 员工业务场景身份验证
     *
     * @param chainId    连锁id
     * @param clinicId   诊所 ID
     * @param employeeId 员工 ID
     * @param authReq    auth req
     * @return {@link AbcServiceResponse }<{@link EmployeeView }>
     */
    @PostMapping("/bus-authentication")
    @LogReqAndRsp
    public AbcServiceResponse<EmployeeBusAuthRsp> employeeBusAuthentication(@RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                            @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                            @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                            @RequestBody EmployeeBusAuthReq authReq) {
        return new AbcServiceResponse<>(EmployeeBusinessAuthServiceFactory.getInstance().getEmployeeBusinessAuthService(authReq.getGrantType()).authentication(chainId, clinicId, employeeId, authReq));
    }

    /**
     * 员工业务场景身份验证发送短信验证码
     *
     * @param chainId    连锁id
     * @param clinicId   诊所 ID
     * @param employeeId 员工 ID
     * @param sendSmsReq 发送短信请求
     * @return {@link AbcServiceResponse }<{@link SmsSendRsp }>
     */
    @PostMapping("/bus-authentication/sendSmsVerifyCode")
    @LogReqAndRsp
    public AbcServiceResponse<SmsSendRsp> employeeBusAuthenticationSendSmsVerifyCode(@RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                     @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                     @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                                     @Valid @RequestBody EmployeeBusSendSmsReq sendSmsReq) {
        return new AbcServiceResponse<>(
                ((EmployeeBusinessVerifyCodeAuthService) EmployeeBusinessAuthServiceFactory.getInstance()
                        .getEmployeeBusinessAuthService(ClinicConstants.EmployeeBusAuthGrantType.VERIFY_CODE))
                        .sendSms(chainId, clinicId, employeeId, sendSmsReq)
        );
    }

    /**
     * 员工业务场景身份验证 认证吗
     *
     * @param chainId     连锁id
     * @param clinicId    诊所 ID
     * @param employeeId  员工 ID
     * @param authCodeReq 授权码 req
     * @return {@link AbcServiceResponse }<{@link EmployeeBusAuthCodeRsp }>
     */
    @PostMapping("/bus-authentication/authCode")
    @LogReqAndRsp
    public AbcServiceResponse<EmployeeBusAuthCodeRsp> employeeBusAuthenticationAuCode(@RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                      @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                      @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                                      @Valid @RequestBody EmployeeBusAuthCodeReq authCodeReq) {
        return new AbcServiceResponse<>(
                ((EmployeeBusinessAuthCodeAuthService) EmployeeBusinessAuthServiceFactory.getInstance()
                        .getEmployeeBusinessAuthService(ClinicConstants.EmployeeBusAuthGrantType.AUTHORIZATION_CODE))
                        .generateAuthCode(chainId, clinicId, employeeId, authCodeReq)
        );
    }

    @LogReqAndRsp
    @GetMapping("/bus-authentication/qrCode")
    @ApiOperation(value = "生成员工业务场景生成二维码", produces = "application/json")
    public AbcServiceResponse<EmployeeBusQrCodeRsp> getEmployeeBusQrCode(@RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                         @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                         @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                         @RequestParam("businessId") @ApiParam(value = "业务Id") String businessId,
                                                                         @RequestParam("businessType") @ApiParam(value = "业务类型") String businessType) {
        return new AbcServiceResponse<>(employeeService.getEmployeeBusQrCode(chainId, clinicId, employeeId, businessId, businessType));
    }

    @LogReqAndRsp
    @GetMapping("/bus-authentication/qrCode-status")
    @ApiOperation(value = "查询员工业务场景二维码状态", produces = "application/json")
    public AbcServiceResponse<EmployeeBusQrCodeRsp> getEmployeeBusQrCodeStatus(@RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                               @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                               @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                               @RequestParam("qrCodeSceneKey") @ApiParam(value = "二维码场景值") String qrCodeSceneKey) {
        return new AbcServiceResponse<>(employeeService.getEmployeeBusQrCodeStatus(chainId, clinicId, employeeId, qrCodeSceneKey));
    }

    @LogReqAndRsp
    @PutMapping("/bus-authentication/qrCode-status")
    @ApiOperation(value = "修改员工业务场景二维码状态", produces = "application/json")
    public AbcServiceResponse<EmployeeBusQrCodeRsp> updateEmployeeBusQrCodeStatus(@RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                  @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                  @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                                  @RequestBody UpdateEmployeeBusQrCodeStatusReq req) {
        return new AbcServiceResponse<>(employeeService.updateEmployeeBusQrCodeStatus(chainId, clinicId, employeeId, req));
    }
}
