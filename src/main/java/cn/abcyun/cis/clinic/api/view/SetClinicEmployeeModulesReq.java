package cn.abcyun.cis.clinic.api.view;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 设置成员门店权限req
 * <AUTHOR>
 * @date 2023/5/18 13:45
 */
@Data
public class SetClinicEmployeeModulesReq {
    /**
     * 连锁id: 不能为空
     */
    @NotEmpty(message = "连锁id不能为空")
    private String chainId;
    /**
     * 门店id：不能为空
     */
    @NotEmpty(message = "门店id不能为空")
    private String clinicId;
    /**
     * 员工id: 不能为空
     */
    @NotEmpty(message = "员工id不能为空")
    private List<String> employeeIds;

    /**
     * 操作人id: 不能为空
     */
    @NotEmpty(message = "操作人id不能为空")
    private String operatorId;

    /**
     * 权限id合集
     */
    private List<String> moduleIds;

    /**
     * 0: 增加权限 1: 移除权限
     * {@link Type}
     */
    private int type;

    public static class Type {
        public static final int ADD = 0;
        public static final int REMOVE = 1;
    }
}
