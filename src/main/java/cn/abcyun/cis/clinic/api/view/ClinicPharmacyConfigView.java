package cn.abcyun.cis.clinic.api.view;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.BeanUtils;

@ApiModel
@Data
public class ClinicPharmacyConfigView {

  /**
   * id
   */
  
  @ApiModelProperty(value = "id")
  private String id;

  /**
   * 0经营范围；1资质证照类型
   * {@link cn.abcyun.cis.clinic.model.ClinicPharmacyConfig.Type}
   */
  @ApiModelProperty(value = "0经营范围；1资质证照类型")
  private int type;

  /**
   * 名称
   */
  @ApiModelProperty(value = "名称")
  private String name;

  /**
   * 排序
   */
  @ApiModelProperty(value = "排序")
  private int sort;

  public static ClinicPharmacyConfigView from (cn.abcyun.cis.clinic.model.ClinicPharmacyConfig clinicPharmacyConfig) {
    if (clinicPharmacyConfig == null) {
      return null;
    }
    ClinicPharmacyConfigView view = new ClinicPharmacyConfigView();
    BeanUtils.copyProperties(clinicPharmacyConfig, view);
    return view;
  }
  
}
