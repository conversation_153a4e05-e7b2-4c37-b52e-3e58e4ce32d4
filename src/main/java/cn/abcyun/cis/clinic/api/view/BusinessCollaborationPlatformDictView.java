package cn.abcyun.cis.clinic.api.view;

import cn.abcyun.cis.clinic.model.BusinessCollaborationPlatformDict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.BeanUtils;

/**
 * 业务协同平台字典视图
 * <AUTHOR>
 * @date 2025/10/15 20:23
 */
@Data
@ApiModel
public class BusinessCollaborationPlatformDictView {

    @ApiModelProperty(value = "主键ID")
    private Long id;

    @ApiModelProperty(value = "字典类型")
    private int dictType;

    @ApiModelProperty(value = "协同平台值")
    private String platformValue;

    @ApiModelProperty(value = "协同平台值含义")
    private String platformValueMeaning;

    @ApiModelProperty(value = "HIS系统值")
    private String hisSystemValue;


    /**
     * 从实体转换为视图对象
     */
    public static BusinessCollaborationPlatformDictView from(BusinessCollaborationPlatformDict dict) {
        if (dict == null) {
            return null;
        }
        BusinessCollaborationPlatformDictView view = new BusinessCollaborationPlatformDictView();
        BeanUtils.copyProperties(dict, view);
        return view;
    }
}
