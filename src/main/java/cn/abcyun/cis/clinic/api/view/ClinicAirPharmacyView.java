package cn.abcyun.cis.clinic.api.view;

import cn.abcyun.cis.clinic.model.Organ;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.springframework.beans.BeanUtils;
import org.springframework.util.StringUtils;

import java.util.Objects;

/**
 * 门店是否支持空中药房
 * <AUTHOR>
 * @date 2021/11/19 14:47
 */
@Data
public class ClinicAirPharmacyView {

    @JsonProperty("clinicId")
    private String id;

    private String addressProvinceId;

    private String addressCityId;

    private int innerFlag;

    @JsonProperty("clinicName")
    public String getClinicName() {
        return !StringUtils.isEmpty(shortName) ? shortName : name;
    }

    @JsonProperty("isSupportAirPharmacy")
    private boolean isSupportAirPharmacy;

    @JsonIgnore
    private String name;

    @JsonIgnore
    private String shortName;

    @JsonIgnore
    private int nodeType;

    public static ClinicAirPharmacyView from(Organ organ) {
        if (Objects.isNull(organ)) {
            return null;
        }
        ClinicAirPharmacyView view = new ClinicAirPharmacyView();
        BeanUtils.copyProperties(organ, view);
        return view;
    }
}
