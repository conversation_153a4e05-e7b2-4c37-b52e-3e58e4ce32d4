package cn.abcyun.cis.clinic.api.view;

import cn.abcyun.cis.clinic.model.ClinicEmployee;
import cn.abcyun.cis.clinic.model.Constants;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.Instant;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 个人信息 + 所在门店信息
 * <AUTHOR>
 * @date 2022/3/22 11:08
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EmployeeWithClinicsView extends EmployeeView {

    @ApiModelProperty(value = "微信unionId")
    private String wechatUnionId;

    @ApiModelProperty(value = "角色名称")
    private List<String> rolesName;

    @ApiModelProperty(value = "最近登录的门店")
    private Clinic clinic;

    @Data
    public static class Clinic {

        @ApiModelProperty(value = "门店id")
        private String id;

        @ApiModelProperty(value = "连锁id")
        private String parentId;

        @ApiModelProperty(value = "门店名称")
        private String name;

        @ApiModelProperty(value = "门店名称")
        private String parentName;

        @ApiModelProperty(value = "省名称")
        private String addressProvinceName;

        @ApiModelProperty(value = "市名称")
        private String addressCityName;

        @ApiModelProperty(value = "公众号id")
        private String mpId;

        @ApiModelProperty(value = "小程序id")
        private String weappId;

        @ApiModelProperty(value = "销售")
        private String sellerName;

        @ApiModelProperty(value = "版本")
        private String edition;

        @ApiModelProperty(value = "是否试用门店 0:否 1:是")
        private int isTrial;

        @ApiModelProperty(value = "最后一次登录时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
        private Instant lastLoginTime;

        public String getEdition() {
            if (StringUtils.isEmpty(editionId)) {
                return "无版本";
            }
            switch (Integer.parseInt(editionId)) {
                case Constants.ClinicEditionId.BASIC:
                    return "基础版";
                case Constants.ClinicEditionId.PROFESSIONAL:
                    return "专业版";
                case Constants.ClinicEditionId.ULTIMATE:
                    return "旗舰版";
                case Constants.ClinicEditionId.VIP:
                    return "大客户版";
                default: return "无版本";
            }
        }

        /**
         * 版本id
         */
        @JsonIgnore
        private String editionId;

    }

    @JsonIgnore
    private List<Integer> roles;

    public List<String> getRolesName() {
        if (CollectionUtils.isEmpty(this.roles)) {
            return Collections.emptyList();
        }
        return this.roles.stream()
                .map(role -> {
                    String roleName = "其他";
                    switch (role) {
                        case ClinicEmployee.Role.DOCTOR:
                            roleName = "医生";
                            break;
                        case ClinicEmployee.Role.NURSE:
                            roleName = "护士";
                            break;
                        case ClinicEmployee.Role.EXAMINER:
                            roleName = "检验师";
                            break;
                        case ClinicEmployee.Role.PHYSIOTHERAPIST:
                            roleName = "理疗师";
                            break;
                        case ClinicEmployee.Role.ASSISTANT:
                            roleName = "医助";
                            break;
                        case ClinicEmployee.Role.OTHER:
                            roleName = "其他";
                            break;
                        case ClinicEmployee.Role.WAREHOUSER:
                            roleName = "库管";
                            break;
                        case ClinicEmployee.Role.BUYER:
                            roleName = "采购";
                            break;
                    }
                    return roleName;
                })
                .collect(Collectors.toList());
    }
}
