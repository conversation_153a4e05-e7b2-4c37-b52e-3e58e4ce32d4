package cn.abcyun.cis.clinic.api.view;

import com.aliyuncs.auth.sts.AssumeRoleResponse;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/11/8 14:55
 */
@Data
public class QueryOssStsTokenRsp {

    @JsonProperty("bucket")
    private String bucket;

    @JsonProperty("SecurityToken")
    private String securityToken;

    @JsonProperty("AccessKeySecret")
    private String accessKeySecret;

    @JsonProperty("AccessKeyId")
    private String accessKeyId;

    @JsonProperty("Expiration")
    private String expiration;

    public static QueryOssStsTokenRsp fillCredentials(AssumeRoleResponse.Credentials credentials) {
        if (credentials == null) {
            return null;
        }
        QueryOssStsTokenRsp rsp = new QueryOssStsTokenRsp();
        rsp.setSecurityToken(credentials.getSecurityToken());
        rsp.setAccessKeySecret(credentials.getAccessKeySecret());
        rsp.setAccessKeyId(credentials.getAccessKeyId());
        rsp.setExpiration(credentials.getExpiration());
        return rsp;
    }
}
