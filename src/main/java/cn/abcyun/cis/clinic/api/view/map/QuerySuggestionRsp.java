package cn.abcyun.cis.clinic.api.view.map;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/3 18:15
 */

@ApiModel(value = "QuerySuggestionRsp")
@Data
public class QuerySuggestionRsp {
    /**
     * 状态码，0为正常，其它为异常
     */
    @ApiModelProperty(value = "状态码，0为正常，其它为异常")
    private int status;
    /**
     * 状态说明
     */
    @ApiModelProperty(value = "状态说明")
    private String message;
    /**
     * 结果总数（注：本服务一个查询条件最多返回100条结果）
     */
    @ApiModelProperty(value = "结果总数（注：本服务一个查询条件最多返回100条结果）")
    private int count;
    /**
     * 查询结果
     */
    @ApiModelProperty(value = "查询结果")
    private List<Data> data;

    @ApiModel(value = "QuerySuggestionRsp.Data")
    @lombok.Data
    public static class Data {
        /**
         * POI唯一标识（type为4时不返回）
         */
        @ApiModelProperty(value = "POI唯一标识（type为4时不返回）")
        private String id;
        /**
         * 提示文字（地点名称）
         */
        @ApiModelProperty(value = "提示文字（地点名称）")
        private String title;
        /**
         * 地址（type为4时不返回）
         */
        @ApiModelProperty(value = "地址（type为4时不返回）")
        private String address;
        /**
         * POI（地点）分类（type为4时不返回）
         */
        @ApiModelProperty(value = "POI（地点）分类（type为4时不返回）")
        private String category;
        /**
         * POI（地点）分类编码，设置added_fields=category_code时返回
         */
        @ApiModelProperty(value = "POI（地点）分类编码，设置added_fields=category_code时返回")
        @JsonProperty("category_code")
        private int categoryCode;
        /**
         * POI类型，值说明：0:普通POI / 1:公交车站 / 2:地铁站 / 3:公交线路 / 4:行政区划
         */
        @ApiModelProperty(value = "POI类型，值说明：0:普通POI / 1:公交车站 / 2:地铁站 / 3:公交线路 / 4:行政区划")
        private int type;
        /**
         * 行政区划代码
         */
        @ApiModelProperty(value = "行政区划代码")
        private int adcode;
        /**
         * 提示所述位置坐标
         */
        @ApiModelProperty(value = "提示所述位置坐标")
        private Location location;
        /**
         * 省份
         */
        @ApiModelProperty(value = "省份")
        private String province;
        /**
         * 城市
         */
        @ApiModelProperty(value = "城市")
        private String city;
        /**
         * 区域
         */
        @ApiModelProperty(value = "区域")
        private String district;

//        private List<SubPois> sub_pois;

        @ApiModel(value = "QuerySuggestionRsp.Location")
        @lombok.Data
        public static class Location {
            /**
             * 纬度
             */
            @ApiModelProperty(value = "纬度")
            private String lat;
            /**
             * 经度
             */
            @ApiModelProperty(value = "经度")
            private String lng;
        }

//        @lombok.Data
//        public static class SubPois {
//            private String parent_id;
//            private String id;
//            private String title;
//            private String address;
//            private String category;
//            private Location location;
//            private int adcode;
//            private String city;
//        }
    }
}
