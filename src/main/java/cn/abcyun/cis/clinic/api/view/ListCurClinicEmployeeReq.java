package cn.abcyun.cis.clinic.api.view;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024-01-30 11:22
 * @Description
 */

@Data
public class ListCurClinicEmployeeReq {
    private String chainId;
    @NotEmpty(message = "clinicId can not be empty")
    private String clinicId;
    private Integer isDoctor;
    private Integer isNeedHeadImg;
    private List<String> moduleIds;
    private List<Integer> roles;
}
