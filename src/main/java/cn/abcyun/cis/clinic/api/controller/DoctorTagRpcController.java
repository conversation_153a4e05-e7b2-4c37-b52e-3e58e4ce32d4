package cn.abcyun.cis.clinic.api.controller;

import cn.abcyun.cis.clinic.api.view.DoctorTagView;
import cn.abcyun.cis.clinic.api.view.DoctorTagWithEmployeeView;
import cn.abcyun.cis.clinic.api.view.QueryDoctorTagsWithEmployeeReq;
import cn.abcyun.cis.clinic.api.view.UpdateDoctorTagsForMcReq;
import cn.abcyun.cis.clinic.service.DoctorTagService;
import cn.abcyun.common.model.AbcListPage;
import cn.abcyun.common.model.AbcServiceResponse;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/rpc/v3/clinics/tags")
public class DoctorTagRpcController {

    @Autowired
    private DoctorTagService doctorTagService;

    @GetMapping("/for-mc/{chainId}")
    public AbcServiceResponse<AbcListPage<DoctorTagView>> queryDoctorTagsForMc(@PathVariable("chainId") String chainId) {
        List<DoctorTagView> tags = doctorTagService.queryDoctorTagsForMc(chainId);
        AbcListPage<DoctorTagView> rspList = new AbcListPage<>();
        rspList.setRows(tags);
        return new AbcServiceResponse<>(rspList);
    }

    @GetMapping("/with-employee-for-mc/{chainId}")
    public AbcServiceResponse<AbcListPage<DoctorTagWithEmployeeView>> queryDoctorTagsWithEmployeeForMc(@PathVariable("chainId") String chainId) {
        List<DoctorTagWithEmployeeView> tags = doctorTagService.queryDoctorTagsWithEmployeeForMc(chainId);
        AbcListPage<DoctorTagWithEmployeeView> rspList = new AbcListPage<>();
        rspList.setRows(tags);
        return new AbcServiceResponse<>(rspList);
    }

    @PutMapping("/for-mc")
    public AbcServiceResponse<AbcListPage<DoctorTagView>> updateDoctorTagsForMc(@RequestBody UpdateDoctorTagsForMcReq reqBody) {
        doctorTagService.clearCacheDoctorTagsForMc(reqBody.getChainId());

        List<DoctorTagView> tags = doctorTagService.updateDoctorTagsForMc(reqBody);
        AbcListPage<DoctorTagView> rspList = new AbcListPage<>();
        rspList.setRows(tags);
        return new AbcServiceResponse<>(rspList);
    }

    @ApiOperation(value = "查询标签员工通用接口", produces = "application/json")
    @PostMapping("/with-employee")
    public AbcServiceResponse<AbcListPage<DoctorTagWithEmployeeView>> queryDoctorTagsWithEmployee(@RequestBody @Valid QueryDoctorTagsWithEmployeeReq reqBody) {
        List<DoctorTagWithEmployeeView> tags = doctorTagService.queryDoctorTagsWithEmployeeForMcNew(reqBody);
        AbcListPage<DoctorTagWithEmployeeView> rspList = new AbcListPage<>();
        rspList.setRows(tags);
        return new AbcServiceResponse<>(rspList);
    }

    @ApiOperation(value = "查询员工标签列表", produces = "application/json")
    @GetMapping("/get-by-employee")
    public AbcServiceResponse<AbcListPage<DoctorTagView>> getDoctorTagsByEmployee(@RequestParam String chainId, @RequestParam String employeeId) {
        AbcListPage<DoctorTagView> rsp = new AbcListPage<>();
        rsp.setRows(doctorTagService.getDoctorTagsByEmployee(chainId, employeeId));
        return new AbcServiceResponse<>(rsp);
    }
}
