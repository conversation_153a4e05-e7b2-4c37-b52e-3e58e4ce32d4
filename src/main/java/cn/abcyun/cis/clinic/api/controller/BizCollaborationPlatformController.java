package cn.abcyun.cis.clinic.api.controller;

import cn.abcyun.cis.clinic.api.view.BusinessCollaborationPlatformDictView;
import cn.abcyun.cis.clinic.service.BizCollaborationPlatformDictService;
import cn.abcyun.common.model.AbcListPage;
import cn.abcyun.common.model.AbcServiceResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025/10/15 20:23
 */
@RestController
@RequestMapping("/api/v3/clinics/biz-collaboration-platform")
@Api(value = "BizCollaborationPlatformController", tags = "业务协同平台", produces = "application/json")
@Slf4j
public class BizCollaborationPlatformController {
    @Autowired
    private BizCollaborationPlatformDictService dictService;

    /**
     * 根据dictType获取字典码表
     * @param dictType 字典类型
     *                 有缓存
     */
    @GetMapping("/dict")
    @ApiOperation(value = "根据dictType获取字典码表", produces = "application/json")
    public AbcServiceResponse<AbcListPage<BusinessCollaborationPlatformDictView>> getDictByDictType(@RequestParam("dictType") int dictType,
                                                                                                    @RequestParam("platformType") int platformType) {
        return new AbcServiceResponse<>(dictService.getDictByDictType(dictType, platformType));
    }


}
