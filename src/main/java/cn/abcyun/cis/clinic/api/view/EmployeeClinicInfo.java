package cn.abcyun.cis.clinic.api.view;

import cn.abcyun.cis.clinic.model.ClinicEmployee;
import cn.abcyun.cis.commons.util.JsonUtils;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;


@EqualsAndHashCode(callSuper = true)
@Data
public class EmployeeClinicInfo extends EmployeeClinicBasicInfo {
    private Integer sort;           // 排序
    private Integer showInWeClinic; // 是否在微诊所展示
    private String shebaoDoctorCode;        //社保医生服务编码
    private String shebaoDoctorJobTitle;    //社保医生职称
    private String shebaoChargerCode;       //社保收银员编码
    private String nationalDoctorCode;      //国家医生编码
    private String nationalNurseCode;       //
    private JsonNode assistFor;             // 协作谁:{isAll, scope:[doctorId,doctorId]}
    private String qwCorpId;                //企微企业id
    private String qwUserId;                //企微人员id
    /**
     * 已扣分数
     */
    @ApiModelProperty("已扣分数")
    private Integer shebaoScoreSum;

    /**
     * 状态
     */
    private Integer shebaoDrStatus; // 状态

    /**
     * 年度考核分数年份
     */
    private String shebaoScoreYear; // 年度考核分数年份

    /**
     * 年度考核总分数
     */
    @ApiModelProperty("年度考核总分数")
    private Integer shebaoScoreTotal = 12; // 年度考核总分数

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AssistFor {
        private Boolean isAll = false;
        private List<String> scope = new ArrayList<>();
    }

    public static EmployeeClinicInfo from(ClinicEmployee clinicEmployee) {
        if (clinicEmployee == null) {
            return null;
        }

        EmployeeClinicInfo clinicInfo = new EmployeeClinicInfo();
        BeanUtils.copyProperties(clinicEmployee, clinicInfo);
        return clinicInfo;
    }

    public void fixFieldAssistFor() {
        ClinicEmployee.AssistFor tmp = JsonUtils.readValue(this.assistFor, ClinicEmployee.AssistFor.class);
        if (tmp == null) {
            return;
        }
        if (tmp.getIsAll() != null && tmp.getIsAll()) {
            // 助理全部医生, 只返回标志，不返回全部的医生列表，而是返回空集合
            tmp.setScope(Collections.emptyList());
            this.assistFor = JsonUtils.dumpAsJsonNode(tmp);
        }
    }

    /**
     * 清除医保评分信息
     */
    public void clearShebaoScoreInfo() {
        this.shebaoScoreSum = null;
        this.shebaoScoreYear = null;
        this.shebaoScoreTotal = null;
        this.shebaoDrStatus = null;
    }
}
