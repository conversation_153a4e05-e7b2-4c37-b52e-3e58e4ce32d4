package cn.abcyun.cis.clinic.api.view;

import cn.abcyun.cis.clinic.model.ClinicBusinessPermission;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/31 15:10
 */
@Data
public class BatchListClinicBusinessPermissionReq {

    @NotEmpty(message = "chainId can not be empty")
    private String chainId;

    @NotEmpty(message = "clinicId can not be empty")
    private String clinicId;

    /**
     * 权限key集合
     * {@link ClinicBusinessPermission.Key}
     */
    @NotEmpty(message = "keys can not be empty")
    private List<String> keys;

    /**
     * 业务id合集
     */
    @NotEmpty(message = "businessIds can not be empty")
    private List<String> businessIds;
}
