package cn.abcyun.cis.clinic.api.view.login;

import cn.abcyun.cis.clinic.api.view.AbcTokenInfo;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.jsonwebtoken.Claims;
import lombok.Data;

import java.time.Instant;

/**
 * <AUTHOR>
 * @create 2023-02-15 11:52
 * @Description
 */
@Data
public class WeappTokenInfo extends AbcTokenInfo {
    private String wxAppId;
    private Integer wxAppType;
    private String wxUserId;
    private String wxOpenId;
    private String wxUnionId;

    public WeappTokenInfo() {
        super();
    }

    public WeappTokenInfo(Claims claims) {
        super(claims);
        this.wxAppId = claims.get("wxAppId", String.class);
        this.wxAppType = claims.get("wxAppType", Integer.class);
        this.wxUserId = claims.get("wxUserId", String.class);
        this.wxOpenId = claims.get("wxOpenId", String.class);
        this.wxUnionId = claims.get("wxUnionId", String.class);
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class RedisTokenValue {
        private WeappTokenInfo tokenInfo;
        private int isValid;
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        private String invalidLoginWay;
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Instant invalidTime;
    }

}
