package cn.abcyun.cis.clinic.api.view;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * 领取门店活动rsp
 * <AUTHOR>
 * @date 2022/5/11 17:27
 */
@Data
public class ObtainClinicActivityRsp {

    @ApiModelProperty(value = "领取活动成功的门店列表")
    private List<ActivityOrganView> list;

    @ApiModelProperty(value = "活动减免总费用")
    public BigDecimal getEquivalentAmountTotal() {
        if (CollectionUtils.isEmpty(list)) {
            return BigDecimal.ZERO;
        }
        return list.stream()
                .map(ActivityOrganView::getEquivalentAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
}
