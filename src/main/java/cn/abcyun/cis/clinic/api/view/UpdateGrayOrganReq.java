package cn.abcyun.cis.clinic.api.view;

import cn.abcyun.cis.clinic.model.GrayOrgan;
import cn.abcyun.cis.commons.exception.ParamNotValidException;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.collections.CollectionUtils;

import java.util.Set;

@Data
@Accessors(chain = true)
public class UpdateGrayOrganReq {

    /**
     * 类型：0:修改grayOrgan的env和zone，10：纯通知网关更新
     */
    @ApiModelProperty(value = "类型：0:修改grayOrgan的env和zone，10：纯通知网关更新")
    private int type;

    /**
     * 0:mq，1：redis
     */
    @ApiModelProperty(value = "0:mq，1：redis（只有type为10时，才能使用redis通道）")
    private int notifyGatewayChannel;

    @ApiModelProperty(value = "连锁id集合，一次最多500家")
    private Set<String> chainIds;

    @ApiModelProperty(value = "环境")
    private int env;

    @ApiModelProperty(value = "zone")
    private String zone;

    /**
     * 是否通知前端
     */
    @ApiModelProperty(value = "是否通知前端")
    private int notifyToPc;


    public void checkParam() {

        if (type == 0) {
            if (CollectionUtils.isEmpty(chainIds)) {
                throw new ParamNotValidException("chainIds不能为空");
            }

            if (chainIds.size() > 500) {
                throw new ParamNotValidException("一次最多修改500家");
            }

            if (!GrayOrgan.Env.ALL_ENVS.contains(env)) {
                throw new ParamNotValidException("env只能是0(prod)，1(gray)，2{pre}");
            }

            if (!GrayOrgan.Zone.ALL_ZONES.contains(zone)) {
                throw new ParamNotValidException("zone只能是primary，standby");
            }

            if (getNotifyGatewayChannel() != NotifyGatewayChannel.ROCKET_MQ) {
                throw new ParamNotValidException("修改grayOrgan的env和zone时，只能使用rocketMq通知网关");
            }
        }

    }

    public static class NotifyGatewayChannel {
        public static final int ROCKET_MQ = 0;
        public static final int REDIS = 1;

    }
}
