package cn.abcyun.cis.clinic.api.controller;

import cn.abcyun.cis.clinic.api.view.*;
import cn.abcyun.cis.clinic.model.Department;
import cn.abcyun.cis.clinic.model.Organ;
import cn.abcyun.cis.clinic.service.DepartmentService;
import cn.abcyun.cis.commons.jwt.CisJWTUtils;
import cn.abcyun.cis.core.aop.LogReqAndRsp;
import cn.abcyun.common.model.AbcListPage;
import cn.abcyun.common.model.AbcServiceResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/11/9 17:21
 */
@Api(value = "DepartmentController", description = "科室接口", produces = "application/json")
@RestController
@RequestMapping("/api/v3/clinics/departments")
public class DepartmentController {

    @Autowired
    private DepartmentService departmentService;


    @GetMapping
    public AbcServiceResponse<AbcListPage<DepartmentWithCountView>> listDepartmentsWithCountInfo(@RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                                 @RequestParam(value = "showDisable", required = false, defaultValue = "0") int showDisable,
                                                                                                 @RequestParam(required = false) Integer businessScope,
                                                                                                 @RequestParam(required = false) Integer type) {
        AbcListPage<DepartmentWithCountView> list = departmentService.listDepartmentsWithCountInfo(clinicId, type, showDisable, businessScope);
        return new AbcServiceResponse<>(list);
    }

    @GetMapping("/{id}")
    public AbcServiceResponse<DepartmentWithEmployeeView> getDepartment(@PathVariable String id,
                                                                        @RequestHeader(CisJWTUtils.CIS_HEADER_HIS_TYPE) String hisType,
                                                                        @RequestParam(required = false) Integer businessScope) {
        DepartmentWithEmployeeView view = departmentService.getDepartment(id, hisType.equals(String.valueOf(Organ.HisType.HOSPITAL)), businessScope);
        return new AbcServiceResponse<>(view);
    }

    @PostMapping
    public AbcServiceResponse<DepartmentView> insertDepartment(@RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                               @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                               @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                               @RequestBody @Valid DepartmentSaveReq saveReq) {
        DepartmentView departmentView = departmentService.insertDepartment(saveReq, chainId, clinicId, employeeId);
        return new AbcServiceResponse<>(departmentView);
    }

    @PutMapping("/{id}")
    public AbcServiceResponse<DepartmentView> updateDepartment(@PathVariable String id,
                                                               @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                               @RequestBody @Valid DepartmentUpdateReq updateReq) {
        DepartmentView departmentView = departmentService.updateDepartment(id, updateReq, employeeId);
        return new AbcServiceResponse<>(departmentView);
    }

    @PutMapping("/{id}/hospital")
    @LogReqAndRsp
    public AbcServiceResponse<DepartmentView> updateDepartmentEmployeesForHospital(@PathVariable String id,
                                                                                   @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                                   @RequestBody DepartmentUpdateHospitalReq updateReq) {
        DepartmentView departmentView = departmentService.updateDepartmentEmployeesForHospital(id, updateReq, employeeId);
        return new AbcServiceResponse<>(departmentView);
    }

    @PutMapping("/{id}/status")
    public AbcServiceResponse<String> updateDepartmentStatus(@PathVariable String id,
                                                             @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                             @RequestHeader(CisJWTUtils.CIS_HEADER_HIS_TYPE) String hisType,
                                                             @RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                             @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                             @RequestBody @Valid DepartmentStatusUpdateReq req) {
        departmentService.updateDepartmentStatus(id, employeeId, chainId, clinicId, hisType, req);
        return new AbcServiceResponse<>(id);
    }

    @DeleteMapping("/{id}")
    public AbcServiceResponse<String> deleteDepartment(@PathVariable String id,
                                                       @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                       @RequestHeader(CisJWTUtils.CIS_HEADER_HIS_TYPE) String hisType,
                                                       @RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                       @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId
    ) {
        departmentService.deleteDepartment(id, employeeId, chainId, clinicId, hisType);
        return new AbcServiceResponse<>(id);
    }

    @GetMapping("/employees")
    public AbcServiceResponse<DepartmentEmployeeListView> listDepartmentEmployeeAbstractList(@RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                             @RequestParam(value = "queryClinicId", required = false) String queryClinicId) {
        DepartmentEmployeeListView view = departmentService.listDepartmentEmployeeAbstractList(clinicId, queryClinicId);
        return new AbcServiceResponse<>(view);
    }

    @GetMapping("/employees/{clinicId}/{employeeId}")
    public AbcServiceResponse<AbcListPage<EmployeeDepartmentInfo>> listWaitingCheckEmployees(@PathVariable String clinicId,
                                                                                             @PathVariable String employeeId) {
        List<EmployeeDepartmentInfo> rows = departmentService.listWaitingCheckDepartEmployees(clinicId, employeeId);
        AbcListPage<EmployeeDepartmentInfo> result = new AbcListPage<>();
        result.setRows(rows);
        return new AbcServiceResponse<>(result);
    }


    @ApiOperation(value = "查询当前登录人的门诊科室列表：不包括职能科室", httpMethod = "GET")
    @GetMapping("/get-by-cur-employee")
    public AbcServiceResponse<AbcListPage<DepartmentView>> getByCurEmployee(@RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                            @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                            @RequestParam(required = false) Integer businessScope) {
        List<Department> rows = departmentService.findDepartmentsByEmployeeId(clinicId, employeeId, Arrays.asList(Department.Type.OUTPATIENT_DEPARTMENT, Department.Type.CHILDCARE_DEPARTMENT), businessScope);
        AbcListPage<DepartmentView> result = new AbcListPage<>();
        result.setRows(rows.stream().map(DepartmentView::from).collect(Collectors.toList()));
        return new AbcServiceResponse<>(result);
    }

    @GetMapping("/outpatient")
    @ApiOperation(value = "查询门店门诊科室列表", httpMethod = "GET")
    public AbcServiceResponse<AbcListPage<DepartmentView>> listOutpatientDepartment(@RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String headerClinicId,
                                                                                    @RequestParam(required = false) String clinicId) {

        clinicId = StringUtils.isEmpty(clinicId) ? headerClinicId : clinicId;
        List<Department> rows = departmentService.listOutpatientDepartment(clinicId);
        AbcListPage<DepartmentView> result = new AbcListPage<>();
        result.setRows(rows.stream().map(DepartmentView::from).collect(Collectors.toList()));
        return new AbcServiceResponse<>(result);
    }

}
