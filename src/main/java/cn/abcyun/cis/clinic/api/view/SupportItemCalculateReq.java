package cn.abcyun.cis.clinic.api.view;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/27 21:54
 */
@Data
@ApiModel
public class SupportItemCalculateReq {
    /**
     * 开通项目的 key
     */
    private List<SupportItem> supportItems;

    @Data
    @ApiModel("SupportItemCalculateReq.SupportItem")
    public static class SupportItem {
        /**
         * 开通项目key
         */
        private String key;

        /**
         * 开通项目的名称
         */
        private String name;
    }

}
