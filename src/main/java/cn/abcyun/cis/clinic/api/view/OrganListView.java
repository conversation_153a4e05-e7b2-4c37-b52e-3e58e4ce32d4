package cn.abcyun.cis.clinic.api.view;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.config.GoodsConfigView;
import cn.abcyun.cis.clinic.model.Organ;
import cn.abcyun.cis.clinic.utils.YesOrNo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.beans.BeanUtils;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-01 17:16:56
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class OrganListView extends OrganFullView {

    @ApiModelProperty(value = "门店商品权限")
    private ClinicGoodsDataPermissionView clinicGoodsPermission;

    public static OrganListView from(Organ organ, GoodsConfigView goodsConfig, ClinicGoodsDataPermissionView permissionView) {
        if (organ == null) {
            return null;
        }
        OrganListView result = new OrganListView();
        BeanUtils.copyProperties(organ, result);
        if (goodsConfig == null || permissionView == null) {
            return result;
        }
        result.setClinicGoodsPermission(permissionView);
        if (!goodsConfig.enableClinicSelfPrice(organ.getId())) {
            permissionView.setGoodsAdjustPriceConfig(null);
        }
        if (!goodsConfig.enableClinicCreateArchive(organ.getId())) {
            permissionView.setGoodsCreateArchivesConfig(null);
        }
        if (!goodsConfig.enableClinicUpdateArchive(organ.getId())) {
            permissionView.setGoodsModifyArchivesConfig(null);
        }
        if (!goodsConfig.enableClinicDeleteArchive(organ.getId())) {
            permissionView.setGoodsDeleteArchivesConfig(null);
        }
        return result;
    }
}
