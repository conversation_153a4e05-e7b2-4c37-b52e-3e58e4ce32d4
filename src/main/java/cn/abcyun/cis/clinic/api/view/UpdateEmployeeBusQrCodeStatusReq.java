package cn.abcyun.cis.clinic.api.view;

import cn.abcyun.cis.clinic.dto.EmployeeBusQrCodeRedisDto;
import cn.abcyun.cis.commons.exception.ParamNotValidException;
import cn.abcyun.cis.commons.exception.ParamRequiredException;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * 修改业务场景二维码状态请求参数
 */
@Data
public class UpdateEmployeeBusQrCodeStatusReq {

    @ApiModelProperty("场景key，用来查询redis")
    private String qrCodeSceneKey;
    @ApiModelProperty("状态; 10：已扫；99：取消 cn.abcyun.cis.clinic.dto.EmployeeBusQrCodeRedisDto.Status")
    private int status;
    @ApiModelProperty("扫码用户id")
    private String scanEmployeeId;

    public void validateParams() {
        if (StringUtils.isBlank(qrCodeSceneKey)) {
            throw new ParamRequiredException("qrCodeSceneKey");
        }
        if (!Objects.equals(status, EmployeeBusQrCodeRedisDto.Status.SCANNED) &&
                !Objects.equals(status, EmployeeBusQrCodeRedisDto.Status.CANCELED)) {
            throw new ParamNotValidException("只能修改为。10：已扫；99：取消");
        }
        if (Objects.equals(status, EmployeeBusQrCodeRedisDto.Status.SCANNED)) {
            if (StringUtils.isBlank(scanEmployeeId)) {
                throw new ParamRequiredException("scanEmployeeId");
            }
        }
    }
}
