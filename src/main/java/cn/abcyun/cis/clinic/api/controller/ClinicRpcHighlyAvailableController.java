package cn.abcyun.cis.clinic.api.controller;

import cn.abcyun.bis.rpc.sdk.cis.model.clinic.CreateZoneTaskReq;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.GetRegionEnvZoneCountRsp;
import cn.abcyun.cis.clinic.api.view.BaseSuccessRsp;
import cn.abcyun.cis.clinic.api.view.UpdateGrayOrganReq;
import cn.abcyun.cis.clinic.service.HighlyAvaliableService;
import cn.abcyun.cis.core.aop.LogReqAndRsp;
import cn.abcyun.common.model.AbcListPage;
import cn.abcyun.common.model.AbcServiceResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 高可用服务
 */
@RestController
@RequestMapping("/rpc/v3/clinics/ha")
public class ClinicRpcHighlyAvailableController {
    @Autowired
    private HighlyAvaliableService highlyAvaliableService;


    /**
     * 修改门店账号多端登录设置
     */
    @PostMapping("/update-gray-organ")
    @LogReqAndRsp
    public AbcServiceResponse<BaseSuccessRsp> updateGrayOrgan(@RequestBody UpdateGrayOrganReq req) {
        req.checkParam();

        if (req.getType() == 0) {
            highlyAvaliableService.updateGrayOrgan(req);
        } else if (req.getType() == 10) {
            highlyAvaliableService.refreshGrayOrganCore(req.getNotifyToPc() == 1, req.getNotifyGatewayChannel());
        }
        return new AbcServiceResponse<>(new BaseSuccessRsp());
    }

    /**
     * 创建可用分区放量任务
     */
    @PostMapping("/zone-task")
    @LogReqAndRsp
    public AbcServiceResponse<CreateZoneTaskReq> createZoneTask(@RequestBody CreateZoneTaskReq req) {
        return new AbcServiceResponse<>(highlyAvaliableService.createZoneTask(req));
    }
    /**
     * 修改可用分区放量任务状态
     */
    @PutMapping("/zone-task/{taskId}/status")
    @LogReqAndRsp
    public AbcServiceResponse<CreateZoneTaskReq> updateZoneTaskStatus(@PathVariable("taskId") String taskId, @RequestParam("status") int status) {
        return new AbcServiceResponse<>(highlyAvaliableService.updateZoneTaskStatus(Long.parseLong(taskId), status));
    }

    @GetMapping("/zone-task/{taskId}")
    public AbcServiceResponse<CreateZoneTaskReq> getZoneTask(@PathVariable String taskId) {
        return new AbcServiceResponse<>(highlyAvaliableService.getZoneTask(Long.parseLong(taskId)));
    }
    @GetMapping("/zone-task/region-chain-count")
    @LogReqAndRsp
    public AbcServiceResponse<AbcListPage<GetRegionEnvZoneCountRsp>> getRegionChainCount() {
        return new AbcServiceResponse<>(highlyAvaliableService.getRegionChainCount());
    }



    /**
     * 查询可用分区放量任务列表
     */
    @GetMapping("/zone-task")
    public AbcServiceResponse<AbcListPage<CreateZoneTaskReq>> listZoneTasks() {
        return new AbcServiceResponse<>(highlyAvaliableService.listZoneTasks());
    }
}
