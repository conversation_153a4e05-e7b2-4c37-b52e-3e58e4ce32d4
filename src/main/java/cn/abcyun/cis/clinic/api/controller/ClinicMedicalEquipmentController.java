package cn.abcyun.cis.clinic.api.controller;

import cn.abcyun.cis.clinic.api.view.ClinicMedicalEquipmentSaveReq;
import cn.abcyun.cis.clinic.api.view.ClinicMedicalEquipmentView;
import cn.abcyun.cis.clinic.service.ClinicMedicalEquipmentService;
import cn.abcyun.cis.commons.jwt.CisJWTUtils;
import cn.abcyun.common.model.AbcListPage;
import cn.abcyun.common.model.AbcServiceResponse;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 医疗设备管理
 * <AUTHOR>
 * @date 2021/11/16 11:09
 */

@RestController
@RequestMapping("/api/v3/clinics/equipment")
@Api(value = "ClinicMedicalEquipmentController", description = "医疗设备管理", produces = "application/json")
public class ClinicMedicalEquipmentController {
    @Autowired
    private ClinicMedicalEquipmentService equipmentService;

    @PostMapping
    public AbcServiceResponse<ClinicMedicalEquipmentView> insertEquipment(@RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                          @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                          @Valid @RequestBody ClinicMedicalEquipmentSaveReq req){
        ClinicMedicalEquipmentView view = equipmentService.insertEquipment(clinicId, req, employeeId);
        return new AbcServiceResponse<>(view);
    }

    @GetMapping("/{id}")
    public AbcServiceResponse<ClinicMedicalEquipmentView> getEquipment(@PathVariable Integer id) {
        ClinicMedicalEquipmentView view = equipmentService.getEquipment(id);
        return new AbcServiceResponse<>(view);
    }

    @PutMapping("/{id}")
    public AbcServiceResponse<ClinicMedicalEquipmentView> updateEquipment(@PathVariable Integer id,
                                                                          @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                          @Valid @RequestBody ClinicMedicalEquipmentSaveReq req) {
        ClinicMedicalEquipmentView view = equipmentService.updateEquipment(id, employeeId, req);
        return new AbcServiceResponse<>(view);
    }

    @DeleteMapping("/{id}")
    public AbcServiceResponse<Integer> deleteEquipment(@PathVariable Integer id) {
        equipmentService.deleteEquipment(id);
        return new AbcServiceResponse<>(id);
    }

    @GetMapping("/list")
    public AbcServiceResponse<AbcListPage<ClinicMedicalEquipmentView>> listEquipments(@RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                      @RequestParam(required = false, defaultValue = "0") Integer offset,
                                                                                      @RequestParam(required = false, defaultValue = "10") Integer limit){
        AbcListPage<ClinicMedicalEquipmentView> result = equipmentService.listEquipments(clinicId, offset, limit);
        return new AbcServiceResponse<>(result);
    }

}
