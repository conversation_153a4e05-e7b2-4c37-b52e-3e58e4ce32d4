package cn.abcyun.cis.clinic.api.view;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/4/7 11:17
 */
@Data
@ApiModel(value = "人员数据权限")
public class ClinicEmployeeDataPermission {

    @ApiModelProperty(value = "系统设置权限")
    private Settings settings;

    @ApiModelProperty(value = "收费权限")
    private Cashier cashier;

    @ApiModelProperty(value = "工作台权限")
    private Dashboard dashboard;

    @ApiModelProperty(value = "库存权限")
    private Inventory inventory;

    @ApiModelProperty(value = "门诊权限")
    private Outpatient outpatient;

    @ApiModelProperty(value = "统计权限")
    private Statistics statistics;

    @ApiModelProperty(value = "药房权限")
    private Pharmacy pharmacy;

    @ApiModelProperty(value = "患者权限")
    private Crm crm;

    @ApiModelProperty(value = "执行站权限")
    private Nurse nurse;

    @ApiModelProperty(value = "商城权限")
    private Mall mall;

    @ApiModelProperty(value = "医保权限")
    private MedicalInsurance medicalInsurance;

    @ApiModelProperty(value = "挂号预约权限")
    private Registration registration;

    @ApiModelProperty(value = "微诊所权限")
    private MicroClinic microClinic;

    @ApiModelProperty(value = "零售权限")
    private Retail retail;

    @Data
    public static class Cashier {

        @ApiModelProperty(value = "收费-能否查看药品进价: 0 不能 1 能")
        private int isCanSeeGoodsCostPrice;

        @ApiModelProperty(value = "收费-能否查看查看患者就诊历史: 0 不能 1 能")
        private int isCanSeePatientHistory;

        @ApiModelProperty(value = "收费-能否查看查看患者手机号: 0 不能 1 能")
        private int isCanSeePatientMobile;

        @ApiModelProperty(value = "收费-能否查看修改支付方式的权限: 0 不能 1 能")
        private int isCanSeeModifyPayMode;
    }

    @Data
    public static class Dashboard {
        @ApiModelProperty(value = "工作台-收费员查看账目: 0 不允许查看账目 1 只允许查看个人经手账目 2 允许查看门店全部账目")
        private int chargerPermission;

        @ApiModelProperty(value = "工作台-医生查看诊疗收入: 0 不允许查看个人诊疗收入 1 允许查看个人诊疗收入")
        private int doctorOutpatientFee;

        @ApiModelProperty(value = "工作台-医生查看挂号收入: 0 不允许查看个人挂号收入 1 允许查看个人挂号收入")
        private int doctorRegistrationFee;

        @ApiModelProperty(value = "工作台-医生查看患者看板: 0 只允许查看自己的患者看板 1 允许查看当天所有患者看板 2不允许查看患者看板")
        private int kanbanPermission;

        @ApiModelProperty(value = "工作台-医生查看执行金额: 0 不允许查看个人执行金额 1 允许查看个人执行金额")
        private int doctorExecuteFee;
    }

    @Data
    public static class Inventory {

        @ApiModelProperty(value = "库存-查看药品物资成本: 0 不能 1 能")
        private int isCanSeeGoodsCost;

        @ApiModelProperty(value = "库存-查看药品物资毛利: 0 不能 1 能")
        private int isCanSeeGoodsProfit;

        @ApiModelProperty(value = "库存-查看领用药品价格: 0 不能 1 能")
        private int isCanSeeObtainGoodsPrice;

        @ApiModelProperty(value = "库存-查看调拨药品价格: 0 不能 1 能")
        private int isCanSeeTransGoodsPrice;

        @ApiModelProperty(value = "库存-查看报损药品价格: 0 不能 1 能")
        private int isCanSeeDamageGoodsPrice;

        @ApiModelProperty(value = "库存-查看盘点药品价格: 0 不能 1 能")
        private int isCanSeeCheckGoodsPrice;

        @ApiModelProperty(value = "库存-新建/编辑药品档案: 0 不能 1 能")
        @Deprecated
        private int isCanOperateGoodsArchives;

        @ApiModelProperty(value = "库存-定价/调价: 0 不能 1 能")
        private int isCanOperateGoodsAdjustPrice;

        @ApiModelProperty(value = "库存-查看生产出库药品价格: 0 不能 1 能")
        private int isCanSeeProductionGoodsPrice;

        @ApiModelProperty(value = "库存-新建药品档案: 0 不能 1 能")
        private int isCanCreateGoodsArchives;

        @ApiModelProperty(value = "库存-编辑药品档案: 0 不能 1 能")
        private int isCanModifyGoodsArchives;

        @ApiModelProperty(value = "库存-删除药品档案: 0 不能 1 能")
        private int isCanDeleteGoodsArchives;
    }

    @Data
    public static class Outpatient {
        @ApiModelProperty(value = "门诊-医生查看药品价格: 0 只允许查看药品总价 1 允许查看药品明细，总价 2 不允许查看药品明细，总价")
        private int goodsPrice;

        @ApiModelProperty(value = "门诊-医生查看历史处方: 0 只允许查看自己开出的历史处方 1 允许查看患者所有的历史处方 2 不允许查看")
        private int historyPrescription;

        @ApiModelProperty(value = "门诊-医生查看药品进价: 0 不允许查看药品进价 1 允许查看药品进价（议价时便于参考）")
        private int goodsCostPrice;

        @ApiModelProperty(value = "门诊-医生查看处方价格: 0 不允许 1 允许")
        private int isCanSeePrescriptionPrice;

        @ApiModelProperty(value = "门诊-医生查看门诊单总价: 0 不允许 1 允许")
        private int isCanSeeTotalPrice;

        @ApiModelProperty(value = "门诊-医生查看药品进价: 0 不允许 1 允许")
        private int isCanSeeGoodsCostPrice;

        @ApiModelProperty(value = "门诊-能否查看查看患者手机号: 0 不能 1 能")
        private int isCanSeePatientMobile;

        @ApiModelProperty(value = "能否查看修改取消预约挂号的权限: 0 不能 1 能")
        private int isCanModifyRegistration;
    }

    @Data
    public static class Statistics {

        @ApiModelProperty(value = "统计-查看药品物资成本: 0 不能 1 能")
        private int isCanSeeGoodsCost;

        @ApiModelProperty(value = "统计-查看药品物资毛利: 0 不能 1 能")
        private int isCanSeeGoodsProfit;

        @ApiModelProperty(value = "统计-查看患者手机号: 0 不能 1 能")
        private int isCanSeePatientMobile;
    }

    @Data
    public static class Pharmacy {
        @ApiModelProperty(value = "药房-发药员查看患者就诊历史: 0 不能 1 能")
        private int isCanSeePatientHistory;
        @ApiModelProperty(value = "药房-能否查看查看患者手机号: 0 不能 1 能")
        private int isCanSeePatientMobile;
    }

    @Data
    public static class Crm {
        @ApiModelProperty(value = "患者-能否查看所有患者: 0 不能 1 能")
        private int isCanSeeAllPatients;
        @ApiModelProperty(value = "患者-能否查看查看患者手机号: 0 不能 1 能")
        private int isCanSeePatientMobile;
        @ApiModelProperty(value = "患者-能否查看查看患者消费金额: 0 不能 1 能")
        private int isCanSeePatientPayAmount;
        @ApiModelProperty(value = "患者-能否修改首诊来源: 0 不能 1 能")
        private int isCanModifyFirstFromAway;
        @ApiModelProperty(value = "患者-能否修改患者姓名: 0 不能 1 能")
        private int isCanModifyName;
        @ApiModelProperty(value = "患者-能否修改档案号: 0 不能 1 能")
        private int isCanModifySn;
        @ApiModelProperty(value = "患者-能否修改患者标签: 0 不能 1 能")
        private int isCanModifyTag;
        @ApiModelProperty(value = "患者-能否修改身份证号: 0 不能 1 能")
        private int isCanModifyIdCard;
        @ApiModelProperty(value = "患者-能否查看会员毛利率: 0 不能 1 能")
        private int isCanSeePatientProfit;
        @ApiModelProperty(value = "患者-能否修改患者积分: 0 不能 1 能")
        private int isCanModifyPoint;
        @ApiModelProperty(value = "患者-能否修改患者优惠劵: 0 不能 1 能")
        private int isCanModifyCoupon;
        @ApiModelProperty(value = "患者-能否查看所有历史处方: 0不能 1能")
        private int isCanSeeAllHistoryPrescription;
    }

    @Data
    public static class Nurse {
        @ApiModelProperty(value = "查看已执行的单据: 0 能查看所有 1 只能查看自己参与的（开单或执行） 2 不能查看 ")
        private int executedSheetDetail;
        @ApiModelProperty(value = "查看患者就诊历史: 1允许 0不允许")
        private int medicalHistory;
        @ApiModelProperty(value = "能否查看查看患者手机号: 0 不能 1 能")
        private int isCanSeePatientMobile;
        @ApiModelProperty(value = "查看历史执行单: 0 允许所有人查看 1 只允许参与人查看(包括开单人及执行人) 2 不能查看")
        private int historySheet;
    }

    @Data
    public static class Mall {

        @ApiModelProperty(value = "商城-查看商品价格: 0 不能 1 能")
        private int isCanSeeGoodsPrice;

        @ApiModelProperty(value = "商城-采购商品: 0 不能 1 能")
        private int isCanPurchaseGoods;
    }

    @Data
    public static class MedicalInsurance {

        @ApiModelProperty(value = "医保-查看医保主页: 0 不能 1 能")
        private int isCanSeeHomePage;

        @ApiModelProperty(value = "医保-查看医保账目数据: 0 不能 1 能")
        private int isCanSeeAccount;

        @ApiModelProperty(value = "医保-查看业务登记数据: 0 不能 1 能")
        private int isCanSeeBusinessRegistrationRecord;

        @ApiModelProperty(value = "医保-查看机构资料数据: 0 不能 1 能")
        private int isCanSeeProfileData;

        @ApiModelProperty(value = "医保-查看医保设置信息: 0 不能 1 能")
        private int isCanSeeSetupInformation;
    }

    @Data
    public static class Registration {

        @ApiModelProperty(value = "查看患者就诊历史: 1允许 0不允许")
        private int medicalHistory;

        @ApiModelProperty(value = "能否查看查看患者手机号: 0 不能 1 能")
        private int isCanSeePatientMobile;

        @ApiModelProperty(value = "能否查看修改支付方式的权限: 0 不能 1 能")
        private int isCanSeeModifyPayMode;

        @ApiModelProperty(value = "能否查看修改取消预约挂号的权限: 0 不能 1 能")
        private int isCanSeeModifyRegistration;
    }

    @Data
    public static class Settings {

        @ApiModelProperty(value = "修改成员姓名")
        private int isCanModifyEmployeeName;
    }

    @Data
    public static class MicroClinic {

        @ApiModelProperty(value = "能否查看查看首页数据: 0不能 1能")
        private int isCanSeeHomePageData;
    }


    @Data
    public static class Retail {

        @ApiModelProperty(value = "零售记录-毛利/毛利率配置: 0 不能 1 能")
        private int isCanSeeProfit;

        @ApiModelProperty(value = "零售开单-查看进价: 0 不能 1 能")
        private int isCanSeeGoodsCostPrice;

        @ApiModelProperty(value = "零售开单-查看毛利率: 0 不能 1 能")
        private int isCanSeeChargeProfit;

        @ApiModelProperty(value = "零售-操作患者余额: 0 不能 1 能")
        private int isCanOptPatientBalance;

        @ApiModelProperty(value = "零售-操作患者积分: 0 不能 1 能")
        private int isCanOptPatientPoints;

        @ApiModelProperty(value = "零售-操作患者优惠券: 0 不能 1 能")
        private int isCanOptPatientCoupon;

        @ApiModelProperty(value = "零售-查看患者毛利率: 0 不能 1 能")
        private int isCanSeePatientProfit;

    }
}
