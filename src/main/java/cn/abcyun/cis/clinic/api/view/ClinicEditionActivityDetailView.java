package cn.abcyun.cis.clinic.api.view;

import cn.abcyun.cis.clinic.model.ClinicEditionActivity;
import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.Instant;
import java.util.List;

/**
 * 诊所版本活动详情视图
 * <AUTHOR>
 * @date 2025-11-06
 */
@Data
public class ClinicEditionActivityDetailView {

    @ApiModelProperty(value = "活动ID")
    private String id;

    @ApiModelProperty(value = "活动名称")
    private String name;

    @ApiModelProperty(value = "赠送时长, 格式ISO-8601 period formats, P1M(1个月)")
    private String period;

    @ApiModelProperty(value = "活动开始时间")
    private Instant beginDate;

    @ApiModelProperty(value = "活动结束时间")
    private Instant endDate;

    @ApiModelProperty(value = "活动是否有效（启用且在有效期内）")
    private int isValid;

    @ApiModelProperty(value = "活动配置信息(JSON格式)")
    private JsonNode activityConfigs;

    @ApiModelProperty(value = "等价金额信息(JSON格式)")
    private List<ClinicEditionActivity.EquivalentAmount> equivalentAmount;

}
