package cn.abcyun.cis.clinic.api.view;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class CooperationClinicAccountOrderCalculateRsp {
    private String clinicId;

    @ApiModelProperty(value = "购买店版本id")
    private String editionId;

    @ApiModelProperty(value = "当前已购账号数")
    private int cooperationClinicCount;

    @ApiModelProperty(value = "当前已增购账号数")
    private int additionalAccountCount;

    @ApiModelProperty(value = "开始时间")
    private String beginDate;

    @ApiModelProperty(value = "结束时间")
    private String endDate;

    @ApiModelProperty(value = "本次增购账号数")
    private int count;

    @ApiModelProperty(value = "总价")
    private BigDecimal totalPrice;

    @ApiModelProperty(value = "抵扣费用")
    private BigDecimal deductionFee;

    @ApiModelProperty(value = "应收费用")
    private BigDecimal receivableFee;

    @ApiModelProperty(value = "基础账号数")
    @JsonProperty("basicCooperationClinicCount")
    public int getBasicCooperationClinicCount() {
        return cooperationClinicCount - additionalAccountCount;
    }

    @ApiModelProperty(value = "年数")
    private int years;

    @ApiModelProperty(value = "天数")
    private long days;

    @ApiModelProperty(value = "账号日单价")
    private BigDecimal dayPrice;

    /**
     * 0: 按年收费
     * 1: 按天收费
     */
    private int calculateType;

    @ApiModelProperty(value = "绑定账号数")
    private int bindCount;

}
