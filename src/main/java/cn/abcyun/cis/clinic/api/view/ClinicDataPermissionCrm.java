package cn.abcyun.cis.clinic.api.view;

import cn.abcyun.cis.clinic.model.ClinicDataPermissionSpecifiedEmployee;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/4/7 11:17
 */
@Data
@ApiModel(value = "患者权限")
public class ClinicDataPermissionCrm {

    @ApiModelProperty(value = "医生查看患者信息: 1查看全部患者 0仅查看自己接诊过的患者")
    private int doctorPatients;

    @ApiModelProperty(value = "执行人查看患者信息：1查看全部患者 0仅查看自己参与过的患者")
    private int executorPatients;

    @ApiModelProperty(value = "医生查看患者历史处方：1 允许查看患者所有的历史处方 0 只允许查看自己开出的历史处方")
    private int historyPrescription;

    @ApiModelProperty(value = "查看患者手机号")
    private ClinicDataPermissionSpecifiedEmployee patientMobile;

    @ApiModelProperty(value = "修改首诊来源")
    private ClinicDataPermissionSpecifiedEmployee modifyFirstFromAway;

    @ApiModelProperty(value = "查看患者消费金额")
    private ClinicDataPermissionSpecifiedEmployee patientPayAmount;

    @ApiModelProperty(value = "修改患者姓名")
    private ClinicDataPermissionSpecifiedEmployee modifyName;
    @ApiModelProperty(value = "修改患者身份证号")
    private ClinicDataPermissionSpecifiedEmployee modifyIdCard;
    @ApiModelProperty(value = "修改患者档案号")
    private ClinicDataPermissionSpecifiedEmployee modifySn;
    @ApiModelProperty(value = "修改患者标签")
    private ClinicDataPermissionSpecifiedEmployee modifyTag;
    @ApiModelProperty(value = "查看会员毛利率")
    private ClinicDataPermissionSpecifiedEmployee patientProfit;

    @ApiModelProperty(value = "修改患者积分")
    private ClinicDataPermissionSpecifiedEmployee modifyPoint;
    @ApiModelProperty(value = "修改患者优惠劵")
    private ClinicDataPermissionSpecifiedEmployee modifyCoupon;

}
