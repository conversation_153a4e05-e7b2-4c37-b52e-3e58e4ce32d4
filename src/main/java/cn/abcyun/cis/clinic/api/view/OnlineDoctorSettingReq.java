package cn.abcyun.cis.clinic.api.view;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/1/12 11:04
 */
@Data
public class OnlineDoctorSettingReq {

    @ApiModelProperty(value = "默认网诊门店")
    @NotEmpty(message = "默认网诊门店不能为空")
    private String clinicId;

    @ApiModelProperty(value = "默认网诊费")
    @NotNull(message = "默认网诊费不能为空")
    @DecimalMin(value = "0", message = "网诊费不能小于0")
    private BigDecimal fee;

    @ApiModelProperty(value = "查看灵活配置，默认关闭。:0=关闭,1=打开")
    private Integer viewPermission;

}
