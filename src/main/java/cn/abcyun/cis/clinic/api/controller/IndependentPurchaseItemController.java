package cn.abcyun.cis.clinic.api.controller;

import cn.abcyun.cis.clinic.api.view.PurchaseItemPayOrderView;
import cn.abcyun.cis.clinic.api.view.purchase.*;
import cn.abcyun.cis.clinic.service.purchase.IndependentPurchaseItemFacadeService;
import cn.abcyun.cis.commons.jwt.CisJWTUtils;
import cn.abcyun.cis.core.aop.LogReqAndRsp;
import cn.abcyun.common.model.AbcServiceResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2025/1/13 上午9:53
 * @description 独立购买项的控制层
 */
@Slf4j
@RestController
@RequestMapping("/api/v3/clinics/independent-purchase-item")
@Api(value = "IndependentPurchaseItemController", tags = "独立购买项", produces = "application/json")
public class IndependentPurchaseItemController {

    @Autowired
    private IndependentPurchaseItemFacadeService independentPurchaseItemFacadeService;

    @LogReqAndRsp
    @PostMapping("/configs")
    @ApiOperation(value = "批量获取独立购买项key集合的基础配置信息", produces = "application/json")
    public AbcServiceResponse<IndependentPurchaseItemConfigListView> getIndependentPurchaseConfigList(
            @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String chainId,
            @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) Integer nodeType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) Integer hisType,
            @RequestBody @Valid IndependentPurchaseItemConfigBatchReq batchReq) {
        return new AbcServiceResponse<>(independentPurchaseItemFacadeService.getIndependentPurchaseConfigList(chainId, clinicId, nodeType, hisType, batchReq));
    }

    @LogReqAndRsp
    @PostMapping("/calculate/fee")
    @ApiOperation(value = "计算独立购买项的价格数据信息", produces = "application/json")
    public AbcServiceResponse<IndependentPurchaseItemBatchRsp> calculate(
            @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String chainId,
            @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) Integer nodeType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) Integer hisType,
            @RequestBody @Valid IndependentPurchaseItemBatchReq batchReq) {
        return new AbcServiceResponse<>(independentPurchaseItemFacadeService.calculate(chainId, clinicId, nodeType, hisType, batchReq));
    }

    @LogReqAndRsp
    @PostMapping("/create/pay-order")
    @ApiOperation(value = "创建独立购买项的支付订单信息", produces = "application/json")
    public AbcServiceResponse<IndependentPurchaseItemPayOrderView> createPayOrder(
            @RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) Integer nodeType,
            @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) Integer hisType,
            @RequestHeader(CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String operationId,
            @RequestBody @Valid IndependentPurchaseItemBatchReq batchReq) {
        return new AbcServiceResponse<>(independentPurchaseItemFacadeService.createPayOrder(chainId, clinicId, nodeType, hisType, operationId, batchReq));
    }

    @LogReqAndRsp
    @GetMapping("/order/waiting-paid")
    @ApiOperation(value = "获取独立购买项待支付订单信息", produces = "application/json")
    public AbcServiceResponse<PurchaseItemPayOrderView> getWaitingPaidClinicIndependentPurchaseItemOrder(
            @RequestHeader(CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
            @RequestHeader(CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
            @RequestParam(value = "purchaseKey") String purchaseKey) {
        return new AbcServiceResponse<>(independentPurchaseItemFacadeService.getWaitingPaidClinicIndependentPurchaseItemOrder(chainId, clinicId, purchaseKey));
    }
}
