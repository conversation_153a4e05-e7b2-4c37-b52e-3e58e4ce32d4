package cn.abcyun.cis.clinic.api.view;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @date 2023/2/13 16:57
 */
@ApiModel
@EqualsAndHashCode(callSuper = true)
@Data
public class ClinicBusinessPermissionUpsertReq extends ClinicBusinessPermissionApiUpsertReq {
    @ApiModelProperty(value = "连锁id")
    @NotEmpty(message = "chainId 不能为空")
    private String chainId;
    @ApiModelProperty(value = "门店id")
    @NotEmpty(message = "clinicId 不能为空")
    private String clinicId;
    @ApiModelProperty(value = "操作人id")
    @NotEmpty(message = "operatorId 不能为空")
    private String operatorId;
}
