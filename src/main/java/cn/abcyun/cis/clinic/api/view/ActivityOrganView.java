package cn.abcyun.cis.clinic.api.view;

import cn.abcyun.cis.clinic.model.Organ;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/5/7 10:51
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ActivityOrganView extends OrganBasicView {


    @ApiModelProperty(value = "门店是否已参与活动：0未参与 1参与")
    private int isParticipated;

    @ApiModelProperty(value = "门店活动减免折合费用")
    private BigDecimal equivalentAmount = BigDecimal.ZERO;

    @JsonIgnore
    private String source; //来源

    @ApiModelProperty(value = "门店是否能领取活动：0不能 1可以")
    @JsonProperty(value = "isSupportObtained")
    public int getIsSupportObtained() {
        if (StringUtils.isEmpty(source)) {
            return 1;
        } else if (source.equals(Organ.Source.CHU_TIAN_YUN)) {
            return 0;
        } else if (source.equals(Organ.Source.CHU_TIAN_YUN_BEFORE)) {
            return 0;
        } else {
            return 1;
        }
    }
}
