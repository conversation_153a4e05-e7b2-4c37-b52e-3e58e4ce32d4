package cn.abcyun.cis.clinic.api.view;

import cn.abcyun.cis.clinic.model.GrayOrgan;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotEmpty;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Data
public class UpdateFrontendVersionReq {

    @NotEmpty(message = "tag 不能为空")
    private String tag;

    @NotEmpty(message = "env 不能为空")
    private String env;

    private List<String> zones;

    @NotEmpty(message = "serviceName 不能为空")
    private String serviceName;

    @NotEmpty(message = "regionId 不能为空")
    private String regionId;

    public List<String> getNormativeZones() {
        if (CollectionUtils.isEmpty(zones)) {
            return Arrays.asList(GrayOrgan.Zone.PRIMARY);
        }

        return zones.stream()
                .filter(zone -> GrayOrgan.Zone.ALL_ZONES.contains(zone))
                .distinct()
                .collect(Collectors.toList());
    }
}
