package cn.abcyun.cis.clinic.api.view;

import cn.abcyun.cis.clinic.model.Department;
import cn.abcyun.cis.commons.util.DateUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.StringUtils;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.time.Instant;
import java.util.List;

/**
 * 新增科室
 * <AUTHOR>
 * @date 2021/11/10 16:59
 */
@Data
public class DepartmentSaveReq {

    private Integer beds;

    private String customId;

    @Deprecated
    private List<String> employeeIds;

    @Valid
    private List<DepartmentEmployeeReq> employeeList;

    private int isClinical;

    private String mainMedical;

    private String mobile;

    @NotEmpty(message = "科室名称 name 不能为空")
    private String name;

    private String principal;

    private String secondMedical;

    private String startDate;

    private int type;

    @JsonProperty(value = "force")
    private boolean force;

    @ApiModelProperty(value = "科室地址")
    private String departmentAddress;

    public Instant getStartDate() {
        if (StringUtils.isEmpty(startDate)) {
            return null;
        }
        // 字符串 to Instant
        return DateUtils.getBeginOfDay(startDate);
    }

    public int getType() {
        // 非诊疗科室一定不是门诊科室
        if (isClinical == 0) {
            type = Department.Type.NORMAL_DEPARTMENT;
        }
        return type;
    }

    public String getName() {
        return name.trim();
    }
}
