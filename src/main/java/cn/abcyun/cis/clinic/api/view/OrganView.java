package cn.abcyun.cis.clinic.api.view;

import cn.abcyun.cis.clinic.model.Organ;
import cn.abcyun.cis.core.handler.SignatureUrlSerializer;
import cn.abcyun.scrm.rpc.sdk.rpc.model.corp.CorpView;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.beans.BeanUtils;
import org.springframework.util.StringUtils;

import java.time.Instant;
import java.util.List;
import java.util.Objects;

@EqualsAndHashCode(callSuper = true)
@Data
public class OrganView extends OrganBasicView {
    private int status;             //状态:1:正常；80:到期,禁用；>=90:删除
    private int level;              //级别: 0:试用;10:正常;20:付费
    private Instant expiredAt;      //过期时间
    private String shortNamePyFirst;
    private String namePy;
    private String namePyFirst;

    private String contactPhone;                //联系电话
    private String backupContactPhone;          //备用联系电话
    @JsonSerialize(using = SignatureUrlSerializer.class)
    private String logo;                        //诊所logo
    private String backupContactMobile;         //备用联系电话
    private String qrUrl;                       //微诊所二维码url

    private String addressProvinceId;
    private String addressProvinceName;
    private String addressCityId;
    private String addressCityName;
    private String addressDistrictId;
    private String addressDistrictName;
    private String addressDetail;
    private String addressGeo;                  //经纬度
    private Instant createdDate;
    private String scc;                         //统一社会信用代码
    private String nationalCode;                //国家机构编码
    private String source;                      //来源
    private int innerFlag;                      //内部标志
    private int editionStatus;                  //版本状态
    private int supervisionFlag;                //监管标志
    private String editionId;                   //版本
    private Integer classCode;                  // 医院等级（级）分类：1 一级， 2 二级 3 三级 9 未评级
    private Integer degreeCode;                 // 医院等级（等）分类：1 特等， 2 甲等 3 乙等 9 未评等
    private String category;                    // 诊所机构类型
    private JsonNode practiceSubject;           // 执业科目
    private String qwCorpId;                    // 企业微信id
    /**
     * 门店支持的业务线：位运算: 001普通 010口腔 100眼科
     */
//    @JsonIgnore
    private int busSupportFlag;

    @ApiModelProperty(value = "门店管理员列表")
    private List<EmployeeBasicView> adminList;

    @ApiModelProperty(value = "是否开通了企微管家")
    @JsonProperty("isOpenScrm")
    public int getIsOpenScrm() {
        return StringUtils.isEmpty(qwCorpId) ? 0 : 1;
    }

    @ApiModelProperty(value = "是否开通眼科")
    @JsonProperty("isOpenEyeBus")
    public int getIsOpenEyeBus() {
        return (busSupportFlag & Organ.Business.EYE) > 0 ? 1 : 0;
    }

    @ApiModelProperty(value = "是否开通口腔")
    @JsonProperty("isOpenOralBus")
    public int getIsOpenOralBus() {
        return (busSupportFlag & Organ.Business.DENTISTRY) > 0 ? 1 : 0;
    }

    /**
     * 企业微信信息
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private CorpView qwCorpInfo;

    /**
     * 门店现在的环境
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer env;

    private Instant lastModifiedDate;

    private String createdUserId;
    private String lastModifiedUserId;

    public static OrganView from(Organ organ) {
        if (Objects.isNull(organ)) {
            return null;
        }
        OrganView organView = new OrganView();
        BeanUtils.copyProperties(organ, organView);
        return organView;
    }

    public static class NodeType {
        public static final int INDEPENDENT_CLINIC = 0;
        public static final int CHAIN_HEAD_CLINIC = 1;
        public static final int CHAIN_BRANCH_CLINIC = 2;
    }

    public static class ClinicEditionStatus {
        public static final int NONE = 0;           //未购买
        public static final int BEFORE_VALID = 10;  //未到生效时间
        public static final int VALID = 20;         //正常
        public static final int AFTER_VALID = 30;   //已过期
    }
}
