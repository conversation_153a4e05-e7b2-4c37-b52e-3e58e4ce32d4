package cn.abcyun.cis.clinic.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.vladmihalcea.hibernate.type.json.JsonStringType;
import lombok.Data;
import org.hibernate.annotations.TypeDef;
import org.springframework.beans.BeanUtils;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.Objects;

@Data
@Entity
@Table(name = "v2_clinic_edition_order")
@TypeDef(name = "json", typeClass = JsonStringType.class)
public class ClinicEditionOrder {
    @Id
    private String id;
    private String editionPayOrderId;
    private int type;                   // 订单类型（1: 首购；2: 续费；3: 升级；4: 降级）
    private Integer organType;              // 门店类型（0: 单店；1: 连锁总店；2: 连锁子店）
    private String editionId;              // 购买店版本id
    private Instant beginDate;          // 生效开始时间
    private Instant endDate;            // 生效结束时间
    private int maxEmployeeCount;       // 最多支持雇员数
    private String unit;                // 单位（year）
    private BigDecimal unitPrice;       // 单位定价
    private int unitCount;              // 单位数量
    private BigDecimal totalPrice;      // 总价
    @Deprecated
    private BigDecimal discountPrice;   // 折扣
    @Deprecated
    private BigDecimal adjustmentPrice; // 议价金额
    private BigDecimal deductionFee;    // 抵扣费用
    private BigDecimal liJianFee = BigDecimal.ZERO;      // 立减费用，默认值为0
    @Deprecated
    private BigDecimal receivableFee;   // 应收费用 receivableFee = totalPrice + discountPrice + adjustmentPrice + deductionFee
    private BigDecimal paidFee;         // 支付费用
    private Instant paidTime;           // 支付时间
    private Integer payMode;            // 支付方式(2: 微信；3: 支付宝；100: 线下支付)
    private int status;                 // 状态(0: 未支付；10: 已支付)
    @Deprecated
    private Instant expiredTime;        // 订单失效时间
    private String customerId;          // 下单客户id(employee表id)
    private String bindChainId;
    private String bindClinicId;
    private String sellerId;            // 销售员id(abc-employee表id)
    private String sellerName;          // 销售员姓名
    private int source;                 // 订单来源(1：企业微信销售下单；2：用户网页下单)
    private String remarks;             // 备注
    @Deprecated
    private String payOrderId;          // 支付用的订单号
    @Deprecated
    private String offlinePaidSellerId; // 离线支付时的销售员id

    @org.hibernate.annotations.Type(type = "json")
    @Column(columnDefinition = "json")
    private List<ClinicEditionAttachment> attachments; // 附件

    private String deductionByOrderId; // 被哪个订单抵扣了

    @org.hibernate.annotations.Type(type = "json")
    @Column(columnDefinition = "json")
    @Deprecated
    private DeductionOrderInfo deductionOrderInfo; // 抵扣信息

    /**
     * 开票状态 {@link Constants.OrderInvoiceStatus}
     */
    private int invoiceStatus;

    /**
     * 开票申请ID
     */
    private String invoiceApplyId;

    private int isDeleted;              // 是否删除
    private Instant created;            // 创建时间
    private String createdBy;           // 创建人
    private String lastModifiedBy;      // 最后修改人
    private Instant lastModified;       // 最后修改时间

    private String referrerCode;      // 推荐码
    private String clueId;           // 线索id

    /**
     * {@link Constants.PayAccount}
     */
    private Integer payAccount; // 0:ABC 10:楚天云

    /**
     * 药店合作诊所最大数量
     */
    private Integer maxCooperationClinic;

    public ClinicEditionOrder copy() {
        ClinicEditionOrder order = new ClinicEditionOrder();
        BeanUtils.copyProperties(this, order);
        return order;
    }


    // 计算实际支付价格时要扣除抵扣金
    public BigDecimal getPurchasePrice() {
        return receivableFee.subtract(deductionFee);
    }

    public int getEditionIdValue() {
        return Integer.parseInt(editionId);
    }

    public long getTotalDays() {
        long totalDays = Duration.between(beginDate, endDate).toDays() + 1;
        return totalDays > 0 ? totalDays : 0;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DeductionOrderInfo {
        private List<DeductionOrder> deductionOrders;
        private BigDecimal deductionFee;    // 抵扣费用

        public BigDecimal getPaidFee() {
            return deductionOrders == null ? BigDecimal.ZERO : deductionOrders.stream().map(DeductionOrder::getPaidFee).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        public long getTotalDays() {
            return deductionOrders == null ? 0 : deductionOrders.stream().mapToLong(DeductionOrder::getTotalDays).sum();
        }

        public long getUsedDays() {
            return deductionOrders == null ? 0 : deductionOrders.stream().mapToLong(DeductionOrder::getUsedDays).sum();
        }
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DeductionOrder {
        private String id;
        private long totalDays;
        private long usedDays;
        private BigDecimal paidFee;
    }

    /**
     * 0:试用 1:首购 2:续费 3:升级 4:降级 5:SOP阶段续费
     */
    public static class Type {
        public static final int TRIAL = 0;
        public static final int FIRST_PURCHASE = 1;
        public static final int RENEW_PURCHASE = 2;
        public static final int UPGRADE_PURCHASE = 3;
        public static final int DOWNLOAD_PURCHASE = 4;
        /**
         * SOP 续费订单，即实施结束 30 天内后续费的订单。续费一年 9 折，续费两年 8 折
         */
        public static final int SOP_RENEW_PURCHASE = 5;
    }

    public static class OrganType {
        public static final int INDEPENDENT_CLINIC = 0;
        public static final int CHAIN_HEAD_CLINIC = 1;
        public static final int CHAIN_BRANCH_CLINIC = 2;
    }

    public static class Status {
        public static final int WAITING_PAY = 0;
        public static final int PAID = 10;
        /**
         * 已退款
         */
        public static final int REFUNDED = 20;
        public static final int CLOSED = 90;
    }


    public static String getTypeStr(int type) {
        switch (type) {
            case Type.TRIAL:
                return "试用";
            case Type.FIRST_PURCHASE:
                return "首购";
            case Type.RENEW_PURCHASE:
                return "续费";
            case Type.UPGRADE_PURCHASE:
                return "升级";
            case Type.DOWNLOAD_PURCHASE:
                return "降级";
            case Type.SOP_RENEW_PURCHASE:
                return "SOP续费";
            default:
                return "未知";

        }
    }

    public static String getEditionStr(String editionIdStr) {
        if (editionIdStr == null || editionIdStr.isEmpty()) {
            return "未知";
        }
        int editionId = Integer.parseInt(editionIdStr);
        switch (editionId) {
            case Constants.ClinicEditionId.BASIC:
            case Constants.ClinicEditionId.HOSPITAL_BASIC:
            case Constants.ClinicEditionId.PHARMACY_BASIC:
                return "基础版";
            case Constants.ClinicEditionId.PROFESSIONAL:
            case Constants.ClinicEditionId.HOSPITAL_PROFESSIONAL:
            case Constants.ClinicEditionId.PHARMACY_PROFESSIONAL:
            case Constants.ClinicEditionId.EYE_PROFESSIONAL:
                return "专业版";
            case Constants.ClinicEditionId.ULTIMATE:
            case Constants.ClinicEditionId.EYE_ULTIMATE:
                return "旗舰版";
            case Constants.ClinicEditionId.VIP:
            case Constants.ClinicEditionId.EYE_VIP:
                return "大客户版";
            case Constants.ClinicEditionId.EYE_BASIC:
                return "标准版";
            case Constants.ClinicEditionId.SCRM:
                return "企微管家";
            default:
                return "未知";
        }
    }

}
