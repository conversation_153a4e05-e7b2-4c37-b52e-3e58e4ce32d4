package cn.abcyun.cis.clinic.model;

import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.IdClass;
import javax.persistence.Table;

@Data
@Entity
@Table(name = "v2_clinic_chain_employee_tag")
@IdClass(DoctorTagEmployeeId.class)
public class DoctorTagEmployee {
    @Id
    private String chainId;         // 连锁id
    @Id
    private String employeeId;
    @Id
    private String tagId;

    private int sort;

    public DoctorTagEmployee() {

    }

    public DoctorTagEmployee(String chainId, String employeeId, String tagId) {
        this.chainId = chainId;
        this.employeeId = employeeId;
        this.tagId = tagId;
    }
}
