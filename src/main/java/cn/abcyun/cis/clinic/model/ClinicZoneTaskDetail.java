package cn.abcyun.cis.clinic.model;

import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.time.Instant;

@Data
@Entity
@Table(name = "v2_clinic_zone_task_detail")
public class ClinicZoneTaskDetail {

    @Id
    private Long id;
    private Long zoneTaskId;
    private String name;
    private BigDecimal regionCount;
    private int realRegionCount;
    private int status;
    private int countType;
    private Instant started;
    private Instant finished;
    private Instant created;

    public static final class Status {
        private Status() {
        }

        public static final int NEW = 0;
        public static final int EFFECTIVE = 10;
        public static final int FINISHED = 20;
        public static final int STOP = 30;
    }

    public static final class CountType {
        private CountType() {
        }

        public static final int QUANTITY = 0;
        public static final int RATIO = 1;
    }
}
