package cn.abcyun.cis.clinic.model;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.*;

/**
 * @Description  
 * <AUTHOR>
 * @Date 2022-12-05 19:47:12 
 */

@Data
@Accessors(chain = true)
@Entity
@Table(name ="sys_user")
public class SysUser {


	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private int id;

	/**
	 * 系统用户id
	 */
	private String userId;

	private String name;

}
