package cn.abcyun.cis.clinic.model;

import com.vladmihalcea.hibernate.type.json.JsonStringType;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;

import javax.persistence.*;
import java.time.Instant;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022-04-07 10:57:17
 */

@Data
@Accessors(chain = true)
@Entity
@Table(name = "v2_clinic_data_permission_config")
@TypeDef(name = "json", typeClass = JsonStringType.class)
public class ClinicDataPermissionConfig {


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;

    /**
     * 数据权限名称
     */
    private String name;

    /**
     * 数据权限的key，采用m-path结构
     */
    private String key;

    /**
     * 是否必填，0，非必填，1，必填
     */
    private Integer required;

    /**
     * 权限属性类型
     */
    private String type;

    /**
     * 权限值类型，目前支持：object,number,boolean,string
     */
    private String valueType;

    /**
     * 排序
     */
    private Integer order;

    /**
     * 权限值取值范围
     */
    @Type(type = "json")
    @Column(columnDefinition = "json")
    private List<Values> values;

    /**
     * 角色值取值范围
     */
    @Type(type = "json")
    @Column(columnDefinition = "json")
    private List<Integer> roleIds;

    /**
     * 权限默认值
     */
    private String defaultValue;

    /**
     * 普通诊所权限默认值
     */
    private String normalDefaultValue;

    /**
     * 口腔诊所权限默认值
     */
    private String oralDefaultValue;

    /**
     * 眼科诊所权限默认值
     */
    private String eyeDefaultValue;

    /**
     * 备注信息
     */
    private String comment;

    /**
     * 节点类型，0，父节点，1，子节点
     */
    private Integer nodeType;

    /**
     * 第一个key
     */
    private String keyFirst;

    /**
     * 第二个key
     */
    private String keySecond;

    /**
     * 第三个key
     */
    private String keyThird;

    /**
     * 第四个key
     */
    private String keyFourth;

    /**
     * 第五个key
     */
    private String keyFifth;

    /**
     * 位运算类型
     * 00000001:诊所管家 00000010:口腔管家 00000100:眼科管家 00001000:医院 00010000:药店
     */
    private int hisTypeFlag;

    /**
     * 位运算类型
     * 001:普通诊所 010:连锁店总店 100:连锁店分店
     */
    private int nodeTypeFlag;

    /**
     * 是否内置属性，0：不是内置属性，1：是内置属性
     */
    private Integer buildIn;

    /**
     * 删除状态，1，删除，0，未删除
     */
    private int isDeleted;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Instant created;

    /**
     * 最后修改人
     */
    private String lastModifiedBy;

    /**
     * 更新时间
     */
    private Instant lastModified;

    @Data
    public static class Values {
        private String label;
        private int value;

        // 有权限模块的可查看
        public static final String VALUE_ONLY_ASSIGN_AUTH = "0";
        // 仅管理员
        public static final String VALUE_ONLY_ADMIN_EMPLOYEE = "1";
        // 指定的人员
        public static final String VALUE_SPECIFIED_EMPLOYEE = "2";
        // 指定的角色
        public static final String VALUE_ROLE_EMPLOYEE = "3";
        // 所有人可见
        public static final String VALUE_ALL_EMPLOYEE = "4";
    }

    public static class Key {
        /**
         * 设置：修改成员手机号
         */
        public static final String SETTINGS_MODIFY_EMPLOYEE_NAME = "dataPermission.settings.modifyEmployeeName";
        /**
         * 收费：收费员查看药品进价
         * 药店：“零售单”列表中查看药品进价
         */
        public static final String CASHIER_GOODS_COST_PRICE = "dataPermission.cashier.goodsCostPrice";
        /**
         * 收费：收费员查看患者就诊历史
         */
        public static final String CASHIER_GOODS_PATIENT_HISTORY = "dataPermission.cashier.patientHistory";
        /**
         * 收费：查看患者手机号
         */
        public static final String CASHIER_PATIENT_MOBILE = "dataPermission.cashier.patientMobile";
        /**
         * 收费：修改挂号支付方式
         */
        public static final String CASHIER_MODIFY_PAY_MODE = "dataPermission.cashier.modifyPayMode";

        /**
         * 工作台：收费员查看账目
         */
        public static final String DASHBOARD_CHARGER_PERMISSION = "dataPermission.dashboard.chargerPermission";
        /**
         * 工作台：医生查看诊疗收入
         */
        public static final String DASHBOARD_DOCTOR_OUTPATIENT_FEE = "dataPermission.dashboard.doctorOutpatientFee";
        /**
         * 工作台：医生查看挂号收入
         */
        public static final String DASHBOARD_DOCTOR_REGISTRATION_FEE = "dataPermission.dashboard.doctorRegistrationFee";
        /**
         * 工作台：医生查看患者看板
         */
        public static final String DASHBOARD_KANBAN_PERMISSION = "dataPermission.dashboard.kanbanPermission";
        /**
         * 工作台：医生查看执行金额
         */
        public static final String DASHBOARD_DOCTOR_EXECUTE_FEE = "dataPermission.dashboard.doctorExecuteFee";

        /**
         * 库存：查看药品物资成本
         */
        public static final String INVENTORY_GOODS_COST = "dataPermission.inventory.goodsCost";
        /**
         * 库存：查看药品物资毛利
         */
        public static final String INVENTORY_GOODS_PROFIT = "dataPermission.inventory.goodsProfit";

        /**
         * 库存：领用查看药品金额
         */
        public static final String INVENTORY_OBTAIN_GOODS_PRICE = "dataPermission.inventory.obtainGoodsPrice";
        /**
         * 库存：调拨查看药品金额
         */
        public static final String INVENTORY_TRANS_GOODS_PRICE = "dataPermission.inventory.transGoodsPrice";
        /**
         * 库存：报损查看药品金额
         */
        public static final String INVENTORY_DAMAGE_GOODS_PRICE = "dataPermission.inventory.damageGoodsPrice";
        /**
         * 库存：盘点查看药品金额
         */
        public static final String INVENTORY_CHECK_GOODS_PRICE = "dataPermission.inventory.checkGoodsPrice";

        /**
         * 库存：新建/编辑药品档案
         */
        @Deprecated
        public static final String INVENTORY_GOODS_ARCHIVES = "dataPermission.inventory.goodsArchives";
        /**
         * 库存：新建药品档案
         */
        public static final String INVENTORY_GOODS_CREATE_ARCHIVES = "dataPermission.inventory.goodsCreateArchives";
        /**
         * 库存：编辑药品档案
         */
        public static final String INVENTORY_GOODS_MODIFY_ARCHIVES = "dataPermission.inventory.goodsModifyArchives";
        /**
         * 库存：删除药品档案
         */
        public static final String INVENTORY_GOODS_DELETE_ARCHIVES = "dataPermission.inventory.goodsDeleteArchives";

        /**
         * 库存：定价/调价
         */
        public static final String INVENTORY_GOODS_ADJUST_PRICE = "dataPermission.inventory.goodsAdjustPrice";

        /**
         * 门诊：医生查看药品价格
         */
        public static final String OUTPATIENT_GOODS_PRICE = "dataPermission.outpatient.goodsPrice";
        /**
         * 门诊：医生查看历史处方
         */
        public static final String OUTPATIENT_HISTORY_PRESCRIPTION = "dataPermission.outpatient.historyPrescription";
        /**
         * 门诊：医生查看处方价格
         */
        public static final String OUTPATIENT_PRESCRIPTION_PRICE = "dataPermission.outpatient.prescriptionPrice";
        /**
         * 门诊：医生查看门诊单总价
         */
        public static final String OUTPATIENT_TOTAL_PRICE = "dataPermission.outpatient.totalPrice";
        /**
         * 门诊：医生查看药品进价
         */
        public static final String OUTPATIENT_GOODS_COST_PRICE = "dataPermission.outpatient.goodsCostPrice";
        /**
         * 门诊：查看患者手机号
         */
        public static final String OUTPATIENT_PATIENT_MOBILE = "dataPermission.outpatient.patientMobile";
        /**
         * 门诊：新增修改挂号预约
         */
        public static final String OUTPATIENT_MODIFY_REGISTRATION = "dataPermission.outpatient.modifyRegistration";
        /**
         * 统计：查看药品物资成本
         */
        public static final String STATISTICS_GOODS_COST = "dataPermission.statistics.goodsCost";
        /**
         * 统计：查看药品物资毛利
         */
        public static final String STATISTICS_GOODS_PROFIT = "dataPermission.statistics.goodsProfit";
        /**
         * 统计：查看患者手机号
         */
        public static final String STATISTICS_PATIENT_MOBILE = "dataPermission.statistics.patientMobile";

        /**
         * 药房：发药员查看患者就诊历史
         */
        public static final String PHARMACY_PATIENT_HISTORY = "dataPermission.pharmacy.patientHistory";

        /**
         * 药房：查看患者手机号
         */
        public static final String PHARMACY_PATIENT_MOBILE = "dataPermission.pharmacy.patientMobile";

        /**
         * 患者：医生查看患者信息
         */
        public static final String CRM_DOCTOR_PATIENTS = "dataPermission.crm.doctorPatients";
        /**
         * 患者：执行人查看患者信息
         */
        public static final String CRM_EXECUTOR_PATIENTS = "dataPermission.crm.executorPatients";
        /**
         * 患者：查看患者手机号
         */
        public static final String CRM_PATIENT_MOBILE = "dataPermission.crm.patientMobile";
        /**
         * 患者：查看患者消费金额
         */
        public static final String CRM_PATIENT_PAY_AMOUNT = "dataPermission.crm.patientPayAmount";
        /**
         * 患者：患者档案号编辑
         */
        public static final String CRM_MODIFY_PATIENT_SN = "dataPermission.crm.modifySn";
        /**
         * 患者：患者标签编辑
         */
        public static final String CRM_MODIFY_PATIENT_TAG = "dataPermission.crm.modifyTag";
        /**
         * 患者：患者姓名编辑
         */
        public static final String CRM_MODIFY_PATIENT_NAME = "dataPermission.crm.modifyName";
        /**
         * 患者：患者身份证编辑
         */
        public static final String CRM_MODIFY_PATIENT_ID_CARD = "dataPermission.crm.modifyIdCard";
        /**
         * 患者：修改首诊来源
         */
        public static final String CRM_MODIFY_FIRST_FROM_AWAY = "dataPermission.crm.modifyFirstFromAway";
        /**
         * 患者：查看会员毛利率
         */
        public static final String CRM_PATIENT_PROFIT = "dataPermission.crm.patientProfit";
        /**
         * 患者：修改患者积分
         */
        public static final String CRM_MODIFY_PATIENT_POINTS = "dataPermission.crm.modifyPatientPoints";
        /**
         * 患者：修改患者优惠券
         */
        public static final String CRM_MODIFY_PATIENT_COUPON = "dataPermission.crm.modifyPatientCoupon";
        /**
         * 患者：医生查看历史处方
         */
        public static final String CRM_HISTORY_PRESCRIPTION = "dataPermission.crm.historyPrescription";

        /**
         * 执行站：查看已执行的单据详情
         */
        public static final String NURSE_EXECUTE_SHEET = "dataPermission.nurse.executedSheetDetail";
        /**
         * 执行站：查看历史单据
         */
        public static final String NURSE_HISTORY_SHEET = "dataPermission.nurse.historySheet";
        /**
         * 执行站：查看患者就诊历史
         */
        public static final String NURSE_MEDICAL_HISTORY = "dataPermission.nurse.medicalHistory";
        /**
         * 执行站：查看患者手机号
         */
        public static final String NURSE_PATIENT_MOBILE = "dataPermission.nurse.patientMobile";
        /**
         * 商城：查看商品价格
         */
        public static final String MALL_GOODS_PRICE = "dataPermission.mall.goodsPrice";
        /**
         * 商城：采购商品
         */
        public static final String MALL_GOODS_PURCHASE = "dataPermission.mall.goodsPurchase";

        /**
         * 医保：查看医保主页
         */
        public static final String MEDICAL_INSURANCE_VIEW_HOME = "dataPermission.medicalInsurance.viewHome";
        /**
         * 医保：查看医保账目数据
         */
        public static final String MEDICAL_INSURANCE_VIEW_ACCOUNT = "dataPermission.medicalInsurance.viewAccount";

        /**
         * 医保：查看业务登记记录
         */
        public static final String MEDICAL_INSURANCE_VIEW_BUSINESS_REGISTRATION_RECORD = "dataPermission.medicalInsurance.viewBusinessRegistrationRecord";

        /**
         * 医保：查看机构资料数据
         */
        public static final String MEDICAL_INSURANCE_VIEW_PROFILE_DATA = "dataPermission.medicalInsurance.viewProfileData";

        /**
         * 医保：查看医保设置信息
         */
        public static final String MEDICAL_INSURANCE_VIEW_SETUP_INFORMATION = "dataPermission.medicalInsurance.viewSetupInformation";

        /**
         * 挂号预约：查看患者就诊历史
         */
        public static final String REGISTRATION_MEDICAL_HISTORY = "dataPermission.registration.medicalHistory";

        /**
         * 挂号预约：查看患者手机号
         */
        public static final String REGISTRATION_PATIENT_MOBILE = "dataPermission.registration.patientMobile";

        /**
         * 挂号预约：修改挂号支付方式
         */
        public static final String REGISTRATION_MODIFY_PAY_MODE = "dataPermission.registration.modifyPayMode";

        /**
         * 微诊所查看首页数据
         */
        public static final String MICRO_CLINIC_HOME_PAGE = "dataPermission.microClinic.homePage";

        /**
         * 挂号预约：修改、取消挂号预约
         */
        public static final String REGISTRATION_MODIFY_REGISTRATION = "dataPermission.registration.modifyRegistration";

        /**
         * 库存：生产出库查看药品金额
         */
        public static final String INVENTORY_PRODUCTION_GOODS_PRICE = "dataPermission.inventory.productionGoodsPrice";
        /**
         * 零售：查看药品物资毛利
         */
        public static final String RETAIL_GOODS_PROFIT = "dataPermission.retail.profit";
        /**
         * 药店零售选择批次时查看进价
         */
        public static final String RETAIL_GOODS_COST_PRICE = "dataPermission.cashier.goodsCostPrice";
        /**
         * 零售余额充值/退款
         */
        public static final String RETAIL_PATIENT_BALANCE_OPT = "dataPermission.cashier.patientBalanceOpt";
        /**
         * 零售积分发放/抵扣
         */
        public static final String RETAIL_PATIENT_POINTS_OPT = "dataPermission.cashier.patientPointsOpt";
        /**
         * 零售优惠券发放
         */
        public static final String RETAIL_PATIENT_COUPON_OPT = "dataPermission.cashier.patientCouponOpt";
        /**
         * 零售查看患者毛利率
         */
        public static final String RETAIL_PATIENT_PROFIT = "dataPermission.retail.patientProfit";
        /**
         * 零售开单-查看毛利率
         */
        public static final String RETAIL_CHARGE_PROFIT_CONFIG = "dataPermission.retail.chargeProfitConfig";
    }


}
