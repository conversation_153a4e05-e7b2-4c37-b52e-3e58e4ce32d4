package cn.abcyun.cis.clinic.model.invoice;

import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.time.Instant;

/**
 * 开票申请项
 *
 * <AUTHOR>
 * @date 2023/7/20 17:57
 **/
@Data
@Entity
@Table(name = "v2_clinic_invoice_apply_item")
public class ClinicInvoiceApplyItem {

    /**
     * 开票申请项ID
     */
    @Id
    private String id;

    /**
     * 开票申请项ID
     */
    private String invoiceApplyId;

    /**
     * 连锁ID
     */
    private String chainId;

    /**
     * 诊所ID
     */
    private String clinicId;

    /**
     * 来源订单ID
     */
    private String sourceOrderId;

    /**
     * 开票项名称
     */
    private String name;

    /**
     * 单位数量
     */
    private BigDecimal unitCount;

    /**
     * 开票项含税总金额
     */
    private BigDecimal price;

    /**
     * 开票订单分类
     *
     * @see cn.abcyun.cis.clinic.model.Constants.InvoiceOrderCategory
     */
    private int invoiceOrderCategory;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 开票商品编码
     */
    private String goodsCode;

    private int isDeleted;
    private Instant created;
    private String createdBy;
    private Instant lastModified;
    private String lastModifiedBy;

}
