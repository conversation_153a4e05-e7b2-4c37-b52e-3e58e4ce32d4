package cn.abcyun.cis.clinic.model;

import cn.abcyun.cis.commons.util.DateUtils;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;

/**
 * 诊所当前购买的版本
 */
@Data
@Entity
@Table(name = "v2_clinic_current_edition")
public class ClinicCurrentEdition {
    @Id
    private String id;
    private String chainId;
    private String clinicId;

    /**
     * 购买店版本id 就是 10 20 30 40 的字符串
     */
    private String editionId;

    /**
     * 最多支持雇员数
     */
    private int maxEmployeeCount;
    /**
     * 开始时间
     */
    private Instant beginDate;
    /**
     * 结束时间
     */
    private Instant endDate;

    /**
     * 购买时订单号
     */
    private String orderId;
    private int isTrial;

    private int isDeleted;
    private String createdBy;
    private Instant created;
    private String lastModifiedBy;
    private Instant lastModified;

    private Integer maxCooperationClinic;

    public LocalDate getBeginLocalDate() {
        return DateUtils.toLocalDateTime(beginDate).toLocalDate();
    }

    public LocalDate getEndLocalDate() {
        return DateUtils.toLocalDateTime(endDate).toLocalDate();
    }

    public boolean isExpired() {
        Instant now = Instant.now();
        return (endDate != null && now.isAfter(endDate)) || (beginDate != null && now.isBefore(beginDate));
    }

    public boolean isValid() {
        return !isExpired();
    }

    public boolean isBeforeValid() {
        Instant now = Instant.now();
        return beginDate != null && now.isBefore(beginDate);
    }

    public boolean isAfterValid() {
        Instant now = Instant.now();
        return endDate != null && now.isAfter(endDate);
    }

    public int getEditionIdValue() {
        return Integer.parseInt(editionId);
    }

    public int getEditionStatus() {
        if (isBeforeValid()) {
            return Constants.ClinicEditionStatus.BEFORE_VALID;
        } else if (isAfterValid()) {
            return Constants.ClinicEditionStatus.AFTER_VALID;
        }
        return Constants.ClinicEditionStatus.VALID;
    }

    public int leftDays() {
        Instant now = Instant.now();
        if (endDate == null || now.isAfter(endDate)) {
            return 0;
        }

        Instant leftDaysStart = beginDate != null ? DateUtils.max(now, beginDate) : now;
        return (int) Duration.between(leftDaysStart, endDate).toDays();
    }

    public ClinicCurrentEdition copy() {
        ClinicCurrentEdition edition = new ClinicCurrentEdition();
        BeanUtils.copyProperties(this, edition);
        return edition;
    }
}
