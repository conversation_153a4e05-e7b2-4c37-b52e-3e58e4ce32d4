package cn.abcyun.cis.clinic.model;

import cn.abcyun.cis.commons.util.DateUtils;
import cn.abcyun.cis.commons.util.MathUtils;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.vladmihalcea.hibernate.type.json.JsonStringType;
import lombok.Data;
import org.hibernate.annotations.TypeDef;
import org.springframework.beans.BeanUtils;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.Objects;

@Data
@Entity
@Table(name = "v2_clinic_edition_pay_order")
@TypeDef(name = "json", typeClass = JsonStringType.class)
public class ClinicEditionPayOrder {

    @Id
    private String id;

    private String customerId;          // 下单客户id(employee表id)

    private String chainId;

    private String clinicId;

    private BigDecimal totalPrice;      // 总价

    private BigDecimal discountPrice;   // 折扣(负数优惠券的金额)

    private BigDecimal adjustmentPrice; // 议价金额(totalPrice * 0.2 结果取负数)

    private BigDecimal deductionFee;    // 抵扣费用(负数)

    private BigDecimal liJianFee = BigDecimal.ZERO;      // 立减费用，默认值为0

    private BigDecimal receivableFee;   // 应收费用 receivableFee = totalPrice + discountPrice + adjustmentPrice + deductionFee

    private BigDecimal paidFee;         // 支付费用

    private Instant paidTime;           // 支付时间

    private Integer payMode;            // 支付方式(2: 微信；3: 支付宝；100: 线下支付)

    private int status;                 // 状态(0: 未支付；10: 已支付)

    private Instant expiredTime;        // 订单失效时间

    private String sellerId;            // 销售员id(abc-employee表id)

    private String sellerName;          // 销售员姓名

    private int source;                 // 订单来源(1：企业微信销售下单；2：用户网页下单)

    private String remarks;             // 备注

    private String payOrderId;          // 支付用的订单号

    private String offlinePaidSellerId; // 离线支付时的销售员id

    @org.hibernate.annotations.Type(type = "json")
    @Column(columnDefinition = "json")
    private List<ClinicEditionAttachment> attachments; // 附件

    @org.hibernate.annotations.Type(type = "json")
    @Column(columnDefinition = "json")
    private DeductionOrderInfo deductionOrderInfo; // 抵扣信息

    private int isDeleted;              // 是否删除

    private Instant created;            // 创建时间

    private String createdBy;           // 创建人

    private String lastModifiedBy;      // 最后修改人

    private Instant lastModified;       // 最后修改时间

    @Transient
    private ClinicEditionAccountOrder accountOrder;

    @Transient
    private ClinicCooperationClinicOrder cooperationAccountOrder;

    @Transient
    private ClinicEditionOrder editionOrder;

    @Transient
    private List<ClinicSupportItemOrder> clinicSupportItemOrders;

    // 计算实际支付价格时要扣除抵扣金
    public BigDecimal getPurchasePrice() {
        return receivableFee.subtract(deductionFee);
    }

    // 邀请码
    private String referrerCode;

    /**
     * 线索id
     */
    private String clueId;

    /**
     * 接收账号id
     * {@link Constants.OwnReceiveAccountId}
     */
    private String receiveAccountId;





    public boolean isValid() {
        return !isExpired();
    }

    public boolean isExpired() {
        Instant now = Instant.now();
        return Objects.nonNull(expiredTime) && now.isAfter(expiredTime);
    }

    public void setReferrerCode(String referrerCode) {
        this.referrerCode = referrerCode;
        if (this.editionOrder != null) {
            this.editionOrder.setReferrerCode(referrerCode);
        }
    }

    public void setStatus(int status) {
        this.status = status;

        if (this.accountOrder != null) {
            this.accountOrder.setStatus(status);
        }

        if (this.cooperationAccountOrder != null) {
            this.cooperationAccountOrder.setStatus(status);
        }

        if (this.editionOrder != null) {
            this.editionOrder.setStatus(status);
        }

        if (this.clinicSupportItemOrders != null) {
            for (ClinicSupportItemOrder clinicSupportItemOrder : this.clinicSupportItemOrders) {
                clinicSupportItemOrder.setStatus(status);
            }
        }
    }

    public void setPaidFee(BigDecimal paidFee) {
        this.paidFee = paidFee;
        BigDecimal leftPaidFee = paidFee;
        if (this.clinicSupportItemOrders != null) {
            for (ClinicSupportItemOrder clinicSupportItemOrder : this.clinicSupportItemOrders) {
                clinicSupportItemOrder.setPaidFee(MathUtils.wrapBigDecimalCompare(leftPaidFee, clinicSupportItemOrder.getReceivableFee()) > 0 ? clinicSupportItemOrder.getReceivableFee() : leftPaidFee);
                leftPaidFee = MathUtils.wrapBigDecimalSubtract(leftPaidFee, clinicSupportItemOrder.getPaidFee());
            }
        }

        if (this.accountOrder != null) {
            this.accountOrder.setPaidFee(MathUtils.wrapBigDecimalCompare(leftPaidFee, this.accountOrder.getTotalPrice()) > 0 ? this.accountOrder.getTotalPrice() : leftPaidFee);
            leftPaidFee = MathUtils.wrapBigDecimalSubtract(leftPaidFee, this.accountOrder.getPaidFee());
        }

        if (this.cooperationAccountOrder != null) {
            this.cooperationAccountOrder.setPaidFee(MathUtils.wrapBigDecimalCompare(leftPaidFee, this.cooperationAccountOrder.getTotalPrice()) > 0 ? this.cooperationAccountOrder.getTotalPrice() : leftPaidFee);
            leftPaidFee = MathUtils.wrapBigDecimalSubtract(leftPaidFee, this.cooperationAccountOrder.getPaidFee());
        }

        if (this.editionOrder != null) {
            this.editionOrder.setPaidFee(leftPaidFee);
        }
    }

    public void setDiscountPrice(BigDecimal discountPrice) {
        this.discountPrice = discountPrice;
        if (this.editionOrder != null) {
            this.editionOrder.setDiscountPrice(discountPrice);
        }
    }

    public void setAdjustmentPrice(BigDecimal adjustmentPrice) {
        this.adjustmentPrice = adjustmentPrice;
        if (this.editionOrder != null) {
            this.editionOrder.setAdjustmentPrice(adjustmentPrice);
        }
    }

    public void setDeductionFee(BigDecimal deductionFee) {
        this.deductionFee = deductionFee;
        if (this.editionOrder != null) {
            this.editionOrder.setDeductionFee(deductionFee);
        }
    }

    public void setLiJianFee(BigDecimal liJianFee) {
        this.liJianFee = liJianFee;
        if (this.editionOrder != null) {
            this.editionOrder.setLiJianFee(liJianFee);
        }
    }

    public void setReceivableFee(BigDecimal receivableFee) {
        this.receivableFee = receivableFee;
        if (this.editionOrder != null) {
            this.editionOrder.setReceivableFee(receivableFee);
            if (this.accountOrder != null && receivableFee != null && this.accountOrder.getTotalPrice() != null) {
                this.editionOrder.setReceivableFee(receivableFee.subtract(this.accountOrder.getTotalPrice()));
            }
            if (this.clinicSupportItemOrders != null && receivableFee != null) {
                BigDecimal supportItemPaidFee = clinicSupportItemOrders.stream().map(ClinicSupportItemOrder::getReceivableFee).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                this.editionOrder.setReceivableFee(this.editionOrder.getReceivableFee().subtract(supportItemPaidFee));
            }
            if (this.cooperationAccountOrder != null && receivableFee != null && this.cooperationAccountOrder.getTotalPrice() != null) {
                this.editionOrder.setReceivableFee(this.editionOrder.getReceivableFee().subtract(this.cooperationAccountOrder.getTotalPrice()));
            }
        }
    }

    public void setPaidTime(Instant paidTime) {
        this.paidTime = paidTime;
        if (this.editionOrder != null) {
            this.editionOrder.setPaidTime(paidTime);
        }
        if (this.clinicSupportItemOrders != null) {
            for (ClinicSupportItemOrder clinicSupportItemOrder : this.clinicSupportItemOrders) {
                clinicSupportItemOrder.setPaidTime(DateUtils.toLocalDateTime(paidTime));
            }
        }
    }

    public void setPayMode(Integer payMode) {
        this.payMode = payMode;
        if (this.editionOrder != null) {
            this.editionOrder.setPayMode(payMode);
        }
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
        if (this.editionOrder != null) {
            this.editionOrder.setCustomerId(customerId);
        }
    }

    public void setChainId(String chainId) {
        this.chainId = chainId;
        if (this.editionOrder != null) {
            this.editionOrder.setBindChainId(chainId);
        }
    }

    public void setClinicId(String clinicId) {
        this.clinicId = clinicId;
        if (this.editionOrder != null) {
            this.editionOrder.setBindClinicId(clinicId);
        }
    }

    public void setSellerId(String sellerId) {
        this.sellerId = sellerId;
        if (this.editionOrder != null) {
            this.editionOrder.setSellerId(sellerId);
        }
    }

    public void setSellerName(String sellerName) {
        this.sellerName = sellerName;
        if (this.editionOrder != null) {
            this.editionOrder.setSellerName(sellerName);
        }
    }

    public void setSource(int source) {
        this.source = source;
        if (this.editionOrder != null) {
            this.editionOrder.setSource(source);
        }
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
        if (this.editionOrder != null) {
            this.editionOrder.setRemarks(remarks);
        }
    }

    public void setAttachments(List<ClinicEditionAttachment> attachments) {
        this.attachments = attachments;
        if (this.editionOrder != null) {
            this.editionOrder.setAttachments(attachments);
        }
    }

    public ClinicEditionPayOrder copy() {
        ClinicEditionPayOrder order = new ClinicEditionPayOrder();
        BeanUtils.copyProperties(this, order);
        return order;
    }


    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DeductionOrderInfo {
        private ClinicEditionOrder.DeductionOrderInfo editionDeductionOrderInfo;
        private ClinicEditionOrder.DeductionOrderInfo accountDeductionOrderInfo;
        private ClinicEditionOrder.DeductionOrderInfo supportDeductionOrderInfo;
    }


    public static class Source {
        /**
         * 销售创建
         */
        public static final int ORDER_BY_SELLER = 1;
        /**
         * 产品中心
         */
        public static final int ORDER_BY_CUSTOMER_EDITION_CENTER = 2;
        /**
         * 官网首页
         */
        public static final int ORDER_BY_CUSTOMER_INDEX_PAGE = 3;
        /**
         * 活动
         */
        public static final int ORDER_BY_ACTIVITY = 4;
        /**
         * 移动端续费app
         */
        public static final int ORDER_BY_CUSTOMER_APP = 5;
        /**
         * 移动端续费公众号
         */
        public static final int ORDER_BY_CUSTOMER_WX_MP = 6;
        /**
         * 移动端续费H5
         */
        public static final int ORDER_BY_CUSTOMER_H5 = 7;
        /**
         * 独立购买项
         */
        public static final int ORDER_BY_PURCHASE_ITEM = 8;
    }


    public static class Status {
        public static final int WAITING = 0;
        public static final int PAID = 10;
        /**
         * 已退款
         */
        public static final int REFUNDED = 20;
        public static final int CLOSED = 90;
    }
}
