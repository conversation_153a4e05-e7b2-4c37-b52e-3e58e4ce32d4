package cn.abcyun.cis.clinic.model;

import com.fasterxml.jackson.databind.JsonNode;
import com.vladmihalcea.hibernate.type.json.JsonStringType;
import lombok.Data;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.Instant;

/**
 * 诊所当前购买的版本更变日志
 */
@Data
@Entity
@Table(name = "v2_clinic_current_edition_log")
@TypeDef(name = "json", typeClass = JsonStringType.class)
public class ClinicCurrentEditionLog {
    @Id
    private String id;
    private String chainId;
    private String clinicId;
    private String orderId;
    @Type(type = "json")
    @Column(columnDefinition = "json")
    private JsonNode fromEdition;
    @Type(type = "json")
    @Column(columnDefinition = "json")
    private JsonNode toEdition;

    private String createdBy;
    private Instant created;
}
