package cn.abcyun.cis.clinic.model;

import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.time.Instant;

@Data
@Entity
@Table(name = "v2_clinic_edition_promotion_obtained")
public class ClinicEditionPromotionObtained {
    @Id
    private String id;
    private String promotionId;
    private int type;
    private String name;
    private BigDecimal amount;
    private String holderId;
    private String usedOrderId;
    private Instant expiredTime;
    private String recommendClinicId;

    private int status;
    private Instant created;            // 创建时间
    private String createdBy;           // 创建人
    private String lastModifiedBy;      // 最后修改人
    private Instant lastModified;       // 最后修改时间

    @Transient
    public void setPromotionObtainedUnused() {
        this.setStatus(Status.AVAILABLE);
        this.setUsedOrderId(null);
    }

    public static class Type {
        public static final int PERSON = 1;
        public static final int ORGAN = 2;
    }

    public static class Status {
        public static final int AVAILABLE = 1;
        public static final int USED = 10;
        public static final int INVALID = 20;
    }
}
