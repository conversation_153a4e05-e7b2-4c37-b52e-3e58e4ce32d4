package cn.abcyun.cis.clinic.model;

import com.vladmihalcea.hibernate.type.json.JsonStringType;
import lombok.Data;
import org.hibernate.annotations.TypeDef;

import javax.persistence.*;
import java.io.Serializable;
import java.time.Instant;
import java.util.List;

@Data
@Entity
@Table(name = "v2_clinic_role")
@TypeDef(name = "json", typeClass = JsonStringType.class)
public class ClinicRole implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    private String chainId;
    private String clinicId;
    private String name;
    @org.hibernate.annotations.Type(type = "json")
    @Column(columnDefinition = "json")
    private List<Integer> moduleIds;
    private int hisTypeFlag;
    private int sort;
    private int abcEnvFlag;
    /**
     * 001:独立门店；010:连锁子店； 100:连锁总店
     */
    private int nodeTypeFlag;

    /**
     * 类型，0：内置角色；10：自定义角色
     */
    private int type;

    /**
     * 前端兼容
     */
    private int roleId;

    /**
     * 业务开通标志：位运算
     */
    private int businessFlag;

    /**
     * 父角色ID
     */
    private Integer parentRoleId;

    public Integer getId() {
        return roleId;
    }

    private Integer groupId;

    @Transient
    private String groupName;

    @Transient
    private List<ClinicRole> children;

    /**
     * 独立购买项配置
     */
    private String purchaseItemKey;

    private Instant lastModified;

    private String lastModifiedBy;

    private Integer isDeleted;

    @Data
    public static class Role {
        /**
         * 医生/门诊医生
         */
        public static final int DOCTOR = 1;
        /**
         * 住院医生
         */
        public static final int HOSPITAL_ZY_DOCTOR = 102;
        /**
         * 护士/门诊护士
         */
        public static final int NURSE = 2;
        /**
         * 住院护士
         */
        public static final int HOSPITAL_ZY_NURSE = 104;
        /**
         * 医助/门诊医助
         */
        public static final int ASSIST = 5;
        /**
         * 视光师
         */
        public static final int OPTOMETRIST = 9;
        /**
         * 管理员
         */
        public static final int ADMIN = 0;
        /**
         * 咨询师
         */
        public static final int CONSULTANT = 11;
    }

    public static class Type {
        /**
         * 内置
         */
        public static final int INNER = 0;

        /**
         * 自定义
         */
        public static final int CUSTOM = 10;
    }
}
