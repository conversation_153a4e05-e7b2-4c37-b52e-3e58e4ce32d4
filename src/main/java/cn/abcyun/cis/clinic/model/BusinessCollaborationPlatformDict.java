package cn.abcyun.cis.clinic.model;

import lombok.Data;

import javax.persistence.*;

/**
 * 业务协同平台字典表
 */
@Data
@Entity
@Table(name = "v2_business_collaboration_platform_dict")
public class BusinessCollaborationPlatformDict {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 平台类型：0协同平台
     */
    private int platformType;

    /**
     * 字典类型
     * {@link DictType}
     */
    @Column(name = "dict_type")
    private int dictType;

    private String dictDesc;

    /**
     * 协同平台值
     */
    @Column(name = "platform_value")
    private String platformValue;

    /**
     * 协同平台值含义
     */
    @Column(name = "platform_value_meaning")
    private String platformValueMeaning;

    /**
     * HIS系统值
     */
    @Column(name = "his_system_value")
    private String hisSystemValue;

    /**
     * 是否删除：0未删除，1已删除
     */
    @Column(name = "is_deleted")
    private int isDeleted;

    /**
     * 字典类型常量
     */
    public static class DictType {

    }
}