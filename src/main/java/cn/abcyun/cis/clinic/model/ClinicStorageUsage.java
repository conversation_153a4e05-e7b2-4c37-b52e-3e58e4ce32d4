package cn.abcyun.cis.clinic.model;

import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.Instant;

/**
 * 诊所当前使用oss存储的数量
 * 独立一个表
 * tapd:https://www.tapd.cn/22044681/prong/stories/view/1122044681001019418?url_cache_key=from_url_iteration_list_a8525e6d732f1a0921c9776a7b60011c&action_entry_type=stories
 */
@Data
@Entity
@Table(name = "v2_clinic_storage_usage")
public class ClinicStorageUsage {
    /**
     * 雪花算法主键Id
     */
    @Id
    private Long id;
    /**
     * 连锁Id
     */
    private String chainId;
    /**
     * 门店ID
     */
    private String clinicId;
    /**
     * 总可使用空间
     */
    private Long maxStorageCountByte;

    /**
     * 当前已经使用空间
     * 这个空间有 云函数实时汇总的 ，所以单独其这样一个表来存
     */
    private Long currentStorageCountByte;

    /**
     * 标配
     */
    private Instant created;
    private Instant lastModified;

}
