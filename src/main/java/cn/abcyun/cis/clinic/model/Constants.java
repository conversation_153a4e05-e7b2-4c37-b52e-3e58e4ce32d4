package cn.abcyun.cis.clinic.model;

import cn.abcyun.cis.commons.util.DateUtils;

import com.google.common.collect.Lists;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class Constants {
    public static final String DEFAULT_OPERATOR_ID = "00000000000000000000000000000000";
    public static final String DEFAULT_SYSTEM_CHAIN_ID = "00000000000000000000000000000000";
    public static final String CLINIC_EMPLOYEE_PERMISSION_PREFIX = "_permissionInfo";
    public static final String ALL_MODULES = "所有模块";
    public static final int ALL_MODULES_ID = 0;
    public static final String SEPARATOR_COMMA = ",";
    public static final String SEPARATOR_COMMA_CN = "，";
    public static final String SEPARATOR_SPACE = " ";
    public static final String TOKEN_ALL = "*";
    public static final int ROLE_ADMIN = 1;
    public static final int ROLE_NORMAL_USER = 2;
    /**
     * 开放平台目录
     */
    public static final String OPEN_DIR_PATH = "clinic-usage/{clinicId}/open-api";
    /**
     * 延迟支付订单的秒数(半个小时--产品定)
     */
    public final static int DELAYED_SECONDS = 60 * 30 ;

    /**
     * 数据权限
     */
    public static class PermissionData {
        /**
         * 统计
         */
        public static class Statistics {
            /**
             * 成本
             */
            public static List<Integer> COST_MODULE = new ArrayList<Integer>() {
                {
                    add(Constants.ClinicModule.STATISTICS_REVENUE_DAY_REPORT);
                    add(Constants.ClinicModule.STATISTICS_REVENUE_PACKAGE);
                    add(Constants.ClinicModule.STATISTICS_REVENUE_FINANCIAL_REPORT);
                    add(Constants.ClinicModule.STATISTICS_REVENUE_CHARGE_CATEGORY);
                    add(Constants.ClinicModule.STATISTICS_REVENUE_CHARGE_ITEM);
                    add(Constants.ClinicModule.STATISTICS_INVENTORY_STOCK);
                    add(Constants.ClinicModule.STATISTICS_RESULTS_ORDER);
                    add(Constants.ClinicModule.STATISTICS_RESULTS_INSPECTION);
                    add(Constants.ClinicModule.STATISTICS_RESULTS_RECOMMEND);
                }
            };
            /**
             * 患者手机号
             */
            public static List<Integer> PATIENT_MOBILE = new ArrayList<Integer>() {
                {
                    add(Constants.ClinicModule.CHRONIC_CARE_COUNT);
                    add(Constants.ClinicModule.STATISTICS_REVENUE_REGISTER_SETTLEMENT);
                    add(Constants.ClinicModule.STATISTICS_REVENUE_FINANCIAL_DETAIL);
                    add(Constants.ClinicModule.STATISTICS_REVENUE_PATIENT_LIST);
                    add(Constants.ClinicModule.STATISTICS_REVENUE_OUTPATIENT_LOG);
                    add(Constants.ClinicModule.STATISTICS_RESULTS_ORDER);
                    add(Constants.ClinicModule.STATISTICS_RESULTS_EXECUTE);
                    add(Constants.ClinicModule.STATISTICS_RESULTS_RECOMMEND);
                    add(Constants.ClinicModule.STATISTICS_RESULTS_REFERRAL);
                    add(Constants.ClinicModule.STATISTICS_SIGN);
                    add(Constants.ClinicModule.STATISTICS_RENEW);
                    add(Constants.ClinicModule.STATISTICS_MEMBER);
                    add(Constants.ClinicModule.STATISTICS_CARD);
                    add(Constants.ClinicModule.STATISTICS_COUPON);
                    add(Constants.ClinicModule.STATISTICS_INTEGRAL);
                    add(Constants.ClinicModule.STATISTICS_PROMOTION_REFERRAL);
                    add(Constants.ClinicModule.STATISTICS_OUTPATIENT_LOG);
                    add(Constants.ClinicModuleHospital.Statistics.CHILD_CARD_RECHARGE);
                    add(Constants.ClinicModuleHospital.Statistics.CHILD_OUTPATIENT_LOG);
                }
            };

        }
    }

    public static final HashMap<Integer, Integer> organModuleMap = new HashMap<Integer, Integer>() {{
        put(OrganNodeType.INDEPENDENT_CLINIC, 1);       //  001
        put(OrganNodeType.CHAIN_BRANCH_CLINIC, 1 << 1); //  010
        put(OrganNodeType.CHAIN_HEAD_CLINIC, 1 << 2);   //  100
    }};

    public static final HashMap<Integer, Integer> hisTypeMap = new HashMap<Integer, Integer>() {{
        put(HisType.CIS_HIS_TYPE_NORMAL, 1);
        put(HisType.CIS_HIS_TYPE_DENTISTRY, 1 << 1);
        put(HisType.CIS_HIS_TYPE_EYE, 1 << 2);
        put(HisType.CIS_HIS_TYPE_HOSPITAL, 1 << 3);
        put(HisType.CIS_HIS_TYPE_PHARMACY, 1 << 4);
    }};

    public static class ClinicEditionId {
        public static final int NONE = 0;
        public static final int CLOUD_EXAM = 5;
        public static final int BASIC = 10;         //基础版
        public static final int PROFESSIONAL = 20;  //专业版
        public static final int ULTIMATE = 30;      //旗舰版
        public static final int VIP = 40;           //大客户版

        public static final int HIS_BASIC = 50;                //智慧医院基础版
        public static final int HIS_EYE_PROFESSIONAL = 260;     //智慧医院专业版


        public static final int EYE_NONE = 200;
        public static final int EYE_BASIC = 210;
        public static final int EYE_PROFESSIONAL = 220;
        public static final int EYE_ULTIMATE = 230;
        public static final int EYE_VIP = 240;

        /**
         * 药房基础版
         */
        public static final int PHARMACY_BASIC = 2010;
        /**
         * 药房专业版
         */
        public static final int PHARMACY_PROFESSIONAL = 2020;

        /**
         * 医院基础版
         */
        public static final int HOSPITAL_BASIC = 1010;
        /**
         * 医院专业版
         */
        public static final int HOSPITAL_PROFESSIONAL = 1020;
        /**
         * 企微管家
         */
        public static final int SCRM = 900;
    }

    public static class ClinicEditionStatus {
        public static final int NONE = 0;           //未购买
        public static final int BEFORE_VALID = 10;  //未到生效时间
        public static final int VALID = 20;         //正常
        public static final int AFTER_VALID = 30;    //已过期
    }

    public static class PricePeriod {
        public static final int DAY = 1;
        public static final int MONTH = 2;
        public static final int YEAR = 3;
    }

    public static class OrganNodeType {

        // 单店
        public static final int INDEPENDENT_CLINIC = 0;

        // 连锁总部
        public static final int CHAIN_HEAD_CLINIC = 1;

        // 连锁子店
        public static final int CHAIN_BRANCH_CLINIC = 2;
    }

    public static class AddressRegion {
        public static final String WUHAN = "420100";
    }

    public static class ClinicModule {
        /**
         * 库存
         */
        public static class Inventory {
            /**
             * 库存1级权限
             */
            public static final int ID = 8;
            /**
             * 子权限领用
             */
            public static final int CHILD_OBTAIN = 51211;
            /**
             * 子权限调拨
             */
            public static final int CHILD_TRANS = 105;
            /**
             * 子权限报损
             */
            public static final int CHILD_DAMAGE = 51213;
            /**
             * 子权限盘点
             */
            public static final int CHILD_CHECK = 106;
            /**
             * 子权限生产出库
             */
            public static final int CHILD_PRODUCTION = 110;
        }

        /**
         * 统计
         */
        public static class Statistics {
            public static final int ID = 6;

            /**
             * 库存统计
             */
            public static class Inventory {

                public static final int ID = 203;
                /**
                 * 子权限-调拨统计 3007
                 */
                public static final int CHILD_TRANS = 3007;

            }
        }


        public static final int ALL = 0;
        /**
         * 挂号
         */
        public static final int REGISTER = 1;

        /**
         * 门诊
         */
        public static final int OUT_PATIENT_PARENT = 505;
        public static final int OUT_PATIENT = 2;

        /**
         * 收费
         */
        public static final int CHARGE = 507;

        /**
         * 门诊收费
         */
        public static final int CHARGE_OUTPATIENT = 3;

        /**
         * 药房
         */
        public static final int PHARMACY = 508;

        /**
         * 门诊药房
         */
        public static final int PHARMACY_OUTPATIENT = 4;
        /**
         * 患者
         */
        public static final int PATIENT = 5;
        /**
         * 统计
         */
        public static final int STATISTICS = 6;
        /**
         * 营收统计
         */
        public static final int STATISTICS_REVENUE = 201;
        /**
         * 收费日报
         */
        public static final int STATISTICS_REVENUE_DAY_REPORT = 1002;
        /**
         * 收费套餐统计
         */
        public static final int STATISTICS_REVENUE_PACKAGE = 1007;
        /**
         * 挂号结算
         */
        public static final int STATISTICS_REVENUE_REGISTER_SETTLEMENT = 1008;
        /**
         * 收费明细
         */
        public static final int STATISTICS_REVENUE_FINANCIAL_DETAIL = 1010;
        /**
         * 财务报表
         */
        public static final int STATISTICS_REVENUE_FINANCIAL_REPORT = 1011;
        /**
         * 收费分类统计
         */
        public static final int STATISTICS_REVENUE_CHARGE_CATEGORY = 1013;
        /**
         * 收费项目统计
         */
        public static final int STATISTICS_REVENUE_CHARGE_ITEM = 1014;
        /**
         * 空中药房统计
         */
        public static final int STATISTICS_REVENUE_AIR_PHARMACY = 1015;
        /**
         * 患者清单
         */
        public static final int STATISTICS_REVENUE_PATIENT_LIST = 2004;
        /**
         * 门诊日志
         */
        public static final int STATISTICS_REVENUE_OUTPATIENT_LOG = 2006;
        /**
         * 业绩统计
         */
        public static final int STATISTICS_RESULTS = 206;
        /**
         * 开单业绩
         */
        public static final int STATISTICS_RESULTS_ORDER = 6002;
        /**
         * 执行业绩
         */
        public static final int STATISTICS_RESULTS_EXECUTE = 6003;
        /**
         * 检验业绩
         */
        public static final int STATISTICS_RESULTS_INSPECTION = 6005;
        /**
         * 就诊推荐统计
         */
        public static final int STATISTICS_RESULTS_RECOMMEND = 6009;
        /**
         * 转诊统计
         */
        public static final int STATISTICS_RESULTS_REFERRAL = 6011;
        /**
         * 库存统计
         */
        public static final int STATISTICS_INVENTORY = 203;
        /**
         * 进销存统计
         */
        public static final int STATISTICS_INVENTORY_STOCK = 3009;
        /**
         * 签约统计
         */
        public static final int STATISTICS_SIGN = 9002;
        /**
         * 续约统计
         */
        public static final int STATISTICS_RENEW = 9003;
        /**
         * 会员统计
         */
        public static final int STATISTICS_MEMBER = 11002;
        /**
         * 卡项统计
         */
        public static final int STATISTICS_CARD = 11003;
        /**
         * 优惠劵统计
         */
        public static final int STATISTICS_COUPON = 11004;
        /**
         * 积分统计
         */
        public static final int STATISTICS_INTEGRAL = 11005;
        /**
         * 老带新统计
         */
        public static final int STATISTICS_PROMOTION_REFERRAL = 11006;
        /**
         * 就诊日志
         */
        public static final int STATISTICS_OUTPATIENT_LOG = 5251301;
        /**
         * 管理
         */
        public static final int MANAGE = 7;

        /**
         * 库存
         */
        public static final int INVENTORY = 8;

        /**
         * 品种档案
         */
        public static final int VARIETY_FILE = 51901;

        /**
         * 库存二级权限
         */
        public static final int INVENTORY_GOODS = 51209;

        /**
         * 采购-诊所管家
         */
        public static final int HIS_PURCHASE = 102;

        /**
         * 执行站
         */
        public static final int EXECUTE = 10;

        /**
         * 药店采购管理（库存-采购）
         */
        public static final int PHM_PURCHASE_MANAGE = 70202;

        /**
         * 药店采购模块（库存-采购-采购）
         */
        public static final int PHM_PURCHASE = 51210;


        public static class WeClinic {

            /**
             * 微诊所/医院
             */
            public static final int WE_CLINIC = 14;

            /**
             * 微诊所/医院的微商城
             */
            public static final int WE_CLINIC_MALL = 1403;

        }

        /**
         * 儿保
         */
        public static final int CHILD_HEALTH = 15;
        /**
         * SCRM
         */
        public static final int SCRM = 18;
        /**
         * 慢病康复统计
         */
        public static final int CHRONIC_CARE_COUNT = 207;
        /**
         * 收费统计
         */

        public static final int CHARGE_COUNT = 7002;

        /**
         * 患者档案
         */
        public static final int PATIENT_FILE = 301;
        /**
         * 跨站执行
         */
        public static final int NURSE_EXECUTE_CROSS = 6006;

        /**
         * 医保
         */
        public static final int MEDICAL_INSURANCE = 16;

        /**
         * 机构/人员管理
         */
        public static final int EMPLOYEE_MANAGEMENT = 51704;

        /**
         * 诊所设置
         */
        public static final int CLINIC_SETTINGS = 701;

    }

    public static class ClinicModuleHospital {
        /**
         * 库存
         */
        public static class Inventory {
            /**
             * 库存管理：1级权限
             */
            public static final int ID = 8;
            /**
             * 子权限领用
             */
            public static final int CHILD_OBTAIN = 51211;
            /**
             * 子权限调拨
             */
            public static final int CHILD_TRANS = 105;
            /**
             * 子权限报损
             */
            public static final int CHILD_DAMAGE = 51213;
            /**
             * 子权限盘点
             */
            public static final int CHILD_CHECK = 106;
        }

        /**
         * 住院医生站
         */
        public static class InpatientDoctorStation {
            /**
             * 住院医生站：1级权限
             */
            public static final int PARENT_FIRST = 501;
        }

        /**
         * 住院护士站
         */
        public static class InpatientNurseStation {
            /**
             * 住院护士站：1级权限
             */
            public static final int PARENT_FIRST = 502;
        }

        /**
         * 统计
         */
        public static class Statistics {
            /**
             * 子权限：开卡充值业绩
             */
            public static final int CHILD_CARD_RECHARGE = 5250511;
            /**
             * 门诊日志
             */
            public static final int CHILD_OUTPATIENT_LOG = 2006;
        }
    }

    public static class ClinicModulePharmacy {
        /**
         * 零售
         */
        public static class Retail {
            /**
             * 零售：1级权限
             */
            public static final int PARENT_FIRST = 3;
            /**
             * 子权限零售单
             */
            public static final int CHILD_RETAIL = 70102;
        }

        /**
         * 库存
         */
        public static class Inventory {
            /**
             * 库存管理：1级权限
             */
            public static final int PARENT_FIRST = 8;
            /**
             * 子权限调拨
             */
            public static final int CHILD_TRANS = 70207;
        }
    }

    public static class HisType {
        /**
         * 诊所管家模式
         */
        public static final int CIS_HIS_TYPE_NORMAL = 0;
        /**
         * 口腔管家模式
         */
        public static final int CIS_HIS_TYPE_DENTISTRY = 1;
        /**
         * 眼科管家模式
         */
        public static final int CIS_HIS_TYPE_EYE = 2;

        public static String toHisTypeName(Integer hisType) {
            if (hisType == null) {
                return "";
            }

            switch (hisType) {
                case CIS_HIS_TYPE_NORMAL:
                    return "诊所管家";
                case CIS_HIS_TYPE_DENTISTRY:
                    return "口腔管家";
                case CIS_HIS_TYPE_EYE:
                    return "眼科管家";
                case CIS_HIS_TYPE_PHARMACY:
                    return "药店管家";
                default:
                    return "";
            }
        }

        /**
         * 智慧医院
         */
        public static final int CIS_HIS_TYPE_HOSPITAL = 100;

        /**
         * 药店管家
         */
        public static final int CIS_HIS_TYPE_PHARMACY = 10;
    }

    public static String getRedisConditionMapKey(String clinicId) {
        return String.format("%s:%s", Constants.ClinicCache.CLINIC_EMPLOYEE_CONDITION_MAP, clinicId);
    }

    public static class ClinicCache {
        public static final String CLINIC_EMPLOYEE_SETTINGS = "sc-clinic-employee-settings";
        public static final String CLINIC_SETTINGS_CONFIG = "sc-clinic-settings-config";
        public static final String CLINIC_DOCTOR_TAG_EMPLOYEE = "sc-clinic-doctor-tag-with-employee-view-new1";
        public static final String CLINIC_DOCTOR_TAG = "sc-clinic-doctor-tag";
        public static final String CLINIC_EMPLOYEE_CONDITION_MAP = "sc-clinic-employee-condition-map";
        public static final String QR_SCAN_SYS_TOKEN = "sys-token";
        public static final String REGION_GRAY_ORGANS = "sc-clinic-region-gray-organs";
        public static final String GRAY_ORGAN_ITEM = "sc-clinic-gray-organ-item";
        public static final String CLINIC_CHAIN_EMPLOYEE_V2 = "sc-clinic-chain-employee-v2";
        public static final String CLINIC_CHAIN_EMPLOYEE_IDS_V2 = "sc-clinic-chain-employee-ids-v2";

        public static final String CLINIC_DEPARTMENT_IDS = "sc-clinic-department-ids";
        public static final String CLINIC_DEPARTMENT = "sc-clinic-department";
        public static final String CLINIC_CHAIN_EDITION = "sc-clinic-chain-edition";
        public static final String CLINIC_EDITION = "sc-clinic-edition";
        public static final String CLINIC_CURRENT_EDITION_COMPOSE = "sc-clinic-current-edition-compose";

        public static final String CLINIC_EMPLOYEE_IDS = "sc-clinic-clinic-employee-ids";
        public static final String CLINIC_CLINIC_EMPLOYEE = "sc-clinic-clinic-employee";
        public static final String CLINIC_PURCHASE_ITEMS = "sc-clinic-purchase-items";
        public static final String CLINIC_PHARMACY_CONFIG = "sc-clinic-pharmacy-config";

        public static final String CLINIC_CHAIN_ORGAN_VIEW = "sc-clinic-chain-organ-view";

        public static final String CLINIC_EMPLOYEE_V3 = "sc-clinic-employee-v3";

        public static final String CLINIC_EMPLOYEE = "sc-clinic-employee";

        public static final String CLINIC_EMPLOYEE_SNAP = "employee-snap";

        public static final String CLINIC_MODULES = "sc-clinic-modules";
    }

    public static class Ttl {
        // half hour
        public static final long HALF_HOUR = 30 * 60L;
        // one hour
        public static final long ONE_HOUR = 60 * 60L;
        public static final long TWO_HOUR = 2 * 60 * 60L;
        public static final long FOUR_HOUR = 4 * 60 * 60L;
    }

    /**
     * 诊所组标签
     */
    public static class TagGroupKey {
        /**
         * 诊所类型
         */
        public static final String HIS_TYPE = "hisType";
        /**
         * 机构类别
         */
        public static final String CATEGORY = "category";
        /**
         * 版本级别
         */
        public static final String EDITION_LEVEL = "editionLevel";
        /**
         * 诊所能力
         */
        public static final String ABILITY_SUPPORT = "abilitySupport";
        /**
         * 门店开通时长
         */
        public static final String OPENING_TIME = "openingTime";
        /**
         * 门店活跃度
         */
        public static final String ACTIVITY_DEGREE = "activityDegree";
        /**
         * 版本期限
         */
        public static final String EDITION_END_TIMING = "editionEndTiming";
    }

    /**
     * 诊所标签
     */
    public static class TagKey {
        /**
         * 诊所管家
         */
        public static final String HIS_TYPE_COMMON = TagGroupKey.HIS_TYPE + ".common";
        /**
         * 口腔管家
         */
        public static final String HIS_TYPE_ORAL = TagGroupKey.HIS_TYPE + ".oral";
        /**
         * 眼科管家
         */
        public static final String HIS_TYPE_EYE = TagGroupKey.HIS_TYPE + ".eye";
        /**
         * 中西医结合
         */
        public static final String CATEGORY_CM_WM = TagGroupKey.CATEGORY + ".cm_wm";
        /**
         * 中西
         */
        public static final String CATEGORY_CM = TagGroupKey.CATEGORY + ".cm";
        /**
         * 西医
         */
        public static final String CATEGORY_WM = TagGroupKey.CATEGORY + ".wm";
        /**
         * 基础版
         */
        public static final String EDITION_LEVEL_BASIC = TagGroupKey.EDITION_LEVEL + ".basic";
        /**
         * 专业版
         */
        public static final String EDITION_LEVEL_PROFESSIONAL = TagGroupKey.EDITION_LEVEL + ".professional";
        /**
         * 旗舰版
         */
        public static final String EDITION_LEVEL_ULTIMATE = TagGroupKey.EDITION_LEVEL + ".ultimate";
        /**
         * 大客户版
         */
        public static final String EDITION_LEVEL_VIP = TagGroupKey.EDITION_LEVEL + ".vip";
        /**
         * 医保
         */
        public static final String ABILITY_SUPPORT_SHEBAO = TagGroupKey.ABILITY_SUPPORT + ".shebao";
        /**
         * 微诊所
         */
        public static final String ABILITY_SUPPORT_WE_CLINIC = TagGroupKey.ABILITY_SUPPORT + ".weClinic";
        /**
         * LIS(联机)
         */
        public static final String ABILITY_SUPPORT_LIS_DEVICE = TagGroupKey.ABILITY_SUPPORT + ".lisDevice";
        /**
         * LIS(有项目)
         */
        public static final String ABILITY_SUPPORT_LIST_ITEM = TagGroupKey.ABILITY_SUPPORT + ".lisItem";
        /**
         * 空中药房
         */
        public static final String ABILITY_SUPPORT_AIR_PHARMACY = TagGroupKey.ABILITY_SUPPORT + ".airPharmacy";
        /**
         * 儿保
         */
        public static final String ABILITY_SUPPORT_CHILDCARE = TagGroupKey.ABILITY_SUPPORT + ".childCare";
        /**
         * ABC支付
         */
        public static final String ABILITY_SUPPORT_ABC_PAY = TagGroupKey.ABILITY_SUPPORT + ".abcPay";
        /**
         * 新店
         */
        public static final String OPENING_TIME_SHORT = TagGroupKey.OPENING_TIME + ".short";
        /**
         * 老店
         */
        public static final String OPENING_TIME_LONG = TagGroupKey.OPENING_TIME + ".long";
        /**
         * 非活跃
         */
        public static final String ACTIVITY_DEGREE_DEAD = TagGroupKey.ACTIVITY_DEGREE + ".dead";
        /**
         * 活跃
         */
        public static final String ACTIVITY_DEGREE_ALIVE = TagGroupKey.ACTIVITY_DEGREE + ".alive";
        /**
         * 临期
         */
        public static final String EDITION_END_TIMING_NEAR = TagGroupKey.EDITION_END_TIMING + ".near";
        /**
         * 过期
         */
        public static final String EDITION_END_TIMING_OVERDUE = TagGroupKey.EDITION_END_TIMING + ".overdue";
    }

    public static class LoginWays {
        public static final String WEB_SMS = "SMS"; // 网页短信登录
        public static final String WEB_SMS_REGISTER = "SMSRegister"; // 网页短信注册并登录
        public static final String WEB_WECHAT_SCAN = "wechatScan"; // 扫码登录
        public static final String WEB_TEST = "testLogin"; // 测试登录
        public static final String WEB_PASSWORD = "password"; // 密码登录
        public static final String WEB_WX_CODE = "wechatAuth"; // 微信授权登录

        public static final String APP_SMS = "appSMS"; // 移动端短信登录
        public static final String APP_WX = "appWx"; // 移动端微信登录
        public static final String APP_PASSWORD = "appPassword"; // 移动端密码登录

        public static final String CHU_TIAN_YUN = "chutianyun"; // 楚天云

        public static final String SCRM_WEB = "token"; // scrm网页
        public static final String SCRM_QW_CLIENT = "qwClient"; // scrm企业微信客户端

        public static final String WEAPP_WX = "weappWx"; // 小程序微信登录

        public static final String WEAPP_MOBILE = "weappMobile"; // 小程序手机号登录

        public static final String WEAPP_SMS = "weappSMS"; // 小程序验证码登录

        public static final String WEAPP_PASSWORD = "weappPassword"; // 小程序密码登录

        public static final String DEVICE_CLIENT = "deviceClient"; // 云检设备登录
    }

    public static class SwitchClinicWays {
        public static final String WEB_SWITCH = "clinicSwitch";
        public static final String TEST_SWITCH = "testLogin-clinicSwitch";
        public static final String WEB_PASSWORD_SWITCH = "password-clinicSwitch";
        public static final String WEB_WX_CODE_SWITCH = "wechatAuth-clinicSwitch";
        public static final String WEB_CHU_TIAN_YUN_SWITCH = "chutianyun-clinicSwitch";

        public static final String APP_SMS_SWITCH = "appSMS-clinicSwitch";
        public static final String APP_WX_SWITCH = "appWx-clinicSwitch";
        public static final String APP_PASSWORD_SWITCH = "appPassword-clinicSwitch";
        public static final String WEB_SMS_REGISTER_SWITCH = "SMSRegisterclinicSwitch";
    }

    public static class CookieType {
        public static final int HIS = 0;
        public static final int SCRM = 1;
    }

    public static class LogoutWay {
        public static final String HIS_LOGOUT = "logout";
        public static final String SCRM_LOGOUT = "logoutScrm";
    }

    // 创建订单人类型
    public static class OrderCreatorType {
        // 0客户， 1销售
        public static final int CUSTOMER = 0;
        public static final int SALE = 1;
    }


    public static class SwitchClinicScene {
        // his-pc: 诊所管家pc
        public static final int HIS_PC = 0;
        // his-app: 诊所管家app
        public static final int HIS_APP = 1;
        // scrm-pc: scrm pc
        public static final int SCRM_WEB = 10;
        // scrm-qw: scrm 企微
        public static final int SCRM_QW = 11;
        // mall-weapp：商城小程序
        public static final int MALL_WEAPP = 20;
        // 医保独立端-pc：医保独立端pc
        public static final int YIBAO_PC = 30;
        // 影像截图上传-pc
        public static final int IMAGE_UPLOAD_PC = 40;
        // dicom网关-pc切换
        public static final int DICOM_GATEWAY_PC = 50;
        // his设备(ABC云检)
        public static final int HIS_DEVICE = 60;
    }

    /**
     * 发票类型 0：未知，1：普通发票，2：增值税专用发票
     */
    public static class ClinicInvoiceType {
        /**
         * 未知
         */
        public static final int UNKNOWN = 0;

        /**
         * 普通发票
         */
        public static final int VAT_NORMAL = 1;

        /**
         * 增值税专用发票
         */
        public static final int VAT_SPECIAL = 2;
    }

    /**
     * 开票申请单状态 0:待开票 10:开票中 20:已完成 30:开票失败 40:已取消
     */
    public static class ClinicInvoiceApplyStatus {
        /**
         * 待开票
         */
        public static final int WAITING = 0;

        /**
         * 开票中
         */
        public static final int INVOICING = 10;

        /**
         * 已开票
         */
        public static final int INVOICED = 20;

        /**
         * 开票失败
         */
        public static final int FAILED = 30;

        /**
         * 已取消
         */
        public static final int CANCELED = 40;
    }

    /**
     * 订单开票状态 0:不需要开票（历史订单不需要开票） 10:未申请 20:已申请 30:已开票 40:已关闭
     */
    public static class OrderInvoiceStatus {
        /**
         * 不需要开票（历史订单不需要开票）
         */
        public static final int NO_NEED = 0;

        /**
         * 未申请
         */
        public static final int NOT_APPLIED = 10;

        /**
         * 已申请
         */
        public static final int APPLIED = 20;

        /**
         * 已开票
         */
        public static final int INVOICED = 30;

        /**
         * 已关闭
         */
        public static final int CLOSED = 40;
    }


    public static class PayAccount {
        /**
         * 楚天云 10
         */
        public static final int CTY = 10;
        /**
         * ABC 0
         */
        public static final int ABC = 0;
    }

    /**
     * 购买方式
     */
    public static class BuyMethod {

        /**
         * 新购
         */
        public static final int ADD = 0;

        /**
         * 续费
         */
        public static final int RENEW = 1;

    }

    /**
     * 开票项目分类 0:系统年费 1:系统增购账号费 10:独立购买项 11:独立购买项增购账号费 20:其他费用
     */
    public static class InvoiceOrderCategory {
        /**
         * 系统年费
         */
        public static final int EDITION_ORDER = 0;
        /**
         * 系统增购账号费
         */
        public static final int EDITION_ACCOUNT_ORDER = 1;
        /**
         * 独立购买项
         */
        public static final int PURCHASE_ITEM_ORDER = 10;
        /**
         * 独立购买项增购账号费
         */
        public static final int PURCHASE_ITEM_ACCOUNT_ORDER = 11;
        /**
         * 其他费用
         */
        public static final int OTHER_ORDER = 20;
        /**
         * 短信充值订单
         */
        public static final int SMS_QUOTE_ORDER = 30;
    }

    /**
     * 开票项目类型 0:系统版本费 1:医保对接费 2:LIS设备对接费 3:PACS设备对接费 4:增购账号费 5:自助服务机 6:专网前置机 7:开放平台接口费 8:药诊合作诊所 9:数据迁移费 10:企微管家 11:微商城 12:短信充值费用 13:医院项目费用 14:医保代办（帮助用户办理医保定点机构）
     */
    public static class InvoiceItemType {
        /**
         * 系统版本费
         */
        public static final int ABC_EDITION = 0;
        /**
         * 医保对接费
         */
        public static final int SHEBAO_SUPPORT = 1;
        /**
         * LIS设备对接费
         */
        public static final int LIS_DEVICE_SUPPORT = 2;
        /**
         * PACS设备对接费
         */
        public static final int PACS_DEVICE_SUPPORT = 3;
        /**
         * 增购账号费
         */
        public static final int ADD_ACCOUNT = 4;
        /**
         * 自助服务机
         */
        public static final int SELF_SERVICE_MACHINE = 5;
        /**
         * 专网前置机
         */
        public static final int SPECIAL_NET_PRE_MACHINE = 6;
        /**
         * 开放平台接口费
         */
        public static final int OPENAPI_SUPPORT = 7;
        /**
         * 药诊合作诊所
         */
        public static final int PHARMACY_CLINIC_CONN = 8;
        /**
         * 数据迁移费
         */
        public static final int DATA_MIGRATION = 9;
        /**
         * 企微管家
         */
        public static final int SCRM = 10;
        /**
         * 微商城
         */
        public static final int MC_MALL = 11;
        /**
         * 短信充值费用
         */
        public static final int SMS_RECHARGE = 12;
        /**
         * 医院项目费用
         */
        public static final int HOSPITAL_ITEM = 13;
        /**
         * 医保代办（帮助用户办理医保定点机构）
         */
        public static final int HELP_SHEBAO_ACTIVE = 14;
    }

    public static class OwnReceiveAccountId {
        public static final String BYTEFLOW = "1";
        public static final String BYTEPLANET = "2";
    }

    /**
     * 税率变更时间
     */
    public static  final Instant TAX_RATE_CHANGE_TIME =DateUtils.toInstant(LocalDateTime.of(2025, 7, 1, 0, 0, 0));

    public static class TaxRate {

        public static final BigDecimal ONE_PERCENT = BigDecimal.valueOf(0.01);

        public static final BigDecimal SIX_PERCENT = BigDecimal.valueOf(0.06);

        public static final BigDecimal THIRTEEN_PERCENT = BigDecimal.valueOf(0.13);
    }

    public static List<String> goodsClinicDataPermissionKeyList =
            Lists.newArrayList(ClinicDataPermissionConfig.Key.INVENTORY_GOODS_ADJUST_PRICE,
            ClinicDataPermissionConfig.Key.INVENTORY_GOODS_CREATE_ARCHIVES,
            ClinicDataPermissionConfig.Key.INVENTORY_GOODS_MODIFY_ARCHIVES,
            ClinicDataPermissionConfig.Key.INVENTORY_GOODS_DELETE_ARCHIVES);
}
