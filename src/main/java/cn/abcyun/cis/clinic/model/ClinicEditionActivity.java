package cn.abcyun.cis.clinic.model;

import cn.abcyun.cis.commons.util.JsonUtils;
import cn.abcyun.cis.commons.util.TextUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.vladmihalcea.hibernate.type.json.JsonStringType;
import lombok.Data;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.TypeDef;
import org.springframework.util.CollectionUtils;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;

@Data
@Entity
@Table(name = "v2_clinic_edition_activity")
@TypeDef(name = "json", typeClass = JsonStringType.class)
public class ClinicEditionActivity {

    /**
     * 抗疫同行
     */
    public static final String EPIDEMIC_ACTIVITY_ID = "10000";
    /**
     * 老用户原价续费/升级/降级/购买账号
     */
    public static final String OLD_USER_REBUY_ACTIVITY_ID = "20000";
    /**
     * 限时医保免对接费用
     */
    public static final String SHEBAO_ACTIVITY_ID = "30000";
    /**
     * 双11年度大促
     */
    public static final String DOUBLE_ELEVEN_ACTIVITY_ID = "40000";

    @Id
    private String id;
    private String name;
    @Column(name = "[period]")
    private String period;
    private Instant beginDate;
    private Instant endDate;

    @Type(type = "json")
    @Column(columnDefinition = "json")
    private List<EquivalentAmount> equivalentAmount;

    /**
     * 活动配置（通用JSON格式，支持不同类型的活动配置）
     */
    @Type(type = "json")
    @Column(columnDefinition = "json")
    private JsonNode activityConfigs;

    private int status;

    private Instant created;            // 创建时间
    private String createdBy;           // 创建人
    private String lastModifiedBy;      // 最后修改人
    private Instant lastModified;       // 最后修改时间

    public EquivalentAmount findEquivalentAmount(String editionId) {
        if (CollectionUtils.isEmpty(equivalentAmount) || TextUtils.isEmpty(editionId)) {
            return null;
        }

        return equivalentAmount.stream().filter(item -> TextUtils.equals(editionId, item.getEditionId())).findFirst().orElse(null);
    }

    /**
     * 查找立减活动配置
     */
    public LiJianActivityConfig findLiJianActivityConfig(String editionId) {
        if (activityConfigs == null || TextUtils.isEmpty(editionId)) {
            return null;
        }

        // 从 JsonNode 中获取立减活动配置
        JsonNode liJianConfigs = activityConfigs.get("liJianActivityConfigs");
        if (liJianConfigs == null || !liJianConfigs.isArray()) {
            return null;
        }

        // 遍历配置数组查找匹配的版本
        for (JsonNode configNode : liJianConfigs) {
            String configEditionId = configNode.get("editionId").asText();
            if (TextUtils.equals(editionId, configEditionId)) {
                // 将 JsonNode 转换为 LiJianActivityConfig 对象
                return convertJsonNodeToLiJianActivityConfig(configNode);
            }
        }

        return null;
    }

    /**
     * 判断活动是否在有效期内
     */
    public boolean isActivityValid() {
        Instant now = Instant.now();
        return beginDate != null && endDate != null &&
               !now.isBefore(beginDate) && !now.isAfter(endDate) &&
               status == 1; // 假设status=1表示活动有效
    }

    /**
     * 将 JsonNode 转换为 LiJianActivityConfig 对象
     */
    private LiJianActivityConfig convertJsonNodeToLiJianActivityConfig(JsonNode configNode) {
        return JsonUtils.readValue(configNode, LiJianActivityConfig.class);
    }


    @Data
    public static class EquivalentAmount {
        private String editionId;
        private BigDecimal amount;
    }


    @Data
    public static class LiJianActivityConfig {
        /**
         * 版本ID
         * 普通和口腔： 10 基础班 20 专业版 30 旗舰版 40 大客户版
         * 口腔(待启用)： 110 基础班 120 专业版 130 旗舰版 140 大客户版
         * 眼科： 210 基础班 220 专业版 230 旗舰版 240 大客户版
         */
        private String editionId;

        /**
         * 版本名称
         */
        private String editionName;

        /**
         * 立减规则列表
         */
        private List<LiJianRule> liJianRules;

        /**
         * 最大折扣范围（保留原有字段）
         */
        private List<ClinicEditionMaxAdjustment> maxAdjustment;
    }

    @Data
    public static class LiJianRule {
        /**
         * 购买年数
         */
        private int years;

        /**
         * 订单类型（1:新购 2:续费 3:升级 4:降级 5:SOP续费）
         */
        private int orderType;

        /**
         * 折扣（如0.9表示9折，null表示无折扣）
         */
        private BigDecimal discount;

        /**
         * 立减金额（单位：元）
         */
        private BigDecimal liJianAmount;

        /**
         * 规则描述
         */
        private String description;
    }
}
