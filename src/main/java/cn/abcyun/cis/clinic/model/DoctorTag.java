package cn.abcyun.cis.clinic.model;

import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.Instant;

@Data
@Entity
@Table(name = "v2_clinic_doctor_tag")
public class DoctorTag {
    @Id
    private String id;
    private String chainId;         // 连锁id
    private String name;
    private int type;
    private int status;             // 状态（1：正常；99：已删除）

//    @OneToMany(cascade = CascadeType.ALL)
//    @JoinColumn(name = "tagId")
//    @SQLDelete(sql = "DELETE FROM v2_clinic_chain_employee_tag WHERE tag_id=? and chain_id=? and employee_id=? and tag_id=?")
//    @SQLDeleteAll(sql = "DELETE FROM v2_clinic_chain_employee_tag WHERE tag_id=?")
//    @SQLInsert(sql = "UPDATE v2_clinic_chain_employee_tag SET tag_id=? WHERE chain_id=? and employee_id=? and tag_id=?")
//    @OrderBy(value = "sort asc")
//    private List<DoctorTagEmployee> employees;

    private String createdBy;       // 创建人
    private Instant created;        // 创建时间
    private String lastModifiedBy;  // 最后修改人
    private Instant lastModified;   // 最后修改时间

    public static class Type {
        public static final int SYSTEM = 0;
        public static final int CHAIN = 1;
        public static final int MICRO_CLINIC = 2;
        public static final int DEPARTMENT_TAG = 3;
    }

    public static class Status {
        public static final int NORMAL = 1;
        public static final int DELETED = 99;
    }
}
