package cn.abcyun.cis.clinic.model;

import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.Instant;

@Data
@Entity
@Table(name = "v2_clinic_edition_reminder_read")
public class ClinicEditionReminderRead {
    @Id
    private String id;
    private String clinicId;
    private String employeeId;
    private Instant created;
}
