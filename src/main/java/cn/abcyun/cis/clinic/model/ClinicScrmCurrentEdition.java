package cn.abcyun.cis.clinic.model;

import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.Instant;

@Data
@Entity
@Table(name = "v2_clinic_scrm_current_edition")
public class ClinicScrmCurrentEdition {

	@Id
	private String id;

	/**
	 * 连锁id
	 */
	private String chainId;

	/**
	 * 门店id
	 */
	private String clinicId;

	/**
	 * 企业id
	 */
	private String corpId;

	/**
	 * 购买店版本id
	 */
	private String editionId;

	/**
	 * 最多支持雇员数
	 */
	private Integer maxEmployeeCount;

	/**
	 * 是否删除
	 */
	private int isDeleted;

	/**
	 * 创建时间
	 */
	private Instant created;

	/**
	 * 创建人
	 */
	private String createdBy;

	/**
	 * 最后修改人
	 */
	private String lastModifiedBy;

	/**
	 * 最后修改时间
	 */
	private Instant lastModified;
}
