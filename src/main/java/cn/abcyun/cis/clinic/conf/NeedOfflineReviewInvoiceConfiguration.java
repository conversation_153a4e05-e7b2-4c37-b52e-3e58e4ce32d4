package cn.abcyun.cis.clinic.conf;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "invoice")
public class NeedOfflineReviewInvoiceConfiguration {

    /**
     * 发票抬头
     */
    private Set<String> titles = new HashSet<>();

    /**
     * 纳税人识别号
     */
    private Set<String> identificationNumbers = new HashSet<>();

}
