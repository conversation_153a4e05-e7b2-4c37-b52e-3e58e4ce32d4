package cn.abcyun.cis.clinic.repository;

import cn.abcyun.cis.clinic.model.ClinicThemeConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/2 16:19
 */
@Repository
public interface ClinicThemeConfigRepository extends JpaRepository<ClinicThemeConfig, Integer> {
    List<ClinicThemeConfig> findByIsDeleted(int isDeleted);
}
