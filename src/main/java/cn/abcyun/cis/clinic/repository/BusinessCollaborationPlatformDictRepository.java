package cn.abcyun.cis.clinic.repository;

import cn.abcyun.cis.clinic.model.BusinessCollaborationPlatformDict;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/10/15 20:19
 */
@Repository
public interface BusinessCollaborationPlatformDictRepository extends JpaRepository<BusinessCollaborationPlatformDict, Long> {

    /**
     * 根据字典类型和删除状态查询字典列表
     * @param dictType 字典类型
     * @param isDeleted 是否删除：0未删除，1已删除
     * @return 字典列表
     */
    List<BusinessCollaborationPlatformDict> findByPlatformTypeAndDictTypeAndIsDeleted(int platformType, int dictType, int isDeleted);

}
