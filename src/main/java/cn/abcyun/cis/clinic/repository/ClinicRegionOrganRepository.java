package cn.abcyun.cis.clinic.repository;

import cn.abcyun.cis.clinic.model.ClinicRegionOrgan;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/4/12 11:01
 */
@Repository
public interface ClinicRegionOrganRepository extends JpaRepository<ClinicRegionOrgan, String> {
    /**
     * 根据chainId查询
     */
    Optional<ClinicRegionOrgan> findByChainId(String chainId);

    List<ClinicRegionOrgan> findByChainIdIn(List<String> chainIds);
}
