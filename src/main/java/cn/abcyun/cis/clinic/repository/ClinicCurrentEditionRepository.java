package cn.abcyun.cis.clinic.repository;

import cn.abcyun.cis.clinic.model.ClinicCurrentEdition;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

@Repository
public interface ClinicCurrentEditionRepository extends JpaRepository<ClinicCurrentEdition, String> {
    Optional<ClinicCurrentEdition> findByClinicIdAndIsDeleted(String clinicId, int isDeleted);

    List<ClinicCurrentEdition> findByClinicIdInAndIsDeleted(Collection<String> clinicIds, int isDeleted);

    List<ClinicCurrentEdition> findByChainIdAndIsDeleted(String chainId, int isDeleted);

    List<ClinicCurrentEdition> findByChainIdInAndIsDeleted(Collection<String> chainIds, int isDeleted);

//    List<ClinicCurrentEdition> findByEndDateInAndIsDeleted(Collection<Instant> expiredTimes, int isDeleted);

    Page<ClinicCurrentEdition> findByEndDateInAndIsDeleted(Collection<Instant> expiredTimes, int isDeleted, Pageable pageable);

    Page<ClinicCurrentEdition> findByEndDateBetweenAndIsDeleted(Instant startDate, Instant endDate, int isDeleted, Pageable pageable);

    /**
     * 通过时间范围和 regionId 查询，关联 v2_clinic_region_organ 表
     */
    @Query("SELECT cce FROM ClinicCurrentEdition cce " +
           "INNER JOIN ClinicRegionOrgan cro ON cce.chainId = cro.chainId " +
           "INNER JOIN Organ o ON o.id = cce.clinicId and o.status = 1 and o.viewMode = 1 " +
           "WHERE cce.endDate BETWEEN :startDate AND :endDate " +
           "AND cro.regionId = :regionId AND cce.isDeleted = :isDeleted " +
            "AND o.addressCityId in (:addressCityIds)")
    Page<ClinicCurrentEdition> findByEndDateBetweenAndRegionIdAndIsDeleted(
            @Param("startDate") Instant startDate,
            @Param("endDate") Instant endDate,
            @Param("regionId") String regionId,
            @Param("isDeleted") int isDeleted,
            @Param("addressCityIds") List<String> addressCityIds,
            Pageable pageable);
}
