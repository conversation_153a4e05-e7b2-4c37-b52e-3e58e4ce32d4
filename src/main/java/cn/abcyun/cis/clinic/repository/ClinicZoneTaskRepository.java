package cn.abcyun.cis.clinic.repository;

import cn.abcyun.cis.clinic.model.ClinicZoneTask;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ClinicZoneTaskRepository extends JpaRepository<ClinicZoneTask, Long> {

    List<ClinicZoneTask> findAllByStatusInAndToZone( List<Integer> status,String toZone);
}
