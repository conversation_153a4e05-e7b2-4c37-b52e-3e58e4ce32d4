package cn.abcyun.cis.clinic.repository;

import cn.abcyun.cis.clinic.model.ClinicExpiringAccessConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2025/9/12 19:32
 */
@Repository
public interface ClinicExpiringAccessConfigRepository extends JpaRepository<ClinicExpiringAccessConfig, Long> {
    ClinicExpiringAccessConfig findFirstByChainIdAndClinicIdAndRuleTypeAndIsLimited(String chainId, String clinicId, int ruleType, int isLimited);
}
