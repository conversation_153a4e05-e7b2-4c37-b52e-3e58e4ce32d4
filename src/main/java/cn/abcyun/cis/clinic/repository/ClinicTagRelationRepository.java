package cn.abcyun.cis.clinic.repository;

import cn.abcyun.cis.clinic.model.ClinicTagRelation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/6/30 10:52
 */
@Repository
public interface ClinicTagRelationRepository extends JpaRepository<ClinicTagRelation, String> {

    List<ClinicTagRelation> findByClinicId(String clinicId);

    List<ClinicTagRelation> findByClinicIdIn(List<String> clinicIds);

    List<ClinicTagRelation> findByClinicIdAndTagKeyIn(String clinicId ,List<String> tagIds);

    void deleteByClinicIdAndTagKeyIn(String clinicId, List<String> tagKeyList);
}
