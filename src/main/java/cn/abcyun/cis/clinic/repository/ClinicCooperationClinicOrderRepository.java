package cn.abcyun.cis.clinic.repository;

import cn.abcyun.cis.clinic.model.ClinicCooperationClinicOrder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/7/26 11:51
 */
@Repository
public interface ClinicCooperationClinicOrderRepository extends JpaRepository<ClinicCooperationClinicOrder, String> {

    List<ClinicCooperationClinicOrder> findByClinicIdAndStatusAndIsDeleted(String clinicId, int status, int isDeleted);

    Optional<ClinicCooperationClinicOrder> findByEditionPayOrderIdAndIsDeleted(String editionPayOrderId, int isDeleted);
}
