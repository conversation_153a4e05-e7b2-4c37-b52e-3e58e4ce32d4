package cn.abcyun.cis.clinic.repository;

import cn.abcyun.cis.clinic.model.ClinicBusinessPermission;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/13 15:03
 */
@Repository
public interface ClinicBusinessPermissionRepository extends JpaRepository<ClinicBusinessPermission, String> {
    ClinicBusinessPermission findFirstByKeyAndIsDeleted(String key, int isDeleted);

    List<ClinicBusinessPermission> findByKeyInAndIsDeleted(Collection<String> keys, int isDeleted);
}
