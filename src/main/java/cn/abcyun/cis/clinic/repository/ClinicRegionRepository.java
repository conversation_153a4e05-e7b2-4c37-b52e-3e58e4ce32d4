package cn.abcyun.cis.clinic.repository;

import cn.abcyun.cis.clinic.model.ClinicRegion;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/12 11:00
 */
@Repository
public interface ClinicRegionRepository extends JpaRepository<ClinicRegion, String> {
    List<ClinicRegion> findByIdIn(List<String> regionIds);
}
