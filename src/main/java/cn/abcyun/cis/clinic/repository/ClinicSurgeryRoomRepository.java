package cn.abcyun.cis.clinic.repository;

import cn.abcyun.cis.clinic.model.ClinicSurgeryRoom;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/6 10:14
 */
@Repository
public interface ClinicSurgeryRoomRepository extends JpaRepository<ClinicSurgeryRoom, String> {

    List<ClinicSurgeryRoom> findByChainIdAndClinicIdAndIsDeleted(String chainId, String clinicId, int isDeleted);

    List<ClinicSurgeryRoom> findByChainIdAndClinicIdAndIdIn(String chainId, String clinicId, Collection<String> ids);

    List<ClinicSurgeryRoom> findByChainIdAndIdIn(String chainId, Collection<String> ids);

    List<ClinicSurgeryRoom> findByChainIdAndClinicIdAndNameAndIsDeleted(String chainId, String clinicId, String name, int isDeleted);
}
