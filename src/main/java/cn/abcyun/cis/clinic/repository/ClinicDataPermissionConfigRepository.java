package cn.abcyun.cis.clinic.repository;

import cn.abcyun.cis.clinic.model.ClinicDataPermissionConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/7 11:12
 */
@Repository
public interface ClinicDataPermissionConfigRepository extends JpaRepository<ClinicDataPermissionConfig, Integer> {

    List<ClinicDataPermissionConfig> findByIsDeleted(int isDeleted);
}
