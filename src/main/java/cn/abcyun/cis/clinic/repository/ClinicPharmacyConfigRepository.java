package cn.abcyun.cis.clinic.repository;

import cn.abcyun.cis.clinic.model.ClinicPharmacyConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/26 14:24
 */
@Repository
public interface ClinicPharmacyConfigRepository extends JpaRepository<ClinicPharmacyConfig, String> {
    List<ClinicPharmacyConfig> findByTypeAndIsDeletedOrderBySortAsc(int type, int isDeleted);
}
