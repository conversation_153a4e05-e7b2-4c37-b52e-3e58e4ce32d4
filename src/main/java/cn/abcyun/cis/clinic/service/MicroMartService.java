package cn.abcyun.cis.clinic.service;

import cn.abcyun.bis.rpc.sdk.cis.model.clinic.ClinicConstants;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.ClinicPurchaseItemKey;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.micromart.MicroMartOrganIdListRpcReq;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.micromart.MicroMartOrganListRpcRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.GoodsPharmacyView;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.config.GoodsClinicConfigView;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.config.GoodsConfigView;
import cn.abcyun.bis.rpc.sdk.cis.model.mall.goods.CreateGoodsReduceRuleReq;
import cn.abcyun.bis.rpc.sdk.cis.model.mall.goods.GoodsReduceRule;
import cn.abcyun.bis.rpc.sdk.cis.model.mall.goods.MallGoodsConst;
import cn.abcyun.bis.rpc.sdk.cis.model.wallet.CreatePayOrderRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.wallet.PayOrderCallbackReq;
import cn.abcyun.bis.rpc.sdk.cis.model.wallet.PayOrderCallbackRsp;
import cn.abcyun.cis.clinic.amqp.MQProducer;
import cn.abcyun.cis.clinic.api.view.ClinicCurrentEditionComposeView;
import cn.abcyun.cis.clinic.api.view.ClinicCurrentEditionView;
import cn.abcyun.cis.clinic.api.view.ClinicPurchaseItemView;
import cn.abcyun.cis.clinic.api.view.EmployeeBasicView;
import cn.abcyun.cis.clinic.api.view.micromart.*;
import cn.abcyun.cis.clinic.api.view.purchase.IndependentPurchaseItemPayOrderView;
import cn.abcyun.cis.clinic.client.GoodsClient;
import cn.abcyun.cis.clinic.dto.calculate.BuyOftenFeeCalculateDTO;
import cn.abcyun.cis.clinic.dto.purchase.IndependentPurchaseItemCalculateRequestDTO;
import cn.abcyun.cis.clinic.dto.purchase.IndependentPurchaseItemCalculateResponseDTO;
import cn.abcyun.cis.clinic.enums.CalculateRuleEnum;
import cn.abcyun.cis.clinic.exception.ClinicError;
import cn.abcyun.cis.clinic.exception.ClinicException;
import cn.abcyun.cis.clinic.model.*;
import cn.abcyun.cis.clinic.model.micromart.MicroMartOrgan;
import cn.abcyun.cis.clinic.model.purchase.PurchaseItemAccountOrder;
import cn.abcyun.cis.clinic.model.purchase.PurchaseItemOrder;
import cn.abcyun.cis.clinic.repository.micromart.MicroMartOrganRepository;
import cn.abcyun.cis.clinic.service.purchase.IndependentPurchaseItemCalculateFactory;
import cn.abcyun.cis.clinic.service.purchase.IndependentPurchaseItemCalculateProcessor;
import cn.abcyun.cis.clinic.service.purchase.IndependentPurchaseItemTransactionService;
import cn.abcyun.cis.clinic.utils.YesOrNo;
import cn.abcyun.cis.commons.exception.ParamNotValidException;
import cn.abcyun.cis.commons.exception.ParamRequiredException;
import cn.abcyun.cis.commons.message.MessageDestination;
import cn.abcyun.cis.commons.message.SmsMessageBody;
import cn.abcyun.cis.commons.message.SmsMessageType;
import cn.abcyun.cis.commons.message.ToBMessage;
import cn.abcyun.cis.commons.util.DateUtils;
import cn.abcyun.cis.commons.util.FillUtils;
import cn.abcyun.cis.commons.util.ListUtils;
import cn.abcyun.cis.commons.util.MathUtils;
import cn.abcyun.cis.core.db.UseReadOnlyDB;
import cn.abcyun.cis.core.redis.RedisLock;
import cn.abcyun.cis.core.util.ExecutorUtils;
import cn.abcyun.cis.core.util.JsonUtils;
import cn.abcyun.cis.core.util.Validators;
import cn.abcyun.cis.idgenerator.AbcIdGenerator;
import cn.abcyun.common.log.marker.AbcLogMarker;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/18 下午9:31
 * @description 微商城服务类
 */
@Slf4j
@Service
@EnableAspectJAutoProxy(exposeProxy = true)
public class MicroMartService {

    /**
     * 批量操作的批次大小(出略估算: 1000 * (32 * 2 + 12) / 1024 = 76 kb)
     */
    private final static int BATCH_SIZE = 1000;

    /**
     * 默认的药房号
     */
    private final static int DEFAULT_PHARMACY_NO = -1;

    /**
     * 独立购买项支付回调的地址
     */
    private final static String WALLET_PAY_CALLBACK_URL = "http://abc-cis-sc-clinic-service/rpc/v3/clinics/micro-mart/wallet/pay-callback";

    private final MQProducer producer;

    private final AbcIdGenerator idGenerator;

    private final OrganService organService;

    private final ClinicEditionService clinicEditionService;

    private final EmployeeService employeeService;

    private final MallGoodsService mallGoodsService;

    private final GoodsClient scGoodsService;

    private final IndependentPurchaseItemTransactionService independentPurchaseItemTransactionService;

    private final MicroMartOrganRepository microMartOrganRepository;

    private final IndependentPurchaseItemCalculateFactory independentPurchaseItemCalculateFactory;

    public MicroMartService(MQProducer producer,
                            AbcIdGenerator idGenerator,
                            OrganService organService,
                            ClinicEditionService clinicEditionService,
                            EmployeeService employeeService,
                            MallGoodsService mallGoodsService,
                            GoodsClient scGoodsService,
                            IndependentPurchaseItemTransactionService independentPurchaseItemTransactionService,
                            MicroMartOrganRepository microMartOrganRepository,
                            IndependentPurchaseItemCalculateFactory independentPurchaseItemCalculateFactory) {
        this.producer = producer;
        this.idGenerator = idGenerator;
        this.organService = organService;
        this.clinicEditionService = clinicEditionService;
        this.employeeService = employeeService;
        this.mallGoodsService = mallGoodsService;
        this.scGoodsService = scGoodsService;
        this.independentPurchaseItemTransactionService = independentPurchaseItemTransactionService;
        this.microMartOrganRepository = microMartOrganRepository;
        this.independentPurchaseItemCalculateFactory = independentPurchaseItemCalculateFactory;
    }

    /**
     * 过期清理微商城(虚拟门店 + 服务门店)任务 + 短信发送
     */
    @Transactional(rollbackFor = Exception.class)
    public void clearExpireChainClinicScheduledTask() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        List<String> chainIdList = microMartOrganRepository.findByIsDeleted(0);
        if (CollectionUtils.isEmpty(chainIdList)) {
            return;
        }
        List<List<String>> partitionList = Lists.partition(chainIdList, BATCH_SIZE);
        for (List<String> subChainIdList : partitionList) {
            List<MicroMartOrgan> organList = microMartOrganRepository.findByChainIdInAndIsDeleted(subChainIdList, 0);
            this.clearExpiredOrgan(organList);
            this.sendExpiredNotifySms(organList);
        }
        stopWatch.stop();
        log.info("清理过期的微商城(虚拟门店 + 服务门店)任务执行完毕,耗时: {}", stopWatch.getTotalTimeSeconds());
    }

    /**
     * 购买微商城(虚拟门店 + 服务门店)的基础参数的视图
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public BuyMicroMartOrganView getBuyMartOrganView(String chainId, String clinicId) {
        // Pair<Pair<机构信息, 机构版本信息>, Pair<虚拟门店价格, 服务门店价格>>
        Pair<Pair<Organ, ClinicCurrentEditionView>, Pair<List<ClinicPurchaseItemMaxAdjustment>, List<ClinicPurchaseItemMaxAdjustment>>> pair = this.beforeCheck(chainId, clinicId);
        // 表示当前已经购买过还在有效期内的服务门店数量
        List<ClinicCurrentPurchaseItem> purchaseItemList = clinicEditionService.getChainCurrentPurchaseItemList(chainId, Arrays.asList(ClinicPurchaseItemKey.MICRO_MART_CLOUD_ORGAN, ClinicPurchaseItemKey.MICRO_MART_SERVICE_ORGAN));
        if (CollectionUtils.isNotEmpty(purchaseItemList)) {
            if (purchaseItemList.stream().noneMatch(item -> StringUtils.equals(ClinicPurchaseItemKey.MICRO_MART_CLOUD_ORGAN, item.getPurchaseItemKey()))) {
                // 表示当前没有购买虚拟门店或者虚拟门店已失效,此时还有其它服务门店有效,说明配置有问题
                throw new ClinicException(ClinicError.MICRO_MART_PURCHASE_KEY_CONFIG_ERROR);
            }
        }
        BuyMicroMartOrganView view = new BuyMicroMartOrganView();
        Pair<List<ClinicPurchaseItemMaxAdjustment>, List<ClinicPurchaseItemMaxAdjustment>> maxAdjustment = pair.getSecond();
        // 设置微商城(虚拟门店)的购买价格信息
        view.setCloudOrganAdjustment(maxAdjustment.getFirst());
        // 设置微商城(服务门店)的购买价格信息
        view.setServiceOrganAdjustment(maxAdjustment.getSecond());

        int size = purchaseItemList.size();
        Organ organ = pair.getFirst().getFirst();
        boolean isSignChainClinic = this.isSignChainClinic(organ.getViewMode());
        // 设置微商城(虚拟门店)的截止时间
        Optional<ClinicCurrentPurchaseItem> optional = purchaseItemList.stream()
                .filter(item -> StringUtils.equals(ClinicPurchaseItemKey.MICRO_MART_CLOUD_ORGAN, item.getPurchaseItemKey()))
                .findFirst();
        if (optional.isPresent()) {
            ClinicCurrentPurchaseItem purchase = optional.get();
            LocalDateTime expiredTime = DateUtils.toLocalDateTime(purchase.getEndDate());
            view.setIsBuyCloudOrgan(YesOrNo.NO);
            // 已经有微商城(虚拟门店),则只计算购买服务门店的购买天数 ChronoUnit.DAYS.between计算规则:左开右闭
            int days = (int) Math.abs(ChronoUnit.DAYS.between(LocalDate.now(), expiredTime.toLocalDate()));
            view.setMaxDays(days + 1);
            view.setExpiredTime(expiredTime.with(LocalDate.MAX));
            if (isSignChainClinic) {
                // 0: 因为购买微商城(虚拟门店)会赠送一个微商城(服务门店)
                view.setServiceOrganMaxBuyNum(0);
            } else {
                List<Organ> organList = organService.findSubOrganListByParentId(chainId);
                if (CollectionUtils.isNotEmpty(organList)) {
                    view.setServiceOrganMaxBuyNum(Math.max(organList.size() - size + 1, 0));
                } else {
                    view.setServiceOrganMaxBuyNum(0);
                }
            }
        } else {
            // 设置最大可购买年限
            view.setIsBuyCloudOrgan(YesOrNo.YES);
            view.setMaxYears(3);
            if (isSignChainClinic) {
                // 0: 因为购买微商城(虚拟门店)会赠送一个微商城(服务门店)
                view.setServiceOrganMaxBuyNum(0);
            } else {
                List<Organ> organList = organService.findSubOrganListByParentId(chainId);
                // @TODO 该地方会存在一个bug 如果一段时间后连锁子店数量发生变化(到期失效),但是绑定的服务门店却还在,那么该接口会返回错误的数据,除非失效的同时删除绑定的服务门店信息
                if (CollectionUtils.isNotEmpty(organList)) {
                    view.setServiceOrganMaxBuyNum(Math.max(organList.size() - 1, 0));
                } else {
                    view.setServiceOrganMaxBuyNum(0);
                }
            }
        }
        return view;
    }

    /**
     * 续费微商城(虚拟门店 + 服务门店)的基础参数的视图
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public RenewMicroMartOrganView getRenewMartOrganView(String chainId, String clinicId) {
        // Pair<Pair<机构信息, 机构版本信息>, Pair<虚拟门店价格, 服务门店价格>>
        Pair<Pair<Organ, ClinicCurrentEditionView>, Pair<List<ClinicPurchaseItemMaxAdjustment>, List<ClinicPurchaseItemMaxAdjustment>>> pair = this.beforeCheck(chainId, clinicId);
        // 表示当前已经购买过还在有效期内微商城(虚拟门店+服务门店)
        List<ClinicCurrentPurchaseItem> purchaseItemList = clinicEditionService.getChainCurrentPurchaseItemList(chainId, Arrays.asList(ClinicPurchaseItemKey.MICRO_MART_CLOUD_ORGAN, ClinicPurchaseItemKey.MICRO_MART_SERVICE_ORGAN));
        if (CollectionUtils.isNotEmpty(purchaseItemList)) {
            if (purchaseItemList.stream().noneMatch(item -> StringUtils.equals(ClinicPurchaseItemKey.MICRO_MART_CLOUD_ORGAN, item.getPurchaseItemKey()))) {
                // 表示当前没有购买虚拟门店或者虚拟门店已失效,此时还有其它服务门店有效,说明配置有问题
                throw new ClinicException(ClinicError.MICRO_MART_PURCHASE_KEY_CONFIG_ERROR);
            }
        }
        // 表示当前已经购买过还在有效期内微商城(虚拟门店)信息
        ClinicCurrentPurchaseItem cloud = purchaseItemList.stream()
                .filter(item -> StringUtils.equals(ClinicPurchaseItemKey.MICRO_MART_CLOUD_ORGAN, item.getPurchaseItemKey()))
                .findFirst()
                .orElse(null);
        if (Objects.isNull(cloud)) {
            throw new ClinicException(ClinicError.MICRO_MART_NOT_ALLOWED_RENEW);
        }
        RenewMicroMartOrganView view = new RenewMicroMartOrganView();
        Pair<List<ClinicPurchaseItemMaxAdjustment>, List<ClinicPurchaseItemMaxAdjustment>> maxAdjustment = pair.getSecond();
        // 设置微商城(虚拟门店)的购买价格信息
        view.setCloudOrganAdjustment(maxAdjustment.getFirst());
        // 设置微商城(服务门店)的购买价格信息
        view.setServiceOrganAdjustment(maxAdjustment.getSecond());
        // 微商城(虚拟门店)的过期时间
        LocalDateTime expiredTime = DateUtils.endOfDay(DateUtils.toLocalDateTime(cloud.getEndDate()).toLocalDate());
        // 判断其它微商城(服务门店)的过期时间与微商城(虚拟门店)的过期时间进行比较
        int count = (int) purchaseItemList.stream().filter(item -> item.getEndDate().isBefore(cloud.getEndDate())).count();
        if (count == 0) {
            // 微商城(服务门店)的过期时间与微商城(虚拟门店)的过期时间是一致的,那么微商城(虚拟门店)必须要续费
            view.setIsRenewCloudOrgan(YesOrNo.YES);
            // 这里减2是因为购买微商城(虚拟门店)会赠送一个微商城(服务门店),这个服务门店的过期时间会随着微商城(虚拟门店)续费而变化
            view.setServiceOrganMaxBuyNum(purchaseItemList.size() - 2);
            // 最大续费时常3年
            view.setExpiredTime(expiredTime);
            view.setMaxYears(3);
        } else {
            // 只续费微商城(服务门店),续费截止时间必须跟微商城(虚拟门店)时间一致
            view.setIsRenewCloudOrgan(YesOrNo.NO);
            view.setServiceOrganMaxBuyNum(count);
            ClinicCurrentPurchaseItem purchase = purchaseItemList.stream()
                    .filter(item -> StringUtils.equals(ClinicPurchaseItemKey.MICRO_MART_SERVICE_ORGAN, item.getPurchaseItemKey()) && item.getEndDate().isBefore(cloud.getEndDate()))
                    .findFirst()
                    .orElse(null);
            LocalDateTime purchaseExpireTime;
            if (Objects.isNull(purchase)) {
                // 表示当前没有购买服务门店或者服务门店已失效,此时过期时间=微商城的过期时间
                purchaseExpireTime = expiredTime;
            } else {
                // 服务门店过期时间
                purchaseExpireTime = DateUtils.endOfDay(DateUtils.toLocalDateTime(purchase.getEndDate()).toLocalDate());
            }
            // 服务门店与微商城的过期时间比较
            int days = (int) Math.abs(ChronoUnit.DAYS.between(purchaseExpireTime.toLocalDate(), expiredTime.toLocalDate()));
            if (days < 0) {
                days = 0;
            }
            // 最大续费天数
            view.setMaxDays(days + 1);
            view.setExpiredTime(expiredTime);
        }
        return view;
    }

    /**
     * 计算购买微商城虚拟门店+服务门店的费用
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public CalculateMicroMartOrganOrderFeeView calculate(CalculateMicroMartOrganOrderFeeReq req) {
        Validators.validateEntity(req);
        return this.calculate(req, this.beforeCheck(req.getChainId(), req.getClinicId()));
    }

    /**
     * 微商城虚拟门店开通接口
     */
    @Transactional(rollbackFor = Exception.class)
    public IndependentPurchaseItemPayOrderView organPaidActivate(MicroMartOrganPaidActivateReq req) {
        Validators.validateEntity(req);
        String chainId = req.getChainId();
        String clinicId = req.getClinicId();
        String operationId = req.getEmployeeId();
        if (req.getBuyCloudOrganNum() != 1 && req.getBuyServiceOrganNum() == 0) {
            throw new ClinicException(ClinicError.MICRO_MART_BUY_NUM_NOT_EXIST);
        }
        // 1.检查参数 Pair<Pair<机构信息, 机构版本信息>, Pair<虚拟门店价格, 服务门店价格>>
        Pair<Pair<Organ, ClinicCurrentEditionView>, Pair<List<ClinicPurchaseItemMaxAdjustment>, List<ClinicPurchaseItemMaxAdjustment>>> pair = this.beforeCheck(chainId, clinicId);
        // 2.计算购买价格--判断与前端传入的价格是否一致
        CalculateMicroMartOrganOrderFeeView calculate = this.calculate(req, pair);
        if (calculate.getActualTotalPrice().compareTo(req.getPayAmount()) != 0) {
            throw new ClinicException(ClinicError.CALCUATE_PEICE_NOT_CONSISTENT);
        }
        // 1.无论新购还是续费,判断是否存在订单信息,如果存在则把之前未支付的订单全部置为关闭状态,重新生产新的订单信息.
        List<PurchaseItemOrder> itemOrderList = independentPurchaseItemTransactionService.getWaitPayItemOrderList(chainId, clinicId, Collections.singletonList(ClinicPurchaseItemKey.MICRO_MART_CLOUD_ORGAN));
        if (CollectionUtils.isNotEmpty(itemOrderList)) {
            Set<String> payOrderIds = new HashSet<>();
            itemOrderList.forEach(item -> {
                payOrderIds.add(item.getPayOrderId());
                item.setIsDeleted(YesOrNo.YES);
                FillUtils.fillLastModifiedBy(item, operationId);
            });
            // 关闭主支付订单
            List<ClinicEditionPayOrder> clinicEditionPayOrderList = clinicEditionService.findClinicEditionPayOrderList(payOrderIds);
            if (CollectionUtils.isNotEmpty(clinicEditionPayOrderList)) {
                List<String> walletPayOrderIdList = new ArrayList<>();
                clinicEditionPayOrderList.forEach(item -> {
                    walletPayOrderIdList.add(item.getPayOrderId());
                    item.setIsDeleted(YesOrNo.YES);
                    FillUtils.fillLastModifiedBy(item, operationId);
                });
                // 关闭wallet的支付订单
                independentPurchaseItemTransactionService.batchClosePayAbcOrder(walletPayOrderIdList, operationId);
                clinicEditionService.saveClinicEditionPayOrderList(clinicEditionPayOrderList);
            }
            independentPurchaseItemTransactionService.savePurchaseItemOrderList(itemOrderList);
        }
        List<PurchaseItemAccountOrder> accountOrderList = independentPurchaseItemTransactionService.getWaitPayAccountOrderList(chainId, clinicId, Collections.singletonList(ClinicPurchaseItemKey.MICRO_MART_SERVICE_ORGAN));
        if (CollectionUtils.isNotEmpty(accountOrderList)) {
            accountOrderList.forEach(item -> {
                item.setIsDeleted(YesOrNo.YES);
                FillUtils.fillLastModifiedBy(item, operationId);
            });
            independentPurchaseItemTransactionService.savePurchaseItemAccountOrderList(accountOrderList);
        }
        IndependentPurchaseItemPayOrderView view = new IndependentPurchaseItemPayOrderView();
        // 3创建钱包的支付订单
        String businessId = idGenerator.getUID();
        CreatePayOrderRsp walletPayOrder = independentPurchaseItemTransactionService.createPayAbcOrder(clinicId, businessId, req.getPayAmount(), operationId, WALLET_PAY_CALLBACK_URL);
        String payOrderId = walletPayOrder.getId();
        view.setId(payOrderId);
        view.setAccountId(walletPayOrder.getAccountId());
        view.setAccountBalance(walletPayOrder.getAccountBalance());
        view.setStatus(walletPayOrder.getStatus());
        view.setTotalFee(walletPayOrder.getTotalFee());

        Organ organ = pair.getFirst().getFirst();
        ClinicEditionPayOrder payOrder = this.createPayOrder(chainId, clinicId, organ.getNodeType(), payOrderId, operationId, req, calculate);
        view.setPayOrder(payOrder);
        log.info(AbcLogMarker.MARKER_LONG_TIME, "支付时生成的订单信息: {}", JsonUtils.dump(view));
        // 同时生成rocket的死信队列,到期微支付取消订单
        this.sendPurchaseItemTransactionRecordTimeoutMessage(payOrder.getId(), operationId);
        return view;
    }

    /**
     * 计算价格
     */
    private CalculateMicroMartOrganOrderFeeView calculate(CalculateMicroMartOrganOrderFeeReq req, Pair<Pair<Organ, ClinicCurrentEditionView>, Pair<List<ClinicPurchaseItemMaxAdjustment>, List<ClinicPurchaseItemMaxAdjustment>>> pair) {
        Organ organ = pair.getFirst().getFirst();
        this.beforeCheckPaid(organ, req.getBuyMethod(), req.getBuyCloudOrganNum(), req.getBuyServiceOrganNum());
        // 1.组装日期
        this.setBeginEndTime(req.getChainId(), DateUtils.toLocalDateTime(pair.getFirst().getSecond().getEndDate()), req);

        // 2.开始计算
        List<ClinicPurchaseItemMaxAdjustment> cloudOrganList = pair.getSecond().getFirst();
        IndependentPurchaseItemCalculateResponseDTO cloudOrganPrice = this.calculateCloudOrganPrice(req, cloudOrganList.stream().filter(item -> item.getBuyMethod() == req.getBuyMethod()).findFirst().orElse(null));

        List<ClinicPurchaseItemMaxAdjustment> serviceOrganList = pair.getSecond().getSecond();
        IndependentPurchaseItemCalculateResponseDTO serviceOrganPrice = this.calculateServiceOrganPrice(req, serviceOrganList.stream().filter(item -> item.getBuyMethod() == req.getBuyMethod()).findFirst().orElse(null));

        CalculateMicroMartOrganOrderFeeView feeView = new CalculateMicroMartOrganOrderFeeView();
        BuyOftenFeeCalculateDTO cloudCalculateBuyYears = cloudOrganPrice.getCalculateBuyYears();
        if (Objects.isNull(cloudCalculateBuyYears)) {
            cloudCalculateBuyYears = new BuyOftenFeeCalculateDTO();
        }
        feeView.setBuyCloudOrganNum(cloudOrganPrice.getBuyNum());
        feeView.setCloudOrganOriginPrice(cloudCalculateBuyYears.getOriginalUnitPrice());
        feeView.setCloudOrganOriginTotalPrice(cloudCalculateBuyYears.getOriginalTotalFee());
        feeView.setCloudOrganActualTotalPrice(cloudCalculateBuyYears.getReceivableFee());

        BuyOftenFeeCalculateDTO serviceCalculateBuyYears = serviceOrganPrice.getCalculateBuyYears();
        if (Objects.isNull(serviceCalculateBuyYears)) {
            serviceCalculateBuyYears = new BuyOftenFeeCalculateDTO();
        }
        feeView.setBuyServiceOrganNum(serviceOrganPrice.getBuyNum());
        feeView.setServiceOrganOriginPrice(serviceCalculateBuyYears.getOriginalUnitPrice());
        feeView.setServiceOrganOriginTotalPrice(serviceCalculateBuyYears.getOriginalTotalFee());
        feeView.setServiceOrganActualTotalPrice(serviceCalculateBuyYears.getReceivableFee());

        feeView.setOriginTotalPrice(feeView.getCloudOrganOriginTotalPrice().add(feeView.getServiceOrganOriginTotalPrice()));
        feeView.setActualTotalPrice(feeView.getCloudOrganActualTotalPrice().add(feeView.getServiceOrganActualTotalPrice()));
        feeView.setExpireTime(req.getEndTime());
        return feeView;
    }

    /**
     * 独立购买项的支付回调
     */
    @Transactional(rollbackFor = Exception.class)
    @RedisLock(key = "'sc_clinic_micro_pay_lock:' + #callbackReq.id", leaseTime = 10)
    public PayOrderCallbackRsp purchaseWalletPayCallback(PayOrderCallbackReq callbackReq) {
        if (Objects.isNull(callbackReq) || CollectionUtils.isEmpty(callbackReq.getItems())) {
            // 发送告警消息到小喇叭
            log.error(AbcLogMarker.MARKER_LONG_TIME, "微商城支付回调的订单信息异常.");
            throw new ClinicException(ClinicError.PAY_ORDER_ITEM_NOT_EXISTED);
        }
        callbackReq.setPayTime(Instant.now());
        log.info(AbcLogMarker.MARKER_LONG_TIME, "微商城的支付回调信息: {}", JsonUtils.dump(callbackReq));
        PayOrderCallbackRsp rsp = new PayOrderCallbackRsp();
        try {
            String payOrderId = callbackReq.getId();
            List<ClinicEditionPayOrder> payOrderList = clinicEditionService.findClinicEditionPayOrderList(Collections.singletonList(payOrderId), ClinicEditionPayOrder.Source.ORDER_BY_PURCHASE_ITEM, ClinicConstants.PayStatus.WAITING);
            // 防重复处理
            if (CollectionUtils.isEmpty(payOrderList)) {
                log.info(AbcLogMarker.MARKER_LONG_TIME, "微商城的支付回调没有需要待支付的订单信息: {}", payOrderId);
                return rsp;
            }
            // 非特殊情况只有一条数据
            for (ClinicEditionPayOrder payOrder : payOrderList) {
                // 获取全局的chainId,并判断当前门店是单电还是连锁门店
                String chainId = payOrder.getChainId();
                String clinicId = payOrder.getClinicId();
                Organ organ = organService.getOrganById(clinicId);
                boolean signChainClinic = this.isSignChainClinic(organ.getViewMode());

                // 1.更新v2_clinic_edition_pay_order表的支付时间、支付状态、支付金额、支付方式等信息
                payOrder.setPaidTime(callbackReq.getPayTime());
                payOrder.setStatus(ClinicConstants.PayStatus.PAID);
                payOrder.setPayMode(callbackReq.getPayMode());
                payOrder.setPaidFee(MathUtils.wrapBigDecimalOrZero(callbackReq.getTotalFee()));
                clinicEditionService.bindPayOrderReceiveAccountId(payOrder, callbackReq.getOwnReceiveAccountId());
                clinicEditionService.saveClinicEditionPayOrderList(Collections.singletonList(payOrder));

                // 2.更新对应的v2_clinic_purchase_item_order表的支付时间、支付状态、支付金额、支付方式等信息
                List<PurchaseItemOrder> waitPayItemOrderList = independentPurchaseItemTransactionService.getWaitPayItemOrderList(Collections.singletonList(payOrder.getId()));
                PurchaseItemOrder itemOrder = null;
                if (CollectionUtils.isNotEmpty(waitPayItemOrderList)) {
                    if (waitPayItemOrderList.size() > 1) {
                        throw new ClinicException(ClinicError.MICRO_MART_ORDER_NOT_EXCEED_MORE);
                    }
                    itemOrder = waitPayItemOrderList.get(0);
                    itemOrder.setPaidTime(DateUtils.toLocalDateTime(callbackReq.getPayTime()));
                    itemOrder.setStatus(ClinicConstants.PayStatus.PAID);
                    itemOrder.setPayMode(callbackReq.getPayMode());
                    itemOrder.setPaidFee(itemOrder.getReceivableFee());
                    itemOrder.setInvoiceStatus(Constants.OrderInvoiceStatus.NOT_APPLIED);
                    itemOrder.setLastModified(LocalDateTime.now());
                    independentPurchaseItemTransactionService.savePurchaseItemOrderList(Collections.singletonList(itemOrder));
                }

                // 3.更新对应的v2_clinic_purchase_item_account_order表的支付时间、支付状态、支付金额、支付方式等信息
                List<PurchaseItemAccountOrder> waitPayAccountOrderList = independentPurchaseItemTransactionService.getWaitPayItemAccountOrderList(Collections.singletonList(payOrder.getId()));
                if (CollectionUtils.isNotEmpty(waitPayAccountOrderList)) {
                    waitPayAccountOrderList.forEach(item -> {
                        item.setPaidTime(DateUtils.toLocalDateTime(callbackReq.getPayTime()));
                        item.setStatus(ClinicConstants.PayStatus.PAID);
                        item.setPayMode(callbackReq.getPayMode());
                        item.setPaidFee(item.getReceivableFee());
                        item.setInvoiceStatus(Constants.OrderInvoiceStatus.NOT_APPLIED);
                        item.setLastModified(LocalDateTime.now());
                    });
                    independentPurchaseItemTransactionService.savePurchaseItemAccountOrderList(waitPayAccountOrderList);
                }

                // 4.根据结果查询如果是虚拟门店,则更新ClinicPurchaseItemView独立购买项的信息
                if (Objects.nonNull(itemOrder)) {
                    // 执行到此步骤表示购买或者续费过微商城(虚拟门店),此时需要新建/更新ClinicPurchaseItemView独立购买项的信息
                    List<ClinicCurrentPurchaseItem> viewList = clinicEditionService.getChainCurrentPurchaseItemList(chainId, Collections.singletonList(ClinicPurchaseItemKey.MICRO_MART_CLOUD_ORGAN));
                    log.info(AbcLogMarker.MARKER_LONG_TIME, "微商城(虚拟门店)数据库信息: {}", JsonUtils.dump(viewList));
                    ClinicCurrentPurchaseItem purchaseItem;
                    if (CollectionUtils.isNotEmpty(viewList)) {
                        if (viewList.size() > 1) {
                            // 存在多个微商城的独立购买项时,发送小喇叭消息进行提示处理
                            throw new ClinicException(ClinicError.MICRO_MART_PURCHASE_NOT_EXCEED_MORE);
                        }
                        purchaseItem = viewList.get(0);
                        purchaseItem.setEndDate(DateUtils.toInstant(itemOrder.getEndTime()));
                    } else {
                        purchaseItem = clinicEditionService.createClinicCurrentPurchaseItem(chainId, chainId, itemOrder.getPurchaseItemKey(), DateUtils.toInstant(itemOrder.getBeginTime()), DateUtils.toInstant(itemOrder.getEndTime()), itemOrder.getReceivableFee(), itemOrder.getCreatedBy());
                        // 发送MQ消息通知已经新购买了微商城(虚拟门店)
                        producer.sendMicroMartFinishMessage(chainId, chainId);
                    }
                    clinicEditionService.saveClinicEditionPurchaseItem(purchaseItem);
                    // 更新绑定微商城(虚拟门店)上的过期时间
                    MicroMartOrgan microMartOrgan = microMartOrganRepository.findByChainIdAndClinicIdAndIsDeleted(chainId, chainId, YesOrNo.NO);
                    if (Objects.nonNull(microMartOrgan)) {
                        microMartOrgan.setExpiredTime(itemOrder.getEndTime());
                        log.info(AbcLogMarker.MARKER_LONG_TIME, "微商城(虚拟门店)更新过期时间: {}", JsonUtils.dump(microMartOrgan));
                    } else {
                        microMartOrgan = this.createMicroMartOrgan(organ, purchaseItem.getId(), itemOrder.getEndTime(), purchaseItem.getLastModifiedBy(), true, 0);
                        log.info(AbcLogMarker.MARKER_LONG_TIME, "微商城(虚拟门店)创建: {}", JsonUtils.dump(microMartOrgan));
                    }
                    microMartOrganRepository.save(microMartOrgan);
                }

                // 5.处理微商城(服务门店)的数据信息
                Set<PurchaseItemAccountOrder> addServiceOrganSet = waitPayAccountOrderList.stream()
                        .filter(item -> StringUtils.equals(item.getPurchaseItemKey(), ClinicPurchaseItemKey.MICRO_MART_SERVICE_ORGAN) && Constants.BuyMethod.ADD == item.getBuyMethod())
                        .collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(addServiceOrganSet)) {
                    log.info(AbcLogMarker.MARKER_LONG_TIME, "微商城(服务门店)新增购买项: {}", JsonUtils.dump(addServiceOrganSet));
                    int size = addServiceOrganSet.size();
                    List<ClinicCurrentPurchaseItem> itemList = new ArrayList<>(size);
                    List<MicroMartOrgan> microMartOrganList = new ArrayList<>(size);
                    addServiceOrganSet.forEach(item -> {
                        ClinicCurrentPurchaseItem purchase = clinicEditionService.createClinicCurrentPurchaseItem(chainId, signChainClinic ? item.getClinicId() : "", item.getPurchaseItemKey(), DateUtils.toInstant(item.getBeginTime()), DateUtils.toInstant(item.getEndTime()), item.getReceivableFee(), item.getCreatedBy());
                        itemList.add(purchase);
                        // 如果是单店,则创建微商城(服务门店)
                        if (signChainClinic) {
                            microMartOrganList.add(this.createMicroMartOrgan(organ, purchase.getId(), item.getEndTime(), purchase.getLastModifiedBy(), false, 1));
                        }
                    });
                    clinicEditionService.saveClinicEditionPurchaseItemList(itemList);
                    microMartOrganRepository.saveAll(microMartOrganList);
                }
                Set<PurchaseItemAccountOrder> renewServiceOrganSet = waitPayAccountOrderList.stream()
                        .filter(item -> StringUtils.equals(item.getPurchaseItemKey(), ClinicPurchaseItemKey.MICRO_MART_SERVICE_ORGAN) && Constants.BuyMethod.RENEW == item.getBuyMethod())
                        .collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(renewServiceOrganSet)) {
                    log.info(AbcLogMarker.MARKER_LONG_TIME, "微商城(服务门店)更新购买项: {}", JsonUtils.dump(renewServiceOrganSet));
                    LocalDateTime expiredTime = renewServiceOrganSet.stream().map(PurchaseItemAccountOrder::getEndTime).findFirst().orElse(LocalDateTime.now());
                    // 如果续费的支付信息不为空,则随机选出相同的购买项,更新其过期时间
                    List<ClinicCurrentPurchaseItem> viewList = clinicEditionService.getChainCurrentPurchaseItemList(chainId, Collections.singletonList(ClinicPurchaseItemKey.MICRO_MART_SERVICE_ORGAN));
                    if (CollectionUtils.isNotEmpty(viewList) && viewList.size() >= renewServiceOrganSet.size()) {
                        // 优先选择快要过期的门店
                        viewList.sort(Comparator.comparing(ClinicCurrentPurchaseItem::getEndDate));
                        viewList = viewList.subList(0, renewServiceOrganSet.size());
                        viewList.forEach(item -> item.setEndDate(DateUtils.toInstant(expiredTime)));
                        // 更新过期时间,同时更新绑定服务门店的过期时间
                        clinicEditionService.saveClinicEditionPurchaseItemList(viewList);
                        Set<String> idSet = viewList.stream().map(ClinicCurrentPurchaseItem::getId).collect(Collectors.toSet());
                        // 获取已经绑定的微商城(服务门店)信息
                        List<MicroMartOrgan> microMartOrganList = this.getMicroMartOrganList(chainId);
                        if (CollectionUtils.isNotEmpty(microMartOrganList)) {
                            // 处理绑定的服务门店数据过期时间
                            microMartOrganList = microMartOrganList.stream().filter(item -> idSet.contains(item.getPurchaseItemId())).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(microMartOrganList)) {
                                microMartOrganList.forEach(item -> item.setExpiredTime(expiredTime));
                                microMartOrganRepository.saveAll(microMartOrganList);
                                log.info(AbcLogMarker.MARKER_LONG_TIME, "微商城(服务门店)更新过期时间: {}", JsonUtils.dump(microMartOrganList));
                            }
                        }
                    }  else {
                        log.error(AbcLogMarker.MARKER_LONG_TIME, "微商城的支付回调更新独立购买项的过期时间时错误,没有足够的独立购买项: {}", JsonUtils.dump(renewServiceOrganSet));
                        throw new ClinicException(ClinicError.MICRO_MART_UPDATE_EXPIRE_TIME_ERROR);
                    }
                }
                // 6.清除门店的缓存信息
                payOrderList.forEach(item -> clinicEditionService.deleteClinicCurrentEditionComposeCache(item.getClinicId()));
            }
            rsp.setCode(200);
        } catch (Exception e) {
            // 发生异常,发送小喇叭消息
            producer.sendServiceAlertMessage("微商城支付回调", e.getMessage());
        }
        return rsp;
    }

    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public MicroMartOrganRsp findByChainIdAndClinicId(String chainId, String clinicId) {
        MicroMartOrgan mmo = microMartOrganRepository.findByChainIdAndClinicIdAndIsDeleted(chainId, clinicId, YesOrNo.NO);
        if (Objects.isNull(mmo)) {
            return null;
        }
        if (LocalDate.now().isAfter(mmo.getExpiredTime().toLocalDate())) {
            return null;
        }
        Organ organ = organService.getOrganById(clinicId);
        this.getOrganEmployeeAdmin(Collections.singletonList(organ), Collections.singletonList(mmo));
        return MicroMartOrganRsp.build(mmo);
    }

    /**
     * 根据id查询微商城门店服务
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public MicroMartOrganRsp findMicroMartOrganById(Long id) {
        if (id == null) {
            throw new ParamRequiredException("绑定门店的唯一值不能为空.");
        }
        MicroMartOrgan mmo = microMartOrganRepository.findById(id).orElse(null);
        if (Objects.isNull(mmo)) {
            return null;
        }
        Organ organ = organService.getOrganById(mmo.getClinicId());
        this.getOrganEmployeeAdmin(Collections.singletonList(organ), Collections.singletonList(mmo));
        return MicroMartOrganRsp.build(mmo, this.createGoodsReduceRule(mmo.getClinicId(), mmo.getIsCloudOrgan() == YesOrNo.YES, mmo.getGoodsReduceRule()));
    }

    /**
     * 查询微商城虚拟门店 + 门店的列表视图
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public MicroMartOrganView listMicroMartOrganByChainId(String chainId) {
        if (StringUtils.isBlank(chainId)) {
            throw new ParamRequiredException("请求参数: chain-id不能为空值.");
        }
        MicroMartOrganView view = new MicroMartOrganView();
        // 获取微商城(虚拟门店)的信息
        List<ClinicCurrentPurchaseItem> cloudPurchaseItemList = clinicEditionService.getChainCurrentPurchaseItemList(chainId, ClinicPurchaseItemKey.MICRO_MART_CLOUD_ORGAN);
        if (CollectionUtils.isEmpty(cloudPurchaseItemList)) {
            view.setRows(Collections.emptyList());
            return view;
        }
        // 此处至少有一条数据,虚拟的虚拟门店
        ClinicCurrentPurchaseItem purchaseItem = cloudPurchaseItemList.get(0);
        if (purchaseItem.isExpired()) {
            // -1表示已经失效了,需要续费操作
            view.setRemindDays(-1);
        }
        // 获取的是还未过期的服务门店数据集合
        List<ClinicCurrentPurchaseItem> serviceOrganList = clinicEditionService.getChainCurrentPurchaseItemList(chainId, Collections.singleton(ClinicPurchaseItemKey.MICRO_MART_SERVICE_ORGAN));

        // 已使用还是在有效期内的微商城(虚拟门店 + 服务门店)数据
        List<Organ> clinicList = organService.findOrganByParentId(chainId);
        List<MicroMartOrgan> organList = this.getNotExpireMicroMartOrganList(chainId);
        if (CollectionUtils.isNotEmpty(organList)) {
            this.getOrganEmployeeAdmin(clinicList, organList);
        }

        view.setClinicNum(clinicList.size());
        // 总数 = 虚拟门店 + 服务门店数量
        view.setTotal(1 + serviceOrganList.size());
        view.setUsed(organList.size());
        view.setExpiredTime(DateUtils.toLocalDateTime(purchaseItem.getEndDate()));
        view.setRows(this.convertMicroMartOrganRspList(organList));
        // 设置快要过期的提醒
        this.createPromptClinic(view, organList);
        return view;
    }

    /**
     * 根据chainId和clinicId查询微商城门店(虚拟门店 + 绑定的门店)的接口
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public MicroMartOrganListRpcRsp.MicroMartOrganRpcRsp getMicroMartOrganByChainIdAndClinicId(String chainId, String clinicId) {
        if (StringUtils.isBlank(chainId) || StringUtils.isBlank(clinicId)) {
            throw new ParamRequiredException("请求参数: 单店或连锁门店(chain-id、clinic-id)不能为空值.");
        }
        MicroMartOrgan microMartOrgan = microMartOrganRepository.findByChainIdAndClinicIdAndIsDeleted(chainId, clinicId, 0);
        if (Objects.isNull(microMartOrgan) || LocalDate.now().isAfter(microMartOrgan.getExpiredTime().toLocalDate())) {
            return null;
        }
        Organ organ = organService.getOrganById(clinicId);
        if (Objects.nonNull(organ)) {
            this.getOrganEmployeeAdmin(Collections.singletonList(organ), Collections.singletonList(microMartOrgan));
        }
        MicroMartOrganListRpcRsp.MicroMartOrganRpcRsp rsp = new MicroMartOrganListRpcRsp.MicroMartOrganRpcRsp();
        BeanUtils.copyProperties(microMartOrgan, rsp);
        return rsp;
    }

    /**
     * 根据id集合批量查询微商城门店(虚拟门店 + 绑定的门店)的接口
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public MicroMartOrganListRpcRsp getMicroMartOrganListByIds(MicroMartOrganIdListRpcReq req) {
        if (Objects.isNull(req) || CollectionUtils.isEmpty(req.getIds())) {
            return null;
        }
        Validators.validateEntity(req);
        return this.convertMicroMartOrganRpcRsp(microMartOrganRepository.findAllById(req.getIds()));
    }

    /**
     * 根据chainId查询微商城门店(虚拟门店 + 绑定的门店)的接口
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public MicroMartOrganListRpcRsp getMicroMartOrganListByChainId(String chainId) {
        if (StringUtils.isBlank(chainId)) {
            return null;
        }
        return this.convertMicroMartOrganRpcRsp(microMartOrganRepository.findByChainIdAndIsDeletedOrderBySortAsc(chainId, 0));
    }

    /**
     * 添加门店查询.连锁:查询连锁下的所有子店;单店:查询单店信息
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public List<MicroMartSelectClinicListView> listMicroMartOrganByViewMode(String chainId, String clinicId) {
        if (StringUtils.isBlank(chainId) || StringUtils.isBlank(clinicId)) {
            throw new ParamRequiredException("门店参数[chainId、clinicId]不能为空.");
        }
        Organ organ = organService.getOrganById(clinicId);
        if (Objects.isNull(organ)) {
            throw new ClinicException(ClinicError.CHAIN_CLINIC_EDITION_EXPIRED);
        }
        // 查询该虚拟的虚拟门店下已绑定的门店信息
        List<MicroMartOrgan> bindOrganList = this.getNotExpireMicroMartOrganList(chainId);
        // 已绑定门店的clinicId集合
        List<String> clinicIdList = bindOrganList.stream().map(MicroMartOrgan::getClinicId).collect(Collectors.toList());
        // 重点: 判断是单店还是连锁店
        if (this.isSignChainClinic(organ.getViewMode())) {
            // 排除绑定了的单店
            if (clinicIdList.contains(clinicId)) {
                return Collections.emptyList();
            }
            MicroMartSelectClinicListView view = MicroMartSelectClinicListView.build(organ);
            List<EmployeeBasicView> clinicAdmins = employeeService.findClinicAdmins(clinicId);
            if (CollectionUtils.isNotEmpty(clinicAdmins)) {
                EmployeeBasicView employeeBasicView = clinicAdmins.get(0);
                view.setAdmin(employeeBasicView.getName());
            }
            return Collections.singletonList(view);
        } else {
            List<Organ> organList = organService.findOrganByParentId(chainId);
            if (CollectionUtils.isEmpty(organList)) {
                return Collections.emptyList();
            }
            // 排除总部和已绑定了的门店
            List<MicroMartSelectClinicListView> viewList = organList.stream()
                    .filter(item -> !StringUtils.equals(item.getId(), organ.getId()) && !clinicIdList.contains(item.getId()))
                    .map(MicroMartSelectClinicListView::build).collect(Collectors.toList());
            Map<String, List<EmployeeBasicView>> map = employeeService.findClinicAdminsByClinicIdList(organList.stream().map(Organ::getId).collect(Collectors.toList()));
            if (MapUtils.isEmpty(map)) {
                return viewList;
            }
            // 设置门店的管理员及联系方式
            viewList.forEach(view -> {
                if (map.containsKey(view.getClinicId())) {
                    List<EmployeeBasicView> employeeBasicViewList = map.getOrDefault(view.getClinicId(), Collections.emptyList());
                    if (CollectionUtils.isNotEmpty(employeeBasicViewList)) {
                        EmployeeBasicView employeeBasicView = employeeBasicViewList.get(0);
                        view.setAdmin(employeeBasicView.getName());
                    }
                }
            });
            return viewList;
        }
    }

    /**
     * 批量添加微商城服务门店信息
     */
    @Transactional(rollbackFor = Exception.class)
    public List<MicroMartOrganRsp> insertBatchMicroMartOrganList(String chainId, String operation, List<MicroMartOrganReq> reqList) {
        if (StringUtils.isBlank(operation)) {
            throw new ParamRequiredException("操作人员operation]不能为空.");
        }
        if (CollectionUtils.isEmpty(reqList)) {
            throw new ParamRequiredException("绑定门店的设置信息不能为空.");
        }
        // 获取当前可以绑定的门店的所有id
        List<ClinicCurrentPurchaseItem> purchaseItemList = clinicEditionService.getChainCurrentPurchaseItemList(chainId, Collections.singletonList(ClinicPurchaseItemKey.MICRO_MART_SERVICE_ORGAN));
        if (CollectionUtils.isEmpty(purchaseItemList)) {
            throw new ClinicException(ClinicError.MICRO_MART_EXPIRED);
        }
        // 已添加门店的数据量(包含已删除和已过期的门店数量)
        int totalNum = microMartOrganRepository.countByChainId(chainId);
        // 获取还在有效期内的门店数量
        List<MicroMartOrgan> organList = this.getNotExpireMicroMartOrganList(chainId);
        // 之所以加1 是要计算微商城(虚拟门店的数量)
        if (CollectionUtils.isNotEmpty(organList) && organList.size() >= (purchaseItemList.size() + 1)) {
            throw new ClinicException(ClinicError.BIND_CLINIC_NUM_EXCEED_MAX);
        }
        // 已经使用了的独立购买项的id
        List<String> usePurchaseItemIdList = organList.stream().map(MicroMartOrgan::getPurchaseItemId).collect(Collectors.toList());
        List<String> purchaseItemIdList = purchaseItemList.stream().map(ClinicCurrentPurchaseItem::getId).collect(Collectors.toList());
        // 还可以使用的独立购买项的id
        purchaseItemIdList.removeAll(usePurchaseItemIdList);
        if (CollectionUtils.isEmpty(purchaseItemIdList) || reqList.size() > purchaseItemIdList.size()) {
            throw new ClinicException(ClinicError.BIND_CLINIC_NUM_EXCEED_MAX);
        }
        Map<String, ClinicCurrentPurchaseItem> purchaseItemMap = purchaseItemList.stream().collect(Collectors.toMap(ClinicCurrentPurchaseItem::getId, Function.identity()));
        int size = reqList.size();
        List<MicroMartOrgan> list = new ArrayList<>(size);
        List<ClinicCurrentPurchaseItem> updatePurchaseItemList = new ArrayList<>(size);
        for (int i = 0; i < size; i++) {
            String id = purchaseItemIdList.get(i);
            ClinicCurrentPurchaseItem purchaseItem = purchaseItemMap.get(id);
            MicroMartOrganReq req = reqList.get(i);
            purchaseItem.setClinicId(req.getClinicId());
            updatePurchaseItemList.add(purchaseItem);
            list.add(this.createMicroMartOrgan(req, totalNum++, operation, purchaseItem.getId(), DateUtils.toLocalDateTime(purchaseItem.getEndDate())));
        }
        // 1.保存绑定门店的信息
        microMartOrganRepository.saveAll(list);
        // 2.更新独立购买享的clinic_id的值
        clinicEditionService.saveClinicEditionPurchaseItemList(updatePurchaseItemList);
        return list.stream().map(MicroMartOrganRsp::build).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    public MicroMartOrganRsp updateMicroMartOrgan(String operation, MicroMartOrganReq req) {
        if (StringUtils.isBlank(operation)) {
            throw new ParamRequiredException("操作人员[operation]不能为空.");
        }
        if (Objects.isNull(req)) {
            throw new ParamRequiredException("绑定门店的设置信息不能为空.");
        }
        Validators.validateEntity(req);
        Long id = req.getId();
        if (id == null) {
            throw new ParamRequiredException("门店的[id]不能为空.");
        }
        MicroMartOrgan microMartOrgan = microMartOrganRepository.findById(id).orElse(null);
        if (Objects.isNull(microMartOrgan)) {
            throw new ClinicException(ClinicError.MICRO_MART_NOT_EXIST_BIND_CLINIC);
        }
        BeanUtils.copyProperties(req, microMartOrgan);
        OrganAddress address = req.getAddress();
        if (Objects.nonNull(address)) {
            microMartOrgan.setAddressProvinceId(address.getAddressProvinceId());
            microMartOrgan.setAddressProvinceName(address.getAddressProvinceName());
            microMartOrgan.setAddressCityId(address.getAddressCityId());
            microMartOrgan.setAddressCityName(address.getAddressCityName());
            microMartOrgan.setAddressDistrictId(address.getAddressDistrictId());
            microMartOrgan.setAddressDistrictName(address.getAddressDistrictName());
        }
        // 对传入的商品扣减库存规则进行校验,排除传入的药房号-1
        List<MicroMartOrganReq.GoodsReduceRuleReq> goodsReduceRuleReqList = req.getGoodsReduceRule();
        if (CollectionUtils.isNotEmpty(goodsReduceRuleReqList)) {
            goodsReduceRuleReqList = goodsReduceRuleReqList.stream()
                    .filter(rule -> StringUtils.isNotBlank(rule.getPharmacyNo()) && Integer.parseInt(rule.getPharmacyNo()) != DEFAULT_PHARMACY_NO)
                    .collect(Collectors.toList());
            List<GoodsReduceRule> goodsReduceRule = goodsReduceRuleReqList.stream()
                    .map(rule -> {
                        GoodsReduceRule reduceRule = new GoodsReduceRule();
                        reduceRule.setGoodsType(rule.getGoodsType());
                        reduceRule.setPharmacyName(rule.getPharmacyName());
                        reduceRule.setPharmacyNo(Integer.parseInt(rule.getPharmacyNo()));
                        reduceRule.setRuleType(rule.getRuleType());
                        return reduceRule;
                    }).collect(Collectors.toList());
            microMartOrgan.setGoodsReduceRule(goodsReduceRule);
        } else {
            microMartOrgan.setGoodsReduceRule(null);
        }
        microMartOrgan.setLastModified(LocalDateTime.now());
        microMartOrgan.setLastModifiedBy(operation);
        microMartOrgan = microMartOrganRepository.saveAndFlush(microMartOrgan);
        if (YesOrNo.NO == microMartOrgan.getIsCloudOrgan() && CollectionUtils.isNotEmpty(microMartOrgan.getGoodsReduceRule())) {
            this.callGoodsReduceRuleRpc(microMartOrgan.getId(), microMartOrgan.getChainId(), microMartOrgan.getClinicId(), operation, microMartOrgan.getGoodsReduceRule());
        }
        return MicroMartOrganRsp.build(microMartOrgan);
    }

    /**
     * 根据id删除门店(逻辑是更新删除字段由0变1)
     */
    @Transactional(rollbackFor = Exception.class)
    public MicroMartOrganRsp deleteMicroMartOrganById(Long id, String operationId) {
        if (id == null) {
            throw new ParamRequiredException("绑定门店的唯一值不能为空.");
        }
        MicroMartOrgan organ = microMartOrganRepository.findById(id).orElse(null);
        if (Objects.isNull(organ)) {
            throw new ParamRequiredException("绑定门店的数据不存在,请刷新界面后在重试.");
        }
        // 删除的同时还需要删除独立购买项的记录信息,然后重置一条记录信息
        ClinicCurrentPurchaseItem purchaseItem = clinicEditionService.getClinicCurrentPurchaseItem(organ.getPurchaseItemId());
        if (Objects.nonNull(purchaseItem)) {
            purchaseItem.setIsDeleted(YesOrNo.YES);
            purchaseItem.setLastModifiedBy(operationId);
            purchaseItem.setLastModified(Instant.now());
            if (purchaseItem.isValid()) {
                // 如果还在有效期内,删除的同时创建一条信息的独立购买项记录信息
                ClinicCurrentPurchaseItem purchase = clinicEditionService.createClinicCurrentPurchaseItem(organ.getChainId(), "", ClinicPurchaseItemKey.MICRO_MART_SERVICE_ORGAN, purchaseItem.getBeginDate(), purchaseItem.getEndDate(), purchaseItem.getPurchasePrice(), operationId);
                clinicEditionService.saveClinicEditionPurchaseItemList(Arrays.asList(purchaseItem, purchase));
            } else {
                clinicEditionService.saveClinicEditionPurchaseItem(purchaseItem);
            }
        }
        organ.setIsDeleted(YesOrNo.YES);
        organ.setLastModified(LocalDateTime.now());
        organ.setLastModifiedBy(operationId);
        microMartOrganRepository.save(organ);
        return MicroMartOrganRsp.build(organ);
    }

    /**
     * 通过门店创建微商城(虚拟门店)信息
     */
    private MicroMartOrgan createMicroMartOrgan(Organ organ, String purchaseItemId, LocalDateTime expireTime, String operation, boolean isCloud, int sort) {
        if (Objects.isNull(organ)) {
            return null;
        }
        MicroMartOrgan microMartOrgan = new MicroMartOrgan();
        microMartOrgan.setId(idGenerator.getUIDLong());
        microMartOrgan.setChainId(organ.getParentId());
        microMartOrgan.setClinicId(isCloud ? organ.getParentId() : organ.getId());
        microMartOrgan.setPurchaseItemId(purchaseItemId);
        microMartOrgan.setName(isCloud ? "微商城" : (StringUtils.isNotBlank(organ.getName()) ? organ.getName() : organ.getShortName()));
        microMartOrgan.setAddressProvinceId(organ.getAddressProvinceId());
        microMartOrgan.setAddressProvinceName(organ.getAddressProvinceName());
        microMartOrgan.setAddressCityId(organ.getAddressCityId());
        microMartOrgan.setAddressCityName(organ.getAddressCityName());
        microMartOrgan.setAddressDistrictId(organ.getAddressDistrictId());
        microMartOrgan.setAddressDistrictName(organ.getAddressDistrictName());
        microMartOrgan.setAddressDetail(organ.getAddressDetail());
        microMartOrgan.setAddressGeo(organ.getAddressGeo());
        List<EmployeeBasicView> clinicAdmins = employeeService.findClinicAdmins(organ.getId());
        if (CollectionUtils.isNotEmpty(clinicAdmins)) {
            EmployeeBasicView employeeBasicView = clinicAdmins.get(0);
            microMartOrgan.setAdmin(employeeBasicView.getName());
        } else {
            microMartOrgan.setAdmin(StringUtils.isNotBlank(organ.getName()) ? organ.getName() : organ.getShortName());
        }
        microMartOrgan.setContactPhone(organ.getContactPhone());
        microMartOrgan.setIsCloudOrgan(isCloud ? YesOrNo.YES : YesOrNo.NO);
        // 虚拟门店 + 实体门店都支持商品快递, 虚拟门店不支持商品自提和商品核销
        microMartOrgan.setIsSupportGoodsDelivery(YesOrNo.YES);
        if (isCloud) {
            microMartOrgan.setIsSupportGoodsSelfPickUp(YesOrNo.NO);
            microMartOrgan.setIsSupportGoodsVerify(YesOrNo.NO);
        } else {
            microMartOrgan.setIsSupportGoodsSelfPickUp(YesOrNo.YES);
            microMartOrgan.setIsSupportGoodsVerify(YesOrNo.YES);
        }
        // 营业时间设置
        if (Objects.isNull(microMartOrgan.getOpeningTime())) {
            microMartOrgan.setOpeningTime(LocalTime.of(8, 0));
        }
        if (Objects.isNull(microMartOrgan.getClosingTime())) {
            microMartOrgan.setClosingTime(LocalTime.of(18, 0));
        }
        microMartOrgan.setExpiredTime(expireTime);
        microMartOrgan.setSort(sort);
        microMartOrgan.setIsDeleted(0);
        microMartOrgan.setCreated(LocalDateTime.now());
        microMartOrgan.setCreatedBy(operation);
        microMartOrgan.setLastModified(LocalDateTime.now());
        microMartOrgan.setLastModifiedBy(operation);
        return microMartOrgan;
    }

    /**
     * 创建绑定的门店信息
     */
    private MicroMartOrgan createMicroMartOrgan(MicroMartOrganReq req, int sort, String operation, String purchaseItemId, LocalDateTime expireTime) {
        MicroMartOrgan microMartOrgan = new MicroMartOrgan();
        BeanUtils.copyProperties(req, microMartOrgan);
        microMartOrgan.setId(idGenerator.getUIDLong());
        // 虚拟门店 + 实体门店都支持商品快递, 虚拟门店不支持商品自提和商品核销
        microMartOrgan.setIsSupportGoodsDelivery(YesOrNo.YES);
        if (Objects.equals(YesOrNo.YES, microMartOrgan.getIsCloudOrgan())) {
            microMartOrgan.setIsSupportGoodsSelfPickUp(YesOrNo.NO);
            microMartOrgan.setIsSupportGoodsVerify(YesOrNo.NO);
        } else {
            microMartOrgan.setIsSupportGoodsSelfPickUp(YesOrNo.YES);
            microMartOrgan.setIsSupportGoodsVerify(YesOrNo.YES);
        }
        // 营业时间设置
        if (Objects.isNull(microMartOrgan.getOpeningTime())) {
            microMartOrgan.setOpeningTime(LocalTime.of(8, 0));
        }
        if (Objects.isNull(microMartOrgan.getClosingTime())) {
            microMartOrgan.setClosingTime(LocalTime.of(18, 0));
        }
        microMartOrgan.setPurchaseItemId(purchaseItemId);
        microMartOrgan.setExpiredTime(expireTime);
        microMartOrgan.setSort(sort);
        microMartOrgan.setIsDeleted(0);
        microMartOrgan.setCreated(LocalDateTime.now());
        microMartOrgan.setCreatedBy(operation);
        OrganAddress address = req.getAddress();
        if (Objects.nonNull(address)) {
            microMartOrgan.setAddressProvinceId(address.getAddressProvinceId());
            microMartOrgan.setAddressProvinceName(address.getAddressProvinceName());
            microMartOrgan.setAddressCityId(address.getAddressCityId());
            microMartOrgan.setAddressCityName(address.getAddressCityName());
            microMartOrgan.setAddressDistrictId(address.getAddressDistrictId());
            microMartOrgan.setAddressDistrictName(address.getAddressDistrictName());
        }
        microMartOrgan.setLastModified(LocalDateTime.now());
        microMartOrgan.setLastModifiedBy(operation);
        return microMartOrgan;
    }

    /**
     * 创建门店的商品扣减规则
     */
    private void callGoodsReduceRuleRpc(Long id, String chainId, String clinicId, String operationId, List<GoodsReduceRule> goodsReduceRuleList) {
        if (CollectionUtils.isEmpty(goodsReduceRuleList)) {
            return;
        }
        Organ organ = organService.getOrganById(clinicId);
        if (Objects.isNull(organ) || !Objects.equals(Organ.Status.NORMAL, organ.getStatus())) {
            return;
        }
        // 不是云店的时候需要调用库存的接口添加门店的商品扣库规则信息
        CreateGoodsReduceRuleReq goodsReduceRuleReq = new CreateGoodsReduceRuleReq();
        goodsReduceRuleReq.setChainId(chainId);
        goodsReduceRuleReq.setClinicId(clinicId);
        goodsReduceRuleReq.setMallOrganId(id);
        goodsReduceRuleReq.setOperatorId(operationId);
        goodsReduceRuleReq.setRules(goodsReduceRuleList);
        if (!mallGoodsService.createGoodsReduceRule(goodsReduceRuleReq)) {
            throw new ClinicException(ClinicError.UPDATE_REDUCE_STOCK_RULE_FAIL);
        }
    }

    /**
     * 判断是否是单店
     */
    private boolean isSignChainClinic(int viewMode) {
        return Organ.ViewMode.SINGLE == viewMode;
    }

    /**
     * 购买微商城(虚拟门店 + 服务门店)之前的检查
     */
    private void beforeCheckPaid(Organ organ, int buyMethod, int buyCloudOrganNum, int buyServiceOrganNum) {
        if (buyCloudOrganNum > 1) {
            throw new ParamNotValidException("购买微商城虚拟门店的数量值不正确.");
        }
        boolean isSignChainClinic = this.isSignChainClinic(organ.getViewMode());
        String chainId = organ.getParentId();
        int size = isSignChainClinic ? 1 : Optional.ofNullable(organService.findSubOrganListByParentId(chainId)).orElseGet(Lists::newArrayList).size();
        // 当前已经购买过还在有效期内的服务门店数量
        List<ClinicCurrentPurchaseItem> itemList = clinicEditionService.getChainCurrentPurchaseItemList(chainId, Arrays.asList(ClinicPurchaseItemKey.MICRO_MART_CLOUD_ORGAN, ClinicPurchaseItemKey.MICRO_MART_SERVICE_ORGAN));
        int cloudOrganNum = 0;
        int alreadyNum = 0;
        if (CollectionUtils.isNotEmpty(itemList)) {
            cloudOrganNum = (int) itemList.stream().filter(item -> Objects.equals(ClinicPurchaseItemKey.MICRO_MART_CLOUD_ORGAN, item.getPurchaseItemKey())).count();
            alreadyNum = (int) itemList.stream().filter(item -> Objects.equals(ClinicPurchaseItemKey.MICRO_MART_SERVICE_ORGAN, item.getPurchaseItemKey())).count();
        }
        if (Constants.BuyMethod.ADD == buyMethod) {
            // 判断微商城是否已经购买过,在未失效之前,购买过就不能再次购买,只能购买服务门店
            if (buyCloudOrganNum == 1 && cloudOrganNum > 0 ) {
                throw new ClinicException(ClinicError.MICRO_MART_EXIST);
            }
            // 还可以购买的服务门店数量
            int surplusUseNum = size - alreadyNum;
            if (buyServiceOrganNum > surplusUseNum) {
                throw new ClinicException(ClinicError.MICRO_MART_NOT_EXCEED_MAX_NUM);
            }
        }
        if (Constants.BuyMethod.RENEW == buyMethod) {
            Optional<ClinicCurrentPurchaseItem> optional = itemList.stream().filter(item -> Objects.equals(ClinicPurchaseItemKey.MICRO_MART_CLOUD_ORGAN, item.getPurchaseItemKey())).findFirst();
            if (cloudOrganNum == 0 || !optional.isPresent()) {
                throw new ClinicException(ClinicError.MICRO_MART_NOT_EXIST);
            }
            // 判断微商城(虚拟门店)是否需要续费
            ClinicCurrentPurchaseItem cloud = optional.get();
            Instant expiredTime = cloud.getEndDate();
            int count = (int) itemList.stream().filter(item -> item.getEndDate().isBefore(expiredTime)).count();
            if (count == 0 && buyCloudOrganNum == 0) {
                throw new ClinicException(ClinicError.MICRO_MART_MUST_RENEW);
            }
            if (count > 0 && buyCloudOrganNum > 0) {
                throw new ClinicException(ClinicError.MICRO_MART_MUST_NOT_RENEW);
            }
            if (buyServiceOrganNum > alreadyNum) {
                throw new ClinicException(ClinicError.MICRO_MART_NOT_NOT_EXCEED_SUB_ENABLED_NUM);
            }
        }
    }

    /**
     * 计算微商城(虚拟门店)的价格
     */
    private IndependentPurchaseItemCalculateResponseDTO calculateCloudOrganPrice(CalculateMicroMartOrganOrderFeeReq req, ClinicPurchaseItemMaxAdjustment adjustment) {
        IndependentPurchaseItemCalculateRequestDTO cloudReq = new IndependentPurchaseItemCalculateRequestDTO();
        cloudReq.setBuyNum(req.getBuyCloudOrganNum());
        cloudReq.setBuyYears(req.getYears());
        cloudReq.setBuyDays(0);
        cloudReq.setBuyMethod(req.getBuyMethod());
        cloudReq.setAdjustment(adjustment);
        IndependentPurchaseItemCalculateProcessor<IndependentPurchaseItemCalculateRequestDTO, IndependentPurchaseItemCalculateResponseDTO> processor = independentPurchaseItemCalculateFactory.processor(CalculateRuleEnum.CALCULATE_RULE_YEAR);
        return processor.calculate(cloudReq);
    }

    /**
     * 计算微商城(服务门店)的价格
     */
    private IndependentPurchaseItemCalculateResponseDTO calculateServiceOrganPrice(CalculateMicroMartOrganOrderFeeReq req, ClinicPurchaseItemMaxAdjustment adjustment) {
        IndependentPurchaseItemCalculateRequestDTO cloudReq = new IndependentPurchaseItemCalculateRequestDTO();
        cloudReq.setBuyNum(req.getBuyServiceOrganNum());
        cloudReq.setBuyMethod(req.getBuyMethod());
        cloudReq.setBuyYears(req.getYears());
        cloudReq.setBuyDays(0);
        cloudReq.setAdjustment(adjustment);
        IndependentPurchaseItemCalculateProcessor<IndependentPurchaseItemCalculateRequestDTO, IndependentPurchaseItemCalculateResponseDTO> processor = independentPurchaseItemCalculateFactory.processor(CalculateRuleEnum.CALCULATE_RULE_YEAR);
        return processor.calculate(cloudReq);
    }

    /**
     * 创建支付订单
     */
    private ClinicEditionPayOrder createPayOrder(String chainId, String clinicId, int nodeType, String payOrderId, String operationId, MicroMartOrganPaidActivateReq req, CalculateMicroMartOrganOrderFeeView calculate) {
        int buyCloudOrganNum = req.getBuyCloudOrganNum();
        int buyServiceOrganNum = req.getBuyServiceOrganNum();
        if (buyCloudOrganNum != 1 && buyServiceOrganNum == 0) {
            throw new ClinicException(ClinicError.MICRO_MART_BUY_NUM_NOT_EXIST);
        }
        // 1.创建v2_clinic_edition_pay_order订单信息
        ClinicEditionPayOrder payOrder = clinicEditionService.createClinicEditionPayOrder(chainId, clinicId, payOrderId, operationId, calculate.getOriginTotalPrice(),  BigDecimal.ZERO, calculate.getOriginTotalPrice().subtract(calculate.getActualTotalPrice()), BigDecimal.ZERO, BigDecimal.ZERO, calculate.getActualTotalPrice(), DateUtils.toInstant(req.getBuyOrderTime().plusSeconds(Constants.DELAYED_SECONDS)), null, null, ClinicEditionPayOrder.Source.ORDER_BY_PURCHASE_ITEM, null, null, null, null, null);
        clinicEditionService.saveClinicEditionPayOrder(payOrder);

        // 是否需要赠送一个额外的服务门店
        boolean isGive = false;
        // 2.创建v2_clinic_purchase_item_order订单信息
        if (buyCloudOrganNum == 1) {
            PurchaseItemOrder itemOrder = independentPurchaseItemTransactionService.createPurchaseItemOrder(chainId, clinicId, ClinicPurchaseItemKey.MICRO_MART_CLOUD_ORGAN, nodeType, req.getBuyMethod(), payOrder.getId(), req.getBeginTime(), req.getEndTime(), calculate.getCloudOrganUnit(), calculate.getBuyCloudOrganNum(), calculate.getCloudOrganOriginPrice(), calculate.getCloudOrganOriginTotalPrice(), calculate.getCloudOrganOriginTotalPrice().subtract(calculate.getCloudOrganActualTotalPrice()), calculate.getCloudOrganActualTotalPrice(), req.getBuyOrderTime().plusSeconds(Constants.DELAYED_SECONDS), operationId);
            independentPurchaseItemTransactionService.savePurchaseItemOrderList(Collections.singletonList(itemOrder));
            // 此处表示购买了微商城(虚拟门店),赠送一个服务门店
            isGive = true;
        }
        List<PurchaseItemAccountOrder> itemAccountOrderList = new ArrayList<>(buyServiceOrganNum);
        if (isGive) {
            itemAccountOrderList.add(independentPurchaseItemTransactionService.createPurchaseItemAccountOrder(chainId, clinicId, ClinicPurchaseItemKey.MICRO_MART_SERVICE_ORGAN, req.getBuyMethod(), payOrder.getId(), req.getBeginTime(), req.getEndTime(), calculate.getServiceOrganUnit(), 1, req.getYears(), BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, req.getBuyOrderTime().plusSeconds(Constants.DELAYED_SECONDS), operationId));
        }
        if (buyServiceOrganNum > 0) {
            BigDecimal totalPrice = calculate.getServiceOrganOriginTotalPrice().divide(BigDecimal.valueOf(buyServiceOrganNum), 2, RoundingMode.HALF_UP);
            BigDecimal discountPrice = (calculate.getServiceOrganOriginTotalPrice().subtract(calculate.getServiceOrganActualTotalPrice())).divide(BigDecimal.valueOf(buyServiceOrganNum), 2, RoundingMode.HALF_UP);
            BigDecimal receivableFee = calculate.getServiceOrganActualTotalPrice().divide(BigDecimal.valueOf(buyServiceOrganNum), 2, RoundingMode.HALF_UP);
            // 数量按1拆单
            for (int i = 0; i < buyServiceOrganNum; i++) {
                itemAccountOrderList.add(independentPurchaseItemTransactionService.createPurchaseItemAccountOrder(chainId, clinicId, ClinicPurchaseItemKey.MICRO_MART_SERVICE_ORGAN, req.getBuyMethod(), payOrder.getId(), req.getBeginTime(), req.getEndTime(), calculate.getServiceOrganUnit(), 1, req.getYears(), calculate.getServiceOrganOriginPrice(), totalPrice, discountPrice, receivableFee, req.getBuyOrderTime().plusSeconds(Constants.DELAYED_SECONDS), operationId));
            }
        }
        if (CollectionUtils.isNotEmpty(itemAccountOrderList)) {
            independentPurchaseItemTransactionService.savePurchaseItemAccountOrderList(itemAccountOrderList);
        }
        return payOrder;
    }

    /**
     * 发送30分钟未支付的delay消息发送
     */
    private void sendPurchaseItemTransactionRecordTimeoutMessage(String clinicEditionPayOrderId, String operationId) {
        producer.sendPurchaseItemOrderTimeoutMessage(clinicEditionPayOrderId, operationId, 0, Constants.DELAYED_SECONDS);
    }

    /**
     * 获取微商城(虚拟门店 + 服务门店)列表信息(包含已经失效但是还未删除的数据)
     */
    private List<MicroMartOrgan> getMicroMartOrganList(String chainId) {
        if (StringUtils.isBlank(chainId)) {
            return Lists.newArrayList();
        }
        return microMartOrganRepository.findByChainIdAndIsDeletedOrderBySortAsc(chainId, YesOrNo.NO);
    }

    /**
     * 获取微商城没有过期且没删除的(虚拟门店 + 服务门店)列表信息
     */
    private List<MicroMartOrgan> getNotExpireMicroMartOrganList(String chainId) {
        return this.getMicroMartOrganList(chainId).stream()
                .filter(organ -> !LocalDate.now().isAfter(organ.getExpiredTime().toLocalDate()))
                .collect(Collectors.toList());
    }

    /**
     * 转换微商城门店列表
     */
    private MicroMartOrganListRpcRsp convertMicroMartOrganRpcRsp(List<MicroMartOrgan> organList) {
        if (CollectionUtils.isEmpty(organList)) {
            return null;
        }
        organList = organList.stream().filter(organ -> !LocalDate.now().isAfter(organ.getExpiredTime().toLocalDate())).collect(Collectors.toList());
        Set<String> clinicIdList = organList.stream().map(MicroMartOrgan::getClinicId).collect(Collectors.toSet());
        List<Organ> organs = Optional.ofNullable(organService.findOrganBatch(clinicIdList)).orElse(Lists.newArrayList());
        this.getOrganEmployeeAdmin(organs, organList);
        MicroMartOrganListRpcRsp rpcRsp = new MicroMartOrganListRpcRsp();
        rpcRsp.setRows(organList.stream().map(organ -> {
            MicroMartOrganListRpcRsp.MicroMartOrganRpcRsp rsp = new MicroMartOrganListRpcRsp.MicroMartOrganRpcRsp();
            BeanUtils.copyProperties(organ, rsp);
            return rsp;
        }).collect(Collectors.toList()));
        return rpcRsp;
    }

    /**
     * 转换微商城门店列表
     */
    private List<MicroMartOrganRsp> convertMicroMartOrganRspList(List<MicroMartOrgan> organList) {
        if (CollectionUtils.isEmpty(organList)) {
            return null;
        }
        // 获取商品扣减库存规则信息
        Pair<Map<String, GoodsClinicConfigView>, Map<String, List<GoodsPharmacyView>>> pair = this.getGoodsStockAndPharmacyView(organList.stream().map(MicroMartOrgan::getClinicId).collect(Collectors.toList()));
        return organList.stream()
                .map(organ -> {
                    GoodsClinicConfigView configView = Optional.of(pair.getFirst()).orElse(new HashMap<>()).getOrDefault(organ.getClinicId(), null);
                    List<GoodsPharmacyView> pharmacyViewList = Optional.of(pair.getSecond()).orElse(new HashMap<>()).getOrDefault(organ.getClinicId(), Lists.newArrayList());
                    return MicroMartOrganRsp.build(organ, this.createGoodsReduceRule(organ.getIsCloudOrgan() == YesOrNo.YES, organ.getGoodsReduceRule(), configView, pharmacyViewList));
                }).collect(Collectors.toList());
    }


    /**
     * 获取商品库存配置信息
     */
    private Pair<Map<String, GoodsClinicConfigView>, Map<String, List<GoodsPharmacyView>>> getGoodsStockAndPharmacyView(List<String> clinicIdList) {
        // 商品库存配置信息,获取是否开启多药房信息
        List<GoodsConfigView> goodsConfigViews = Optional.ofNullable(scGoodsService.batchGetGoodsConfig(clinicIdList)).orElse(Lists.newArrayList());
        goodsConfigViews = goodsConfigViews.stream().filter(Objects::nonNull).collect(Collectors.toList());
        Map<String, GoodsClinicConfigView> goodsConfigMap = goodsConfigViews.stream()
                .map(GoodsConfigView::getStockGoodsConfig)
                .filter(item -> Objects.nonNull(item) && Objects.nonNull(item.getClinicId()))
                .collect(Collectors.toMap(GoodsClinicConfigView::getClinicId, Function.identity()));
        Map<String, List<GoodsPharmacyView>> pharmacyMap = goodsConfigViews.stream()
                .map(GoodsConfigView::getPharmacyList)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .filter(item -> Objects.nonNull(item) && Objects.nonNull(item.getClinicId()) && item.getStatus() == YesOrNo.YES)
                .collect(Collectors.groupingBy(GoodsPharmacyView::getClinicId, Collectors.toList()));
        return Pair.of(goodsConfigMap, pharmacyMap);
    }

    /**
     * 创建商品扣减库存规则
     */
    private GoodsReduceRuleView createGoodsReduceRule(String clinicId, boolean isCloudOrgan, List<GoodsReduceRule> goodsReduceRule) {
        Pair<Map<String, GoodsClinicConfigView>, Map<String, List<GoodsPharmacyView>>> pair = this.getGoodsStockAndPharmacyView(Collections.singletonList(clinicId));
        GoodsClinicConfigView configView = pair.getFirst().getOrDefault(clinicId, null);
        List<GoodsPharmacyView> pharmacyViewList = pair.getSecond().getOrDefault(clinicId, Lists.newArrayList());
        return this.createGoodsReduceRule(isCloudOrgan, goodsReduceRule, configView, pharmacyViewList);
    }

    /**
     * 初始化商品扣减库存规则
     */
    private GoodsReduceRuleView createGoodsReduceRule(boolean isCloudOrgan, List<GoodsReduceRule> goodsReduceRule, GoodsClinicConfigView configView, List<GoodsPharmacyView> pharmacyViewList) {
        GoodsReduceRuleView view = new GoodsReduceRuleView();
        view.setHasMultiPharmacy(YesOrNo.NO);
        if (isCloudOrgan) {
            return view;
        }
        // 20:表示开启了多药房
        if (Objects.isNull(configView) || configView.getOpenPharmacyFlag() != 20 || CollectionUtils.isEmpty(pharmacyViewList)) {
            return view;
        }
        view.setHasMultiPharmacy(YesOrNo.YES);
        List<GoodsReduceRuleView.ReduceRuleView> goodsReduceRuleViewList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(goodsReduceRule)) {
            view.setIsModify(YesOrNo.NO);
            goodsReduceRuleViewList.add(this.createReduceRule(MallGoodsConst.MallGoodsReduceRuleType.WEST, MallGoodsConst.ruleTypeToName.get(MallGoodsConst.MallGoodsReduceRuleType.WEST), -1, ""));
            goodsReduceRuleViewList.add(this.createReduceRule(MallGoodsConst.MallGoodsReduceRuleType.CHINESE_PATENT, MallGoodsConst.ruleTypeToName.get(MallGoodsConst.MallGoodsReduceRuleType.CHINESE_PATENT), -1, ""));
            goodsReduceRuleViewList.add(this.createReduceRule(MallGoodsConst.MallGoodsReduceRuleType.CHINESE_GRANULE, MallGoodsConst.ruleTypeToName.get(MallGoodsConst.MallGoodsReduceRuleType.CHINESE_GRANULE), -1, ""));
            goodsReduceRuleViewList.add(this.createReduceRule(MallGoodsConst.MallGoodsReduceRuleType.MATERIAL, MallGoodsConst.ruleTypeToName.get(MallGoodsConst.MallGoodsReduceRuleType.MATERIAL), -1, ""));
            goodsReduceRuleViewList.add(this.createReduceRule(MallGoodsConst.MallGoodsReduceRuleType.ADDITION, MallGoodsConst.ruleTypeToName.get(MallGoodsConst.MallGoodsReduceRuleType.ADDITION), -1, ""));
        } else {
            view.setIsModify(YesOrNo.YES);
            goodsReduceRule.forEach(rule -> goodsReduceRuleViewList.add(this.createReduceRule(rule.getRuleType(), rule.getGoodsType(), rule.getPharmacyNo(), rule.getPharmacyName())));
            Set<Integer> ruleTypeSet = goodsReduceRuleViewList.stream().map(GoodsReduceRuleView.ReduceRuleView::getRuleType).collect(Collectors.toSet());
            if (!ruleTypeSet.contains(MallGoodsConst.MallGoodsReduceRuleType.WEST)) {
                goodsReduceRuleViewList.add(this.createReduceRule(MallGoodsConst.MallGoodsReduceRuleType.WEST, MallGoodsConst.ruleTypeToName.get(MallGoodsConst.MallGoodsReduceRuleType.WEST), -1, ""));
            }
            if (!ruleTypeSet.contains(MallGoodsConst.MallGoodsReduceRuleType.CHINESE_PATENT)) {
                goodsReduceRuleViewList.add(this.createReduceRule(MallGoodsConst.MallGoodsReduceRuleType.CHINESE_PATENT, MallGoodsConst.ruleTypeToName.get(MallGoodsConst.MallGoodsReduceRuleType.CHINESE_PATENT), -1, ""));
            }
            if (!ruleTypeSet.contains(MallGoodsConst.MallGoodsReduceRuleType.CHINESE_GRANULE)) {
                goodsReduceRuleViewList.add(this.createReduceRule(MallGoodsConst.MallGoodsReduceRuleType.CHINESE_GRANULE, MallGoodsConst.ruleTypeToName.get(MallGoodsConst.MallGoodsReduceRuleType.CHINESE_GRANULE), -1, ""));
            }
            if (!ruleTypeSet.contains(MallGoodsConst.MallGoodsReduceRuleType.MATERIAL)) {
                goodsReduceRuleViewList.add(this.createReduceRule(MallGoodsConst.MallGoodsReduceRuleType.MATERIAL, MallGoodsConst.ruleTypeToName.get(MallGoodsConst.MallGoodsReduceRuleType.MATERIAL), -1, ""));
            }
            if (!ruleTypeSet.contains(MallGoodsConst.MallGoodsReduceRuleType.ADDITION)) {
                goodsReduceRuleViewList.add(this.createReduceRule(MallGoodsConst.MallGoodsReduceRuleType.ADDITION, MallGoodsConst.ruleTypeToName.get(MallGoodsConst.MallGoodsReduceRuleType.ADDITION), -1, ""));
            }
        }
        // 排序
        goodsReduceRuleViewList.sort(Comparator.comparingInt(GoodsReduceRuleView.ReduceRuleView::getRuleType));
        view.setReduceRuleList(goodsReduceRuleViewList);
        view.setGoodsPharmacyList(pharmacyViewList.stream().map(pharmacy -> {
            GoodsReduceRuleView.GoodsPharmacy goodsPharmacy = new GoodsReduceRuleView.GoodsPharmacy();
            goodsPharmacy.setNo(pharmacy.getNo());
            goodsPharmacy.setName(pharmacy.getName());
            return goodsPharmacy;
        }).collect(Collectors.toList()));
        return view;
    }

    /**
     * 创建商品扣减库存规则
     */
    private GoodsReduceRuleView.ReduceRuleView createReduceRule(int ruleType, String goodsType, int pharmacyNo, String pharmacyName) {
        GoodsReduceRuleView.ReduceRuleView reduceRuleView = new GoodsReduceRuleView.ReduceRuleView();
        reduceRuleView.setRuleType(ruleType);
        reduceRuleView.setGoodsType(goodsType);
        reduceRuleView.setPharmacyNo(pharmacyNo);
        reduceRuleView.setPharmacyName(pharmacyName);
        return reduceRuleView;
    }

    private Pair<Pair<Organ, ClinicCurrentEditionView>, Pair<List<ClinicPurchaseItemMaxAdjustment>, List<ClinicPurchaseItemMaxAdjustment>>> beforeCheck(String chainId, String clinicId) {
        if (StringUtils.isBlank(chainId) || StringUtils.isBlank(clinicId)) {
            throw new ParamRequiredException("请求参数: 门店(chain-id、clinic-id)不能为空值.");
        }
        Organ organ = organService.getOrganById(clinicId);
        if (Objects.isNull(organ) || Organ.Status.NORMAL != organ.getStatus()) {
            throw new ClinicException(ClinicError.ORGAN_NOT_EXISTED);
        }
        boolean isSignChainClinic = this.isSignChainClinic(organ.getViewMode());
        if (!isSignChainClinic && Constants.OrganNodeType.CHAIN_HEAD_CLINIC != organ.getNodeType()) {
            // 连锁子店没有拥有购买微商城的权限
            throw new ClinicException(ClinicError.CLINIC_BRANCH_BUY_MICRO_MARK_NOT_ALLOWED);
        }
        // 读取配置信息
        ClinicCurrentEditionComposeView editionComposeView = clinicEditionService.getClinicCurrentEditionComposeView(organ);
        if (Objects.isNull(editionComposeView) || CollectionUtils.isEmpty(editionComposeView.getPurchaseItems())) {
            throw new ClinicException(ClinicError.CLINIC_CONFIG_NOT_EXIST);
        }
        // 按照独立购买项的key分类
        Map<String, ClinicPurchaseItemView> viewMap = editionComposeView.getPurchaseItems().stream()
                .collect(Collectors.toMap(ClinicPurchaseItemView::getKey, Function.identity(), (first, second) -> first));
        return Pair.of(Pair.of(organ, editionComposeView.getEdition()), Pair.of(this.checkBefore(ClinicPurchaseItemKey.MICRO_MART_CLOUD_ORGAN, viewMap), this.checkBefore(ClinicPurchaseItemKey.MICRO_MART_SERVICE_ORGAN, viewMap)));
    }

    private List<ClinicPurchaseItemMaxAdjustment> checkBefore(String purchaseItemKey, Map<String, ClinicPurchaseItemView> viewMap) {
        if (!viewMap.containsKey(purchaseItemKey)) {
            // 微商城(虚拟门店)独立购买项不存在
            throw new ClinicException(ClinicError.MICRO_MART_CONFIG_NOT_EXIST);
        }
        ClinicPurchaseItemView purchase = viewMap.get(purchaseItemKey);
        List<ClinicPurchaseItemMaxAdjustment> maxAdjustmentList = purchase.getMaxAdjustment();
        if (CollectionUtils.isEmpty(maxAdjustmentList)) {
            throw new ClinicException(ClinicError.MICRO_MART_PRICE_CONFIG_NOT_EXIST);
        }
        Map<Integer, ClinicPurchaseItemMaxAdjustment> adjustmentMap = maxAdjustmentList.stream().collect(Collectors.toMap(ClinicPurchaseItemMaxAdjustment::getBuyMethod, Function.identity(), (first, second) -> first));
        if (!adjustmentMap.containsKey(Constants.BuyMethod.ADD) || !adjustmentMap.containsKey(Constants.BuyMethod.RENEW)) {
            throw new ClinicException(ClinicError.MICRO_MART_PRICE_CONFIG_NOT_ERROR);
        }
        return maxAdjustmentList;
    }

    /**
     * 设置计算费用的开始结束时间
     */
    private void setBeginEndTime(String chainId, LocalDateTime editionExpiredTime, CalculateMicroMartOrganOrderFeeReq req) {
        // 表示当前已经购买过还在有效期内微商城(虚拟门店+服务门店)
        List<ClinicCurrentPurchaseItem> purchaseItemList = clinicEditionService.getChainCurrentPurchaseItemList(chainId, Arrays.asList(ClinicPurchaseItemKey.MICRO_MART_CLOUD_ORGAN, ClinicPurchaseItemKey.MICRO_MART_SERVICE_ORGAN));
        if (CollectionUtils.isNotEmpty(purchaseItemList)) {
            if (purchaseItemList.stream().noneMatch(item -> StringUtils.equals(ClinicPurchaseItemKey.MICRO_MART_CLOUD_ORGAN, item.getPurchaseItemKey()))) {
                // 表示当前没有购买虚拟门店或者虚拟门店已失效,此时还有其它服务门店有效,说明配置有问题
                throw new ClinicException(ClinicError.MICRO_MART_PURCHASE_KEY_CONFIG_ERROR);
            }
        }
        // 表示当前已经购买过还在有效期内微商城(虚拟门店)信息
        ClinicCurrentPurchaseItem cloud = purchaseItemList.stream()
                .filter(item -> StringUtils.equals(ClinicPurchaseItemKey.MICRO_MART_CLOUD_ORGAN, item.getPurchaseItemKey()))
                .findFirst()
                .orElse(null);
        int years = req.getYears();
        if (Objects.isNull(cloud)) {
            // 表示还没有购买过微商城(虚拟门店)的信息
            if (Constants.BuyMethod.ADD == req.getBuyMethod()) {
                if (LocalDate.now().isAfter(editionExpiredTime.toLocalDate())) {
                    throw new ClinicException(ClinicError.CHAIN_CLINIC_EDITION_EXPIRED);
                }
                // 新购微商城(虚拟门店 + 服务门店) 起始时间是00:00:00 结束时间是:23:59:59
                req.setBeginTime(DateUtils.beginOfDay(LocalDate.now()));
                // 结束时间是当前时间加一年减一天,就刚好是一年的使用期限
                req.setEndTime(DateUtils.endOfDay(LocalDate.now().plusYears(years).minusDays(1)));
            } else {
                throw new ClinicException(ClinicError.MICRO_MART_NOT_EXIST);
            }
        } else {
            LocalDateTime cloudEndTime = DateUtils.endOfDay(DateUtils.toLocalDateTime(cloud.getEndDate()).toLocalDate());
            if (Constants.BuyMethod.ADD == req.getBuyMethod()) {
                // 只能是单独购买项才会执行此逻辑
                req.setBeginTime(DateUtils.beginOfDay(LocalDate.now()));
                // 单独购买微商城(服务门店)的截止日志只能是微商城(虚拟门店)的截止日期
                req.setEndTime(cloudEndTime);
            } else if (Constants.BuyMethod.RENEW == req.getBuyMethod()) {
                if (req.getBuyCloudOrganNum() == 1) {
                    // 微商城(虚拟门店 + 服务门店一起续费,则按照年续费到微商城(虚拟门店)的截止日期)
                    req.setBeginTime(DateUtils.beginOfDay(cloudEndTime.plusDays(1).toLocalDate()));
                    req.setEndTime(DateUtils.endOfDay(req.getBeginTime().plusYears(years).minusDays(1).toLocalDate()));
                } else {
                    // 单独续费服务门店,续费到微商城(虚拟门店)的截止日期
                    ClinicCurrentPurchaseItem purchase = purchaseItemList.stream()
                            .filter(item -> StringUtils.equals(ClinicPurchaseItemKey.MICRO_MART_SERVICE_ORGAN, item.getPurchaseItemKey()) && item.getEndDate().isBefore(cloud.getEndDate()))
                            .findFirst()
                            .orElse(null);
                    if (Objects.isNull(purchase)) {
                        throw new ClinicException(ClinicError.MICRO_MART_NOT_EXIST_RENEW_CLINIC);
                    } else {
                        req.setBeginTime(DateUtils.beginOfDay(DateUtils.toLocalDateTime(purchase.getEndDate()).plusDays(1).toLocalDate()));
                        req.setEndTime(cloudEndTime);
                    }
                }
            }
        }
    }

    /**
     * 创建快要过期的提醒
     */
    private void createPromptClinic(MicroMartOrganView view, List<MicroMartOrgan> organList) {
        if (CollectionUtils.isEmpty(organList)) {
            return;
        }
        // 微商城(虚拟门店)的过期时间
        organList.stream().filter(organ -> organ.getIsCloudOrgan() == YesOrNo.YES).findFirst().ifPresent(organ -> {
            int days = this.calculateDays(organ.getExpiredTime().toLocalDate());
            if (days <= 6) {
                view.setRemindDays(days < 0 ? -1 : (days + 1));
            }
        });
        // 只找出最小的时间进行过期时间提醒
        Map<LocalDate, Long> maps = organList.stream().filter(organ -> organ.getIsCloudOrgan() == YesOrNo.NO).collect(Collectors.groupingBy(item -> item.getExpiredTime().toLocalDate(), Collectors.counting()));
        Optional<Map.Entry<LocalDate, Long>> min = maps.entrySet().stream().min(Map.Entry.comparingByKey());
        min.ifPresent(map -> {
            int days = this.calculateDays(map.getKey());
            if (days <= 6) {
                view.setPromptClinicNum(map.getValue().intValue());
                view.setPromptDayNum(days < 0 ? -1 : (days + 1));
            }
        });
    }

    /**
     * 计算天数
     */
    private int calculateDays(LocalDate expireDate) {
        long between = ChronoUnit.DAYS.between(LocalDate.now(), expireDate);
        return between < 0 ? -1 : (int) between;
    }

    private void sendExpiredNotifySms(List<MicroMartOrgan> organList) {
        if (CollectionUtils.isEmpty(organList)) {
            return;
        }
        // 找出没有过期的且时间小于7天的微商城(虚拟门店 + 服务门店)
        Map<String, List<MicroMartOrgan>> almostExpiredOrganMap = organList.stream()
                .filter(organ -> !LocalDate.now().isAfter(organ.getExpiredTime().toLocalDate()) && !LocalDate.now().plusDays(6).isBefore(organ.getExpiredTime().toLocalDate()))
                .collect(Collectors.groupingBy(MicroMartOrgan::getChainId));
        if (MapUtils.isEmpty(almostExpiredOrganMap)) {
            return;
        }
        // 按照连锁分组后每组分别发送不同的短信
        almostExpiredOrganMap.forEach(this::expiredNotifySms);
    }

    private void expiredNotifySms(String chainId, List<MicroMartOrgan> organList) {
        if (CollectionUtils.isEmpty(organList)) {
            return;
        }
        ExecutorUtils.execute(() -> {
            List<Employee> clinicAdmins = employeeService.findAdminEmployeeList(chainId);
            MicroMartOrgan cloudOrgan = organList.stream().filter(organ -> organ.getIsCloudOrgan() == YesOrNo.YES).findFirst().orElse(null);
            if (Objects.nonNull(cloudOrgan)) {
                int days = this.calculateDays(cloudOrgan.getExpiredTime().toLocalDate());
                clinicAdmins.forEach(admin -> {
                    this.sendToBMessage(admin.getId(), admin.getMobile(), admin.getCountryCode(), true, 0, days + 1);
                });
            }
            Map<LocalDateTime, List<MicroMartOrgan>> serviceOrganListMap = organList.stream()
                    .filter(organ -> organ.getIsCloudOrgan() == YesOrNo.NO && Objects.nonNull(organ.getExpiredTime()))
                    .collect(Collectors.groupingBy(MicroMartOrgan::getExpiredTime));
            if (MapUtils.isNotEmpty(serviceOrganListMap)) {
                serviceOrganListMap.forEach((expiredTime, serviceOrganList) -> {
                    if (CollectionUtils.isNotEmpty(serviceOrganList)) {
                        int days = this.calculateDays(serviceOrganList.get(0).getExpiredTime().toLocalDate());
                        clinicAdmins.forEach(admin -> this.sendToBMessage(admin.getId(), admin.getMobile(), admin.getCountryCode(), false, serviceOrganList.size(), days + 1));
                    }
                });
            }
        });
    }

    /**
     * 创建微商城(虚拟门店 + 服务门店)过期提醒
     */
    private void sendToBMessage(String employeeId, String mobile, String countryCode, boolean isCloud, int serviceNum, int days) {
        // 只有第7天 第3天 第1天 才会发送短信提醒
        if (!Arrays.asList(1, 3, 7).contains(days)) {
            return;
        }
        SmsMessageBody smsMessageBody = new SmsMessageBody();
        smsMessageBody.setBillStatus(1);
        if (isCloud) {
            smsMessageBody.setType(SmsMessageType.NICRO_CLOUD_EXPRESS_REMIND);
            SmsMessageBody.MicroCloudNotifyData notifyData = new SmsMessageBody.MicroCloudNotifyData();
            notifyData.setDays(days);
            smsMessageBody.setData(JsonUtils.dumpAsJsonNode(notifyData));
        } else {
            smsMessageBody.setType(SmsMessageType.NICRO_SERVICE_EXPRESS_REMIND);
            SmsMessageBody.MicroServiceNotifyData notifyData = new SmsMessageBody.MicroServiceNotifyData();
            notifyData.setDays(days);
            notifyData.setServiceNum(serviceNum);
            smsMessageBody.setData(JsonUtils.dumpAsJsonNode(notifyData));
        }
        MessageDestination destination = new MessageDestination();
        destination.setDestinationId(employeeId);
        destination.setDestination(mobile);
        destination.setCountryCode(countryCode);

        ToBMessage toBMessage = new ToBMessage();
        toBMessage.setChannel(ToBMessage.MessageChannel.SMS);
        toBMessage.setMsgId(idGenerator.getUUID());
        toBMessage.setDestinations(Collections.singletonList(destination));
        toBMessage.setBody(JsonUtils.dumpAsJsonNode(smsMessageBody));
        log.info("发送微商城(虚拟门店+服务门店)的过期提醒: {}", JsonUtils.dump(toBMessage));
        producer.notifyToBMessage(toBMessage);
    }

    /**
     * 清除过期的微商城(虚拟门店 + 服务门店)
     */
    private void clearExpiredOrgan(List<MicroMartOrgan> organList) {
        // 过滤已经过期的门店数据信息
        List<MicroMartOrgan> validOrganList = organList.stream()
                .filter(organ -> LocalDate.now().isAfter(organ.getExpiredTime().toLocalDate()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(validOrganList)) {
            return;
        }
        ExecutorUtils.execute(() -> {
            // 查看是否有删除的虚拟门店数据
            Set<MicroMartOrgan> organSet = validOrganList.stream()
                    .filter(organ -> organ.getIsCloudOrgan() == YesOrNo.YES)
                    .collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(organSet)) {
                // 找出虚拟门店下所有的门店信息,虚拟门店过期则服务门店都不可用
                List<MicroMartOrgan> subOrganList = microMartOrganRepository.findByChainIdInAndIsDeleted(organSet.stream().map(MicroMartOrgan::getChainId).collect(Collectors.toSet()), 0);
                if (CollectionUtils.isNotEmpty(subOrganList)) {
                    validOrganList.addAll(subOrganList);
                }
            }
            // 更改所有数据的状态为删除状态
            validOrganList.forEach(organ -> organ.setIsDeleted(1));
            microMartOrganRepository.saveAll(validOrganList);
        });
    }

    /**
     * 获取门店管理员信息及门店的地址信息
     */
    private void getOrganEmployeeAdmin(List<Organ> organList, List<MicroMartOrgan> microMartOrganList) {
        if (CollectionUtils.isEmpty(organList) || CollectionUtils.isEmpty(microMartOrganList)) {
            return;
        }
        Map<String, List<EmployeeBasicView>> map = employeeService.findClinicAdminsByClinicIdList(organList.stream().map(Organ::getId).collect(Collectors.toList()));
        if (MapUtils.isEmpty(map)) {
            return;
        }
        Map<String, Organ> organMap = ListUtils.toMap(organList, Organ::getId);
        // 设置门店的管理员及联系方式
        microMartOrganList.forEach(mmo -> {
            String clinicId = mmo.getClinicId();
            if (organMap.containsKey(clinicId)) {
                Organ organ = organMap.getOrDefault(clinicId, new Organ());
                mmo.setAddressGeo(organ.getAddressGeo());
                mmo.setContactPhone(organ.getContactPhone());
                mmo.setBackupContactPhone(organ.getBackupContactPhone());
                mmo.setBackupContactMobile(organ.getBackupContactMobile());
            }
            if (map.containsKey(clinicId)) {
                List<EmployeeBasicView> employeeBasicViewList = map.getOrDefault(mmo.getClinicId(), Collections.emptyList());
                if (CollectionUtils.isNotEmpty(employeeBasicViewList)) {
                    EmployeeBasicView employeeBasicView = employeeBasicViewList.get(0);
                    mmo.setAdmin(employeeBasicView.getName());
                }
            }
        });
    }
}
