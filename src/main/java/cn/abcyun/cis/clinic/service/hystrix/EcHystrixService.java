package cn.abcyun.cis.clinic.service.hystrix;

import cn.abcyun.bis.rpc.sdk.cis.model.ec.EcAuthRsp;
import cn.abcyun.cis.clinic.api.view.ClinicEcAuthReq;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/12/16 17:19
 */

@Slf4j
public class EcHystrixService {
    public EcAuthRsp ecAuthFallBack(String chainId, ClinicEcAuthReq req, String employeeId) {
        log.error("服务降级 ecAuth, chainId:{}, req:{}, employeeId:{}", chainId, req, employeeId);
        return null;
    }
}
