package cn.abcyun.cis.clinic.service.hystrix;

import cn.abcyun.bis.rpc.sdk.cis.model.mc.ChainsWeClinicSimpleRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.mc.GetChainWeClinicOpenedStatusReq;
import cn.abcyun.bis.rpc.sdk.cis.model.mc.GetChainWeClinicOpenedStatusRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.shorturl.ShortUrlReq;
import cn.abcyun.bis.rpc.sdk.cis.model.shorturl.ShortUrlView;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/16 14:00
 */
@Slf4j
public class McHystrixService {
    // Mc ------------
    protected GetChainWeClinicOpenedStatusRsp getChainsWeClinicOpenedStatusFallBack(GetChainWeClinicOpenedStatusReq req) {
        log.error("服务降级：abcCisMcFeignClient.getChainsWeClinicOpenedStatus(req) fall back, req = {}", req);
        return null;
    }

    protected List<ChainsWeClinicSimpleRsp.ChainWeClinicSimple> getChainsWeClinicSimpleFallBack(List<String> chainIds) {
        log.error("服务降级：abcCisMcFeignClient.getChainsWeClinicSimple(chainIds) fall back, chainIds = {}", chainIds);
        return new ArrayList<>();
    }

    protected Integer disableMcByOrganIdFallBack(String organId) {
        log.error("服务降级：abcCisMcFeignClient.disableMcByOrganId(organId) fall back, organId = {}", organId);
        return null;
    }
}
