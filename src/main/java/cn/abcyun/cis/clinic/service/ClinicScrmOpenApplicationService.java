package cn.abcyun.cis.clinic.service;

import cn.abcyun.cis.clinic.api.view.ClinicCurrentEditionComposeView;
import cn.abcyun.cis.clinic.api.view.ClinicCurrentEditionView;
import cn.abcyun.cis.clinic.api.view.scrm.ClinicScrmOpenApplicationView;
import cn.abcyun.cis.clinic.model.*;
import cn.abcyun.cis.clinic.repository.AbcSellerOrganRelationFixedRepository;
import cn.abcyun.cis.clinic.repository.ClinicScrmOpenApplicationRepository;
import cn.abcyun.cis.clinic.repository.GrayOrganRepository;
import cn.abcyun.cis.commons.util.DateUtils;
import cn.abcyun.cis.commons.util.FillUtils;
import cn.abcyun.cis.commons.util.JsonUtils;
import cn.abcyun.cis.core.db.UseReadOnlyDB;
import cn.abcyun.cis.idgenerator.AbcIdGenerator;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.time.Instant;
import java.util.Objects;
import java.util.Optional;

/**
 * 门店开通 SCRM 申请业务层
 *
 * <AUTHOR>
 * @date 2023/1/10 1:46 PM
 **/
@Slf4j
@Service
public class ClinicScrmOpenApplicationService {

    @Autowired
    private ClinicScrmOpenApplicationRepository clinicScrmOpenApplicationRepository;

    @Autowired
    private OrganService organService;

    @Autowired
    private AbcSellerOrganRelationFixedRepository abcSellerOrganRelationFixedRepository;

    @Autowired
    private EmployeeService employeeService;

    @Autowired
    private ClinicEditionService clinicEditionService;

    @Autowired
    private GrayOrganRepository grayOrganRepository;

    @Value("${abc.scrm.open-application.notify.webhook}")
    private String openScrmApplicationWebhook;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private AbcIdGenerator abcIdGenerator;

    /**
     * 创建 SCRM 开通申请
     */
    @Transactional(rollbackFor = Exception.class)
    public ClinicScrmOpenApplicationView createScrmOpenApplication(String chainId, String clinicId, String applicantId) {
        // 当前员工已经申请过了，则直接返回
        Optional<ClinicScrmOpenApplication> clinicScrmOpenApplicationOpt = clinicScrmOpenApplicationRepository.findFirstByClinicIdAndApplicantId(clinicId, applicantId);
        if (clinicScrmOpenApplicationOpt.isPresent()) {
            return toClinicScrmOpenApplicationView(clinicScrmOpenApplicationOpt.get());
        }

        // 没有申请过则创建一个新的申请
        ClinicScrmOpenApplication clinicScrmOpenApplication = new ClinicScrmOpenApplication();
        clinicScrmOpenApplication.setId(abcIdGenerator.getUIDLong());
        clinicScrmOpenApplication.setChainId(chainId);
        clinicScrmOpenApplication.setClinicId(clinicId);
        clinicScrmOpenApplication.setApplicantId(applicantId);
        clinicScrmOpenApplication.setApplicationTime(Instant.now());
        FillUtils.fillCreatedBy(clinicScrmOpenApplication, applicantId);
        clinicScrmOpenApplicationRepository.save(clinicScrmOpenApplication);

        // 构建返回数据
        ClinicScrmOpenApplicationView clinicScrmOpenApplicationView = toClinicScrmOpenApplicationView(clinicScrmOpenApplication);

        // 发送通知
        String applicationTimeStr = DateUtils.formatLocalDateTime(DateUtils.toLocalDateTime(clinicScrmOpenApplicationView.getApplicationTime()),
                DateUtils.sFormatterDateTime);
        String content = "企微管家开通申请\n" +
                "> 请求环境: <font color=\"comment\">" + clinicScrmOpenApplicationView.getEnv() + "</font>\n" +
                "> 申请诊所: <font color=\"comment\">" + clinicScrmOpenApplicationView.getClinicName() + "</font>\n" +
                "> 产品类型: <font color=\"comment\">" + clinicScrmOpenApplicationView.getClinicHisTypeName() + "</font>\n" +
                "> 产品版本: <font color=\"comment\">" + clinicScrmOpenApplicationView.getClinicEditionName() + "</font>\n" +
                "> 申请人: <font color=\"comment\">" + clinicScrmOpenApplicationView.getApplicantName() + "</font>\n" +
                "> 申请人手机号: <font color=\"comment\">" + clinicScrmOpenApplicationView.getApplicantMobile() + "</font>\n" +
                "> 申请时间: <font color=\"comment\">" + applicationTimeStr + "</font>\n" +
                "> 负责销售: <font color=\"comment\">" + clinicScrmOpenApplicationView.getSellerName() + "</font>";
        QwMarkdownMessage message = new QwMarkdownMessage(new QwMarkdownMessageBody(content));
        log.info("send open scrm application req:{}", JsonUtils.dump(message));
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<QwMarkdownMessage> request = new HttpEntity<>(message, httpHeaders);
        ResponseEntity<String> sendMessageRsp = restTemplate.postForEntity(openScrmApplicationWebhook, request, String.class);
        log.info("send open scrm application rspBody:{}, rspStatus:{}", sendMessageRsp.getBody(), sendMessageRsp.getStatusCodeValue());

        return clinicScrmOpenApplicationView;
    }

    private ClinicScrmOpenApplicationView toClinicScrmOpenApplicationView(ClinicScrmOpenApplication clinicScrmOpenApplication) {
        if (clinicScrmOpenApplication == null) {
            return null;
        }

        String chainId = clinicScrmOpenApplication.getChainId();
        Organ chain = organService.getOrganById(chainId);
        String clinicId = clinicScrmOpenApplication.getClinicId();
        Optional<Organ> clinicOpt = Optional.ofNullable(Objects.equals(chainId, clinicId) ? chain : organService.getOrganById(clinicId));
        ClinicScrmOpenApplicationView clinicScrmOpenApplicationView = new ClinicScrmOpenApplicationView();
        clinicScrmOpenApplicationView.setId(clinicScrmOpenApplication.getId());
        Optional<GrayOrgan> grayOrganOpt = grayOrganRepository.findByChainId(chainId);
        clinicScrmOpenApplicationView.setEnv(grayOrganOpt.map(GrayOrgan::getEnvName).orElse("prod"));
        clinicScrmOpenApplicationView.setChainId(chainId);
        clinicScrmOpenApplicationView.setChainName(Optional.ofNullable(chain).map(Organ::getName).orElse(""));

        // 门店信息
        clinicOpt.ifPresent(clinic -> {
            clinicScrmOpenApplicationView.setClinicId(clinicId);
            clinicScrmOpenApplicationView.setClinicName(clinic.getName());
            clinicScrmOpenApplicationView.setClinicHisType(clinic.getHisType());
            clinicScrmOpenApplicationView.setClinicHisTypeName(Constants.HisType.toHisTypeName(clinic.getHisType()));
            Optional<ClinicCurrentEditionView> clinicCurrentEditionViewOpt = Optional.ofNullable(clinicEditionService.getClinicCurrentEditionComposeView(clinic))
                    .map(ClinicCurrentEditionComposeView::getEdition);
            clinicCurrentEditionViewOpt.ifPresent(clinicCurrentEditionView -> {
                clinicScrmOpenApplicationView.setClinicEdition(clinicCurrentEditionView.getId());
                clinicScrmOpenApplicationView.setClinicEditionName(clinicCurrentEditionView.getName());
            });
        });

        // 申请人信息
        clinicScrmOpenApplicationView.setApplicationTime(clinicScrmOpenApplication.getApplicationTime());
        String applicantId = clinicScrmOpenApplication.getApplicantId();
        Optional<Employee> employeeOpt = Optional.ofNullable(employeeService.findEmployeeById(applicantId, chainId));
        employeeOpt.ifPresent(employee -> {
            clinicScrmOpenApplicationView.setApplicantId(applicantId);
            clinicScrmOpenApplicationView.setApplicantName(employee.getName());
            clinicScrmOpenApplicationView.setApplicantMobile(employee.getMobile());
        });
        if (!StringUtils.hasText(clinicScrmOpenApplicationView.getApplicantName())) {
            // 没有关联的员工，则用 "--" 来代替
            clinicScrmOpenApplicationView.setApplicantName("--");
        }

        // 销售信息
        Optional<AbcSellerOrganRelationFixed> sellerOrganRelationOpt = abcSellerOrganRelationFixedRepository.findByClinicId(clinicId);
        sellerOrganRelationOpt.ifPresent(sellerOrganRelation -> {
            clinicScrmOpenApplicationView.setSellerId(sellerOrganRelation.getSellerId());
            clinicScrmOpenApplicationView.setSellerName(sellerOrganRelation.getSellerName());
        });
        if (!StringUtils.hasText(clinicScrmOpenApplicationView.getSellerName())) {
            // 没有关联的销售，则用 "--" 来代替
            clinicScrmOpenApplicationView.setSellerName("--");
        }

        return clinicScrmOpenApplicationView;
    }

    /**
     * 查询员工开通 SCRM 申请
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public ClinicScrmOpenApplicationView getScrmOpenApplication(String chainId, String clinicId, String applicantId) {
        Optional<ClinicScrmOpenApplication> clinicScrmOpenApplicationOpt = clinicScrmOpenApplicationRepository.findFirstByClinicIdAndApplicantId(clinicId, applicantId);
        return toClinicScrmOpenApplicationView(clinicScrmOpenApplicationOpt.orElse(null));
    }

    @Data
    private static class QwMarkdownMessage {

        private String msgtype = "markdown";

        private QwMarkdownMessageBody markdown;

        public QwMarkdownMessage(QwMarkdownMessageBody markdown) {
            this.markdown = markdown;
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    private static class QwMarkdownMessageBody {

        private String content;

    }
}
