package cn.abcyun.cis.clinic.service;

import cn.abcyun.cis.clinic.api.view.EmployeeBusAuthReq;
import cn.abcyun.cis.clinic.api.view.EmployeeBusAuthRsp;
import cn.abcyun.cis.clinic.api.view.EmployeeBusGrantReq;
import cn.abcyun.cis.clinic.api.view.EmployeeView;
import cn.abcyun.cis.clinic.exception.ClinicError;
import cn.abcyun.cis.clinic.exception.ClinicException;
import cn.abcyun.cis.clinic.model.ClinicEmployee;
import cn.abcyun.cis.clinic.model.Employee;
import cn.abcyun.cis.clinic.repository.ClinicEmployeeRepository;
import cn.abcyun.cis.clinic.repository.EmployeeRepository;
import cn.abcyun.cis.commons.util.Sha256Utils;
import cn.abcyun.cis.commons.util.UUIDUtils;
import cn.abcyun.cis.core.util.Validators;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.helpers.MessageFormatter;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2025-02-18 19:38
 */
@Service
@RequiredArgsConstructor
public abstract class EmployeeBusinessAuthService {

    private static final String ACCESS_TOKEN_PREFIX = "employee_bus_access_token";

    protected final EmployeeRepository employeeRepository;

    protected final ClinicEmployeeRepository clinicEmployeeRepository;

    protected final EmployeeService employeeService;

    protected final RedisTemplate<String, String> stringRedisTemplate;

    public abstract String getGrantType();

    /**
     * 校验
     *
     * @param authReq auth req
     */
    public void validate(EmployeeBusAuthReq authReq) {
        Validators.validateEntity(authReq);
    }

    /**
     * 校验员工是否在这个门店
     *
     * @param clinicId 诊所 ID
     * @param authReq  auth req
     */
    public Pair<Employee, ClinicEmployee> validateClinicEmployee(String clinicId, EmployeeBusAuthReq authReq) {
        ClinicEmployee clinicEmployee = clinicEmployeeRepository.findFirstByClinicIdAndEmployeeIdAndStatus(clinicId, authReq.getEmployeeId(), ClinicEmployee.Status.NORMAL).orElse(null);
        if (Objects.isNull(clinicEmployee)) {
            throw new ClinicException(ClinicError.PARAM_ERROR, "员工不在该门店");
        }
        Employee employee = employeeRepository.findById(authReq.getEmployeeId()).orElse(null);
        if (Objects.isNull(employee)) {
            throw new ClinicException(ClinicError.PARAM_ERROR, "员工不存在");
        }
        return Pair.of(employee, clinicEmployee);
    }

    protected String getAccessToken() {
        return UUIDUtils.randomUUID();
    }

    protected String generateAccessToken(String clinicId, String businessId, String businessType, String employeeId) {
        String accessToken = getAccessTokenKey(clinicId, businessId, businessType);
        stringRedisTemplate.opsForValue().set(accessToken, employeeId, 5, TimeUnit.MINUTES);
        return accessToken;
    }

    /**
     * 认证
     *
     * @param chainId    连锁id
     * @param clinicId   诊所 ID
     * @param operatorId 操作人ID
     * @param authReq    auth req
     * @return {@link EmployeeView }
     */
    public abstract EmployeeBusAuthRsp authentication(String chainId, String clinicId, String operatorId, EmployeeBusAuthReq authReq);

    /**
     * 授权
     *
     * @param chainId    连锁id
     * @param clinicId   诊所 ID
     * @param operatorId 操作人ID
     * @param grantReq    授权请求
     * @return {@link EmployeeView }
     */
    public EmployeeView authorization(String chainId, String clinicId, String operatorId, EmployeeBusGrantReq grantReq) {
        Validators.validateEntity(grantReq);
        String employeeId = stringRedisTemplate.opsForValue().get(grantReq.getAccessToken());
        if (StringUtils.isBlank(employeeId)) {
            throw new ClinicException(ClinicError.EMPLOYEE_BUS_AUTH_CODE_EXPIRED, "授权码已过期");
        }
        Employee employee = employeeRepository.findById(employeeId).orElse(null);
        if (employee == null) {
            throw new ClinicException(ClinicError.PARAM_ERROR, "员工不存在，授权失败");
        }
        // 删除授权码
        // 这里不删除token,自动过期删除。退费有多种入账方式，一次可以退多次，删除后无法再校验过
        //stringRedisTemplate.delete(grantReq.getAccessToken());
        return EmployeeView.from(employee);
    }

    /**
     * 获取授权码Key
     *
     * @param businessId 企业 ID
     * @param businessType 业务场景
     * @return {@link String }
     */
    private static String getAccessTokenKey(String clinicId, String businessId, String businessType) {
        return Sha256Utils.getSHA256(MessageFormatter.arrayFormat("{}:{}:{}:{}:{}", new Object[]{ACCESS_TOKEN_PREFIX, clinicId, businessId, businessType, UUIDUtils.randomUUID()}).getMessage());
    }
}
