package cn.abcyun.cis.clinic.service;

import cn.abcyun.bis.rpc.sdk.cis.message.gateway.GatewayUpdateConfigMessage;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.CreateZoneTaskReq;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.GetRegionEnvZoneCountRsp;
import cn.abcyun.bis.rpc.sdk.oa.model.ClientDeviceBatchMessageReq;
import cn.abcyun.cis.clinic.amqp.MQProducer;
import cn.abcyun.cis.clinic.api.view.EnvZoneChangedView;
import cn.abcyun.cis.clinic.api.view.GrayOrganView;
import cn.abcyun.cis.clinic.api.view.UpdateGrayOrganReq;
import cn.abcyun.cis.clinic.client.OaManagementClient;
import cn.abcyun.cis.clinic.model.ClinicRegionOrgan;
import cn.abcyun.cis.clinic.model.ClinicZoneTask;
import cn.abcyun.cis.clinic.model.ClinicZoneTaskDetail;
import cn.abcyun.cis.clinic.model.GrayOrgan;
import cn.abcyun.cis.clinic.mybatis.mapper.OrganMapper;
import cn.abcyun.cis.clinic.repository.ClinicZoneTaskDetailRepository;
import cn.abcyun.cis.clinic.repository.ClinicZoneTaskRepository;
import cn.abcyun.cis.clinic.repository.GrayOrganRepository;
import cn.abcyun.cis.clinic.utils.YesOrNo;
import cn.abcyun.cis.commons.exception.CisCustomException;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.util.JsonUtils;
import cn.abcyun.cis.commons.util.ListUtils;
import cn.abcyun.cis.commons.util.MathUtils;
import cn.abcyun.cis.core.db.UseReadOnlyDB;
import cn.abcyun.cis.core.redis.RedisLock;
import cn.abcyun.cis.idgenerator.AbcIdGenerator;
import cn.abcyun.common.model.AbcListPage;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
@EnableAspectJAutoProxy(exposeProxy = true)
public class HighlyAvaliableService {
    @Autowired
    private MQProducer mqProducer;
    @Autowired
    private OrganService organService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private ClinicZoneTaskDetailRepository clinicZoneTaskDetailRepository;
    @Autowired
    private ClinicZoneTaskRepository clinicZoneTaskRepository;
    @Autowired
    private AbcIdGenerator abcIdGenerator;
    @Autowired
    private OrganMapper organMapper;

    @Value("${abc.region-id}")
    private Integer regionId;

    public static final class RedisChannel {
        public static final String GRAY_ORGAN_REFRESH = "GRAY_ORGAN_REFRESH";
    }

    @Autowired
    private GrayOrganRepository grayOrganRepository;
    @Autowired
    private ClinicRegionService clinicRegionService;
    @Autowired
    private OaManagementClient oaManagementClient;

    @Transactional(rollbackFor = Exception.class)
    @RedisLock(key = "sc-clinic-update-gray-organ", waitTime = 10)
    public void updateGrayOrgan(UpdateGrayOrganReq req) {

        //查询已存在的grayOrgan记录
        List<GrayOrgan> grayOrgans = grayOrganRepository.findByChainIdIn(req.getChainIds());

        List<String> existedChainIds = grayOrgans.stream().map(GrayOrgan::getChainId).collect(Collectors.toList());

        for (GrayOrgan organ : grayOrgans) {
            if (organ.getEnv() == req.getEnv() && Objects.equals(organ.getZone(), req.getZone())) {
                continue;
            }

            organ.setEnv(req.getEnv());
            organ.setZone(req.getZone());
            organ.setLastModified(Instant.now());

        }

        List<String> newChainIds = req.getChainIds().stream()
                .filter(chainId -> !existedChainIds.contains(chainId))
                .collect(Collectors.toList());

        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(newChainIds)) {

            //查询regionId
            Map<String, ClinicRegionOrgan> chainIdRegionOrganMap = ListUtils.toMap(clinicRegionService.findClinicRegionOrganByChainIds(newChainIds), ClinicRegionOrgan::getChainId);

            grayOrgans.addAll(req.getChainIds().stream()
                    .filter(chainId -> !existedChainIds.contains(chainId))
                    .map(chainId -> {

                        ClinicRegionOrgan clinicRegionOrgan = chainIdRegionOrganMap.get(chainId);
                        if (Objects.isNull(clinicRegionOrgan)) {
                            log.error("clinicRegionOrgan is null, chainId: {}", chainId);
                            throw new NotFoundException("clinicRegionOrgan is null, chainId:" + chainId);
                        }

                        GrayOrgan grayOrgan = new GrayOrgan();
                        grayOrgan.setChainId(chainId);
                        grayOrgan.setEnv(req.getEnv());
                        grayOrgan.setZone(req.getZone());
                        grayOrgan.setRegionId(Integer.parseInt(clinicRegionOrgan.getRegionId()));
                        grayOrgan.setCreated(Instant.now());
                        grayOrgan.setLastModified(Instant.now());
                        return grayOrgan;
                    })
                    .collect(Collectors.toList())
            );
        }

        grayOrganRepository.saveAll(grayOrgans);
        List<GrayOrganView> grayOrganViews = organService.generateGrayOrganViews(grayOrgans);

        sendRocketMqMessage(req.getNotifyGatewayChannel(), grayOrganViews);

        if (req.getNotifyToPc() == 1) {
            try {
                sendOrganEnvUpdateMessage(grayOrganViews);
            } catch (Exception e) {
                log.error("sendOrganEnvUpdateMessage error.", e);
            }

        }
    }

    public void sendRocketMqMessage(int channel, List<GrayOrganView> grayOrganViews) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(grayOrganViews)) {
            return;
        }

        if (channel == UpdateGrayOrganReq.NotifyGatewayChannel.ROCKET_MQ) {
            GatewayUpdateConfigMessage message = new GatewayUpdateConfigMessage();
            message.setType(GatewayUpdateConfigMessage.Type.UPDATE_GRAY_ORGAN);
            message.setGrayOrgans(grayOrganViews.stream()
                    .map(grayOrganView -> {
                        cn.abcyun.bis.rpc.sdk.cis.model.clinic.GrayOrganView sdkGrayOrganView = new cn.abcyun.bis.rpc.sdk.cis.model.clinic.GrayOrganView();
                        BeanUtils.copyProperties(grayOrganView, sdkGrayOrganView);
                        return sdkGrayOrganView;
                    })
                    .collect(Collectors.toList())
            );

            mqProducer.sendGatewayUpdateConfigMessage(message);
        } else if (channel == UpdateGrayOrganReq.NotifyGatewayChannel.REDIS) {
            stringRedisTemplate.convertAndSend(RedisChannel.GRAY_ORGAN_REFRESH, "");
        }
    }

    public void sendOrganEnvUpdateMessage(List<GrayOrganView> grayOrganViews) {

        if (org.apache.commons.collections.CollectionUtils.isEmpty(grayOrganViews)) {
            return;
        }

        for (List<GrayOrganView> organViews : Lists.partition(grayOrganViews, 500)) {
            List<String> chainIds = organViews.stream()
                    .map(GrayOrganView::getChainId)
                    .collect(Collectors.toList());

            //查询clinicIds
            List<String> clinicIds = organMapper.listClinicIds(chainIds);

            if (org.apache.commons.collections.CollectionUtils.isEmpty(clinicIds)) {
                return;
            }

            EnvZoneChangedView envZoneChangedView = new EnvZoneChangedView();
            envZoneChangedView.setTimestamp(Instant.now());

            List<List<String>> partitionClinicIds = Lists.partition(clinicIds, 500);

            for (List<String> partitionClinicId : partitionClinicIds) {
                ClientDeviceBatchMessageReq req = new ClientDeviceBatchMessageReq();
                req.setClinicIds(partitionClinicId);
                req.setIdentities(new ArrayList<String>() {{
                    add("1");
                }});
                req.setEvent("env_zone_change");

                ClientDeviceBatchMessageReq.AssistMessage assistMessage = new ClientDeviceBatchMessageReq.AssistMessage();
                assistMessage.setType("env_zone_change");
                assistMessage.setContent(JsonUtils.dump(envZoneChangedView));
                req.setMessage(assistMessage);
                oaManagementClient.sendDeviceMessageBatch(req);
            }
        }
    }


    public void refreshGrayOrganCore(boolean notifyToPC, int notifyGatewayChannel) {
        List<GrayOrganView> grayOrganList = new ArrayList<>();
        int offset = 0;
        while (true) {
            try {
                AbcListPage<GrayOrganView> pageList = organService.getGrayOrganList(offset, 1000, regionId);
                List<GrayOrganView> grayOrganViews = pageList.getRows();

                grayOrganList.addAll(grayOrganViews);
                //发送rocketMq消息通知网关更新
                try {
                    sendRocketMqMessage(notifyGatewayChannel, grayOrganViews);
                } catch (Exception e) {
                    log.error("sendRocketMqMessage error.");
                }

                offset += grayOrganViews.size();
                if (offset >= pageList.getTotal()) {
                    break;
                }
            } catch (Exception e) {
                break;
            }
        }


        if (notifyToPC) {
            sendOrganEnvUpdateMessage(grayOrganList);
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public CreateZoneTaskReq createZoneTask(CreateZoneTaskReq req) {
        req.parameterCheck();
        List<ClinicZoneTask> existTaskList = clinicZoneTaskRepository.findAllByStatusInAndToZone(Arrays.asList(
                ClinicZoneTask.Status.NEW,
                ClinicZoneTask.Status.EFFECTIVE
        ), req.getToZone());
        if (!CollectionUtils.isEmpty(existTaskList)) {
            log.error("可用分区放量:已经存在任务了 ,req:{},existTask:{}", req, existTaskList);
            throw new CisCustomException(400, "可用分区放量任务已经存在:" + req.getName());
        }
        int totalGrayZoneCount = organMapper.countEnvRegionCount(req.getRegionId(), 1, req.getFromZone()); // 灰度
        int totalProdZoneCount = organMapper.countEnvRegionCount(req.getRegionId(), 0, req.getFromZone()); // 正式


        Instant now = Instant.now();
        ClinicZoneTask grayZoneTask = new ClinicZoneTask();
        grayZoneTask.setId(abcIdGenerator.getUIDLong());
        grayZoneTask.setName(req.getName() + "(灰度)");
        grayZoneTask.setRegionId(-1);
        grayZoneTask.setEnv(1);
        grayZoneTask.setFromZone(req.getFromZone());
        grayZoneTask.setToZone(req.getToZone());
        grayZoneTask.setStatus(ClinicZoneTask.Status.EFFECTIVE);
        grayZoneTask.setCreated(now);
        grayZoneTask.setCreatedBy(req.getCreatedBy());
        clinicZoneTaskRepository.save(grayZoneTask);

        List<ClinicZoneTaskDetail> detailEntities = new ArrayList<>();
        for (int i = 0; i < req.getList().size(); i++) {
            CreateZoneTaskReq.ZoneTaskDetail detailReq = req.getList().get(i);
            int realGrayCount = MathUtils.wrapBigDecimalMultiply(BigDecimal.valueOf(totalGrayZoneCount), detailReq.getRegionCount().divide(BigDecimal.valueOf(100), 2, RoundingMode.DOWN)).intValue();
            ClinicZoneTaskDetail detail = new ClinicZoneTaskDetail();
            detail.setId(abcIdGenerator.getUIDLong());
            detail.setZoneTaskId(grayZoneTask.getId());
            int realGrayUpdateCount = 0;
            if (i == 0) {
                realGrayUpdateCount = organMapper.updateEnvRegionZoneLimitCount(req.getRegionId(),
                        1,
                        req.getList().size() == 1 ? YesOrNo.NO : YesOrNo.YES,
                        detail.getZoneTaskId(),
                        detail.getId(),
                        req.getFromZone(),
                        req.getToZone(),
                        realGrayCount);

            } else if (i != req.getList().size() - 1) {
                realGrayUpdateCount = organMapper.updateEnvRegionZoneLimitCount(req.getRegionId(),
                        1,
                        YesOrNo.NO,
                        detail.getZoneTaskId(),
                        detail.getId(),
                        req.getFromZone(),
                        req.getToZone(),
                        realGrayCount);
            } else {
                realGrayUpdateCount = organMapper.updateEnvRegionZoneLimitCount(req.getRegionId(),
                        1,
                        YesOrNo.NO,
                        detail.getZoneTaskId(),
                        detail.getId(),
                        req.getFromZone(),
                        req.getToZone(),
                        null);
            }
            detail.setName(detailReq.getName());
            detail.setRegionCount(detailReq.getRegionCount());
            detail.setRealRegionCount(realGrayUpdateCount);
            detail.setStatus(ClinicZoneTaskDetail.Status.EFFECTIVE);
            detail.setCountType(detailReq.getCountType());
            detail.setStarted(detailReq.getStarted());
            detail.setFinished(detailReq.getFinished());
            Instant detailCreated = detailReq.getCreated() != null ? detailReq.getCreated() : now;
            detail.setCreated(detailCreated);
            detail.setRealRegionCount(realGrayUpdateCount);
            detailEntities.add(detail);
        }

        ClinicZoneTask prodZoneTask = new ClinicZoneTask();
        prodZoneTask.setId(abcIdGenerator.getUIDLong());
        prodZoneTask.setName(req.getName() + "(正式)");
        prodZoneTask.setRegionId(-1);
        prodZoneTask.setEnv(0);
        prodZoneTask.setFromZone(req.getFromZone());
        prodZoneTask.setToZone(req.getToZone());
        prodZoneTask.setStatus(ClinicZoneTask.Status.EFFECTIVE);
        prodZoneTask.setCreated(now);
        prodZoneTask.setCreatedBy(req.getCreatedBy());
        for (int i = 0; i < req.getList().size(); i++) {
            CreateZoneTaskReq.ZoneTaskDetail detailReq = req.getList().get(i);
            int realProdCount = MathUtils.wrapBigDecimalMultiply(BigDecimal.valueOf(totalProdZoneCount), detailReq.getRegionCount().divide(BigDecimal.valueOf(100), 2, RoundingMode.DOWN)).intValue();
            ClinicZoneTaskDetail detail = new ClinicZoneTaskDetail();
            detail.setId(abcIdGenerator.getUIDLong());
            detail.setZoneTaskId(prodZoneTask.getId());
            int realProdUpdateCount = 0;
            if (i == 0) {
                realProdUpdateCount = organMapper.updateEnvRegionZoneLimitCount(req.getRegionId(),
                        0,
                        req.getList().size() == 1 ? YesOrNo.NO : YesOrNo.YES,
                        detail.getZoneTaskId(),
                        detail.getId(),
                        req.getFromZone(),
                        req.getToZone(),
                        realProdCount);

            } else if (i != req.getList().size() - 1) {
                realProdUpdateCount = organMapper.updateEnvRegionZoneLimitCount(req.getRegionId(),
                        0,
                        YesOrNo.NO,
                        detail.getZoneTaskId(),
                        detail.getId(),
                        req.getFromZone(),
                        req.getToZone(),
                        realProdCount);
            } else {
                realProdUpdateCount = organMapper.updateEnvRegionZoneLimitCount(req.getRegionId(),
                        0,
                        YesOrNo.NO,
                        detail.getZoneTaskId(),
                        detail.getId(),
                        req.getFromZone(),
                        req.getToZone(),
                        null);
            }
            detail.setName(detailReq.getName() + "(正式)");
            detail.setRegionCount(detailReq.getRegionCount());
            detail.setRealRegionCount(realProdUpdateCount);
            detail.setStatus(ClinicZoneTaskDetail.Status.EFFECTIVE);
            detail.setCountType(detailReq.getCountType());
            detail.setStarted(detailReq.getStarted());
            detail.setFinished(detailReq.getFinished());
            Instant detailCreated = detailReq.getCreated() != null ? detailReq.getCreated() : now;
            detail.setCreated(detailCreated);

            detailEntities.add(detail);
        }

        clinicZoneTaskDetailRepository.saveAll(detailEntities);

        return req;
    }

    @Transactional(rollbackFor = Exception.class)
    public CreateZoneTaskReq updateZoneTaskStatus(Long taskId, int newStatus) {

        if (newStatus != ClinicZoneTask.Status.STOP_ROLL_BACK
                && newStatus != ClinicZoneTask.Status.STOP) {
            throw new CisCustomException(400, "任务状态非法");
        }

        ClinicZoneTask zoneTask = clinicZoneTaskRepository.findById(taskId)
                .orElseThrow(() -> new CisCustomException(400, "可用分区放量任务不存在"));
        if (zoneTask.getStatus() == ClinicZoneTask.Status.FINISHED
                || zoneTask.getStatus() == ClinicZoneTask.Status.STOP
                || zoneTask.getStatus() == ClinicZoneTask.Status.STOP_ROLL_BACK
        ) {
            throw new CisCustomException(400, "任务已经完成");
        }

        //回滚
        if (newStatus == ClinicZoneTask.Status.STOP_ROLL_BACK) {
            List<String> chainIds = organMapper.getRollBackChainIdList(zoneTask.getId(), zoneTask.getToZone(), zoneTask.getEnv());
            if (!CollectionUtils.isEmpty(chainIds)) {
                List<GrayOrganView> needNotify = new ArrayList<>();
                for (List<String> partition : Lists.partition(chainIds, 500)) {
                    int affected = organMapper.rollBackZoneTaskIdByChainIds(zoneTask.getRegionId(),
                            zoneTask.getId(),
                            zoneTask.getEnv(),
                            zoneTask.getFromZone(),
                            zoneTask.getToZone(),
                            partition);

                    if (affected <= 0) {
                        log.info("rollback skipped, partition size:{}, affected:{}", partition.size(), affected);
                        continue;
                    }

                    log.info("rollback partition success, size:{}, affected:{}", partition.size(), affected);

                    List<GrayOrgan> grayOrgans = grayOrganRepository.findByChainIdIn(partition);
                    List<GrayOrgan> targetGrayOrgans = grayOrgans.stream()
                            .filter(grayOrgan -> grayOrgan.getEnv() == zoneTask.getEnv()
                                    && Objects.equals(grayOrgan.getZone(), zoneTask.getFromZone()))
                            .collect(Collectors.toList());

                    if (CollectionUtils.isEmpty(targetGrayOrgans)) {
                        continue;
                    }

                    needNotify.addAll(organService.generateGrayOrganViews(targetGrayOrgans));
                }

                if (!CollectionUtils.isEmpty(needNotify)) {
                    Map<String, GrayOrganView> uniqueNotify = needNotify.stream()
                            .collect(Collectors.toMap(GrayOrganView::getChainId,
                                    Function.identity(),
                                    (first, second) -> second,
                                    LinkedHashMap::new));

                    List<GrayOrganView> notifyList = new ArrayList<>(uniqueNotify.values());

                    for (List<GrayOrganView> partition : Lists.partition(notifyList, 500)) {
                        sendRocketMqMessage(UpdateGrayOrganReq.NotifyGatewayChannel.ROCKET_MQ, partition);
                    }

                    try {
                        sendOrganEnvUpdateMessage(notifyList);
                    } catch (Exception e) {
                        log.error("sendOrganEnvUpdateMessage error during rollback.", e);
                    }
                }
            }
        }
        zoneTask.setStatus(newStatus);
        zoneTask.setLastModified(Instant.now());
        clinicZoneTaskRepository.save(zoneTask);
        log.info("updateZoneTaskStatus: {}", JsonUtils.dump(zoneTask));
        return buildZoneTaskView(zoneTask);
    }

    @UseReadOnlyDB
    public AbcListPage<CreateZoneTaskReq> listZoneTasks() {
        List<ClinicZoneTask> tasks = clinicZoneTaskRepository.findAll(Sort.by(Sort.Direction.DESC, "created"));
        Stream<ClinicZoneTask> stream = tasks.stream();
        AbcListPage<CreateZoneTaskReq> rsp = new AbcListPage<>();
        List<ClinicZoneTask> filteredTasks = stream.collect(Collectors.toList());
        if (filteredTasks.isEmpty()) {
            return rsp;
        }
        rsp.setRows(filteredTasks.stream()
                .map(this::buildZoneTaskView)
                .collect(Collectors.toList()));
        return rsp;
    }

    private CreateZoneTaskReq buildZoneTaskView(ClinicZoneTask zoneTask) {
        CreateZoneTaskReq resp = new CreateZoneTaskReq();
        resp.setId(zoneTask.getId());
        resp.setName(zoneTask.getName());
        resp.setRegionId(zoneTask.getRegionId());
        resp.setEnv(zoneTask.getEnv());
        resp.setFromZone(zoneTask.getFromZone());
        resp.setToZone(zoneTask.getToZone());
        resp.setStatus(zoneTask.getStatus());
        resp.setFinished(zoneTask.getFinished());
        resp.setCreated(zoneTask.getCreated());
        return resp;
    }

    private CreateZoneTaskReq.ZoneTaskDetail buildZoneTaskDetailView(ClinicZoneTaskDetail detail) {
        CreateZoneTaskReq.ZoneTaskDetail detailView = new CreateZoneTaskReq.ZoneTaskDetail();
        detailView.setId(detail.getId());
        detailView.setName(detail.getName());
        detailView.setRegionCount(detail.getRegionCount());
        detailView.setRealRegionCount(detail.getRealRegionCount());
        detailView.setStatus(detail.getStatus());
        detailView.setCountType(detail.getCountType());

        detailView.setStarted(detail.getStarted());
        detailView.setFinished(detail.getFinished());
        detailView.setCreated(detail.getCreated());

        return detailView;
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public AbcListPage<GetRegionEnvZoneCountRsp> getRegionChainCount() {
        List<GetRegionEnvZoneCountRsp> list = organMapper.getRegionChainCount();
        AbcListPage<GetRegionEnvZoneCountRsp> rsp = new AbcListPage<>();
        rsp.setRows(list);
        return rsp;
    }

    @Transactional(rollbackFor = Exception.class)
    public CreateZoneTaskReq getZoneTask(Long taskId) {
        ClinicZoneTask clinicZoneTask = clinicZoneTaskRepository.findById(taskId).orElse(null);
        if (clinicZoneTask == null) {
            throw new CisCustomException(400, "可用分区放量任务不存在");
        }
        List<ClinicZoneTaskDetail> detailList = clinicZoneTaskDetailRepository.findAllByZoneTaskId(taskId);
        if (CollectionUtils.isEmpty(detailList)) {
            throw new CisCustomException(400, "可用分区放量任务不存在");
        }
        CreateZoneTaskReq view = buildZoneTaskView(clinicZoneTask);
        view.setList(detailList.stream().map(this::buildZoneTaskDetailView).collect(Collectors.toList()));
        return view;
    }
}
