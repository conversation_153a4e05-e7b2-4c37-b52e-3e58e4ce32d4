package cn.abcyun.cis.clinic.service.hystrix;

import cn.abcyun.bis.rpc.sdk.cis.model.mall.goods.CreateGoodsReduceRuleReq;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/12/16 17:24
 */
@Slf4j
public class MallGoodsHystrixService {
    public boolean createGoodsReduceRuleFallBack(CreateGoodsReduceRuleReq req) {
        log.error("服务降级 createGoodsReduceRuleFallBack, req:{}", req);
        return false;
    }
}
