package cn.abcyun.cis.clinic.service;

import cn.abcyun.cis.clinic.api.view.map.QuerySuggestionRsp;
import cn.abcyun.cis.clinic.client.LbsQQMapClient;
import cn.abcyun.cis.clinic.service.hystrix.LbsQQMapHystrixService;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @date 2024/9/3 18:42
 */
@Service
public class LbsQQMapService extends LbsQQMapHystrixService {
    @Autowired
    private LbsQQMapClient lbsQQMapClient;
    @Value("${lbs.key}")
    private String key;

    @HystrixCommand(fallbackMethod = "querySuggestionFallBack")
    public QuerySuggestionRsp querySuggestion(String keyword, String region, int pageIndex, int pageSize, int regionFix) {
        QuerySuggestionRsp rsp = lbsQQMapClient.querySuggestion(keyword, region, pageIndex, pageSize, key, regionFix);
        if (rsp != null && !CollectionUtils.isEmpty(rsp.getData())) {
            rsp.getData().forEach(data -> {
                if (data.getAddress() != null) {
                    String address = data.getAddress();
                    // 过滤掉省份信息
                    if (data.getProvince() != null && address.startsWith(data.getProvince())) {
                        address = address.substring(data.getProvince().length());
                    }
                    // 过滤掉城市信息
                    if (data.getCity() != null && address.startsWith(data.getCity())) {
                        address = address.substring(data.getCity().length());
                    }
                    // 过滤掉区县信息
                    if (data.getDistrict() != null && address.startsWith(data.getDistrict())) {
                        address = address.substring(data.getDistrict().length());
                    }
                    // 去掉可能存在的前导空格或特殊字符
                    address = address.replaceFirst("^[\\s,，]*", "");
                    data.setAddress(address);
                }
            });
        } else if (regionFix == 1) {
            // 如果按城市没有查询到结果，且regionFix为1，则按全国范围查询一次
            return querySuggestion(keyword, "", pageIndex, pageSize, 0);
        }
        return rsp;
    }
}
