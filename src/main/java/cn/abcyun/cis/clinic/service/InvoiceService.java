package cn.abcyun.cis.clinic.service;

import cn.abcyun.bis.rpc.sdk.cis.message.clinic.ClinicCurrentPurchaseCreatedMessage;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.OpsCommonRsp;
import cn.abcyun.cis.clinic.service.hystrix.InvoiceHystrixService;
import cn.abcyun.cis.core.util.FeignClientRpcTemplate;
import cn.abcyun.region.rpc.sdk.client.cis.AbcCisInvoiceRegionFeignClient;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/2/14 上午11:21
 * @description TODO
 */
@Slf4j
@Service
public class InvoiceService extends InvoiceHystrixService {

    @Autowired
    private AbcCisInvoiceRegionFeignClient client;

    @HystrixCommand(fallbackMethod = "pushPurchaseMessageHystrix")
    public OpsCommonRsp pushPurchaseMessage(String clinicId, ClinicCurrentPurchaseCreatedMessage message) {
        return FeignClientRpcTemplate.dealRpcClientMethod("pushPurchaseMessage",
                () -> client.purchaseItemCreated(clinicId, message), clinicId, message);
    }
}
