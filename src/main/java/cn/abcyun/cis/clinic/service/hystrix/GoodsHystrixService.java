package cn.abcyun.cis.clinic.service.hystrix;

import cn.abcyun.bis.rpc.sdk.cis.model.goods.OpsCommonRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.config.GoodsConfigView;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/16 17:14
 */
@Slf4j
public class GoodsHystrixService {

    protected OpsCommonRsp openPharmacyFallBack(String chainId, String clinicId,
                                     String coChainId, String coClinicId,
                                     String regionId,
                                     int businessType) {
        log.error("服务降级 openPharmacyFallBack, chainId:{}, clinicId:{}, coChainId:{}, coClinicId:{}, regionId:{}, businessType:{}", chainId, clinicId, coChainId, coClinicId, regionId, businessType);
        return null;
    }

    protected OpsCommonRsp delPharmacyFallBack(String chainId, String clinicId,
                                    String coChainId, String coClinicId,
                                    String regionId,
                                    int businessType) {
        log.error("服务降级 delPharmacyFallBack, chainId:{}, clinicId:{}, coChainId:{}, coClinicId:{}, regionId:{}, businessType:{}", chainId, clinicId, coChainId, coClinicId, regionId, businessType);
        return null;
    }

    protected List<GoodsConfigView> batchGetGoodsConfigFallBack(List<String> clinicIds) {
        log.error("服务降级 batchGetGoodsConfig, clinicIds:{}", clinicIds);
        return new ArrayList<>();
    }
}
