package cn.abcyun.cis.clinic.service;

import cn.abcyun.cis.clinic.api.view.BusinessCollaborationPlatformDictView;
import cn.abcyun.cis.clinic.model.BusinessCollaborationPlatformDict;
import cn.abcyun.cis.clinic.repository.BusinessCollaborationPlatformDictRepository;
import cn.abcyun.cis.core.db.UseReadOnlyDB;
import cn.abcyun.common.model.AbcListPage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 业务协同平台字典服务
 * <AUTHOR>
 * @date 2025/10/15 20:23
 */
@Service
@Slf4j
public class BizCollaborationPlatformDictService {

    @Autowired
    private BusinessCollaborationPlatformDictRepository dictRepository;

    /**
     * 根据dictType获取字典码表
     * 有缓存
     *
     * @param dictType     字典类型
     * @param platformType
     * @return 字典列表
     */
    @Transactional(readOnly = true)
    @UseReadOnlyDB
    @Cacheable(cacheNames = "biz-collaboration-platform-dict", key = "#dictType + ':' + #platformType")
    public AbcListPage<BusinessCollaborationPlatformDictView> getDictByDictType(int dictType, int platformType) {
        // 查询指定类型且未删除的字典数据
        List<BusinessCollaborationPlatformDict> dictList = dictRepository.findByPlatformTypeAndDictTypeAndIsDeleted(platformType, dictType, 0);

        if (dictList == null) {
            dictList = Collections.emptyList();
        }

        // 转换为视图对象
        List<BusinessCollaborationPlatformDictView> viewList = dictList.stream()
                .map(BusinessCollaborationPlatformDictView::from)
                .collect(Collectors.toList());

        // 构建结果
        AbcListPage<BusinessCollaborationPlatformDictView> result = new AbcListPage<>();
        result.setRows(viewList);
        return result;
    }
}
