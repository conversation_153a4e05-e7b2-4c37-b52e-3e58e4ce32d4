package cn.abcyun.cis.clinic.service;

import cn.abcyun.bis.rpc.sdk.cis.model.clinic.DepartmentBase;
import cn.abcyun.cis.clinic.api.view.*;
import cn.abcyun.cis.clinic.exception.ClinicError;
import cn.abcyun.cis.clinic.exception.ClinicException;
import cn.abcyun.cis.clinic.model.*;
import cn.abcyun.cis.clinic.repository.ClinicDeviceRoomDepartmentRepository;
import cn.abcyun.cis.clinic.repository.ClinicDeviceRoomRepository;
import cn.abcyun.cis.clinic.utils.MergeTool;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.exception.ParamNotValidException;
import cn.abcyun.cis.commons.util.FillUtils;
import cn.abcyun.cis.commons.util.ListUtils;
import cn.abcyun.cis.commons.util.TextUtils;
import cn.abcyun.cis.core.db.UseReadOnlyDB;
import cn.abcyun.cis.idgenerator.AbcIdGenerator;
import cn.abcyun.common.model.AbcListPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/2/6 10:16
 */
@Service
public class ClinicDeviceRoomService {
    @Autowired
    private AbcIdGenerator abcIdGenerator;
    @Autowired
    private ClinicDeviceRoomRepository clinicDeviceRoomRepository;
    @Autowired
    private ClinicDeviceRoomDepartmentRepository clinicDeviceRoomDepartmentRepository;
    @Autowired
    private DepartmentService departmentService;
    @Autowired
    private EmployeeService employeeService;


    /**
     * 根据clinicId查询
     */
    public List<ClinicDeviceRoom> findRoomByClinicId(String chainId, String clinicId) {
        if (StringUtils.isEmpty(chainId) || StringUtils.isEmpty(clinicId)) {
            return Collections.emptyList();
        }
        return clinicDeviceRoomRepository.findByChainIdAndClinicIdAndIsDeleted(chainId, clinicId, 0);
    }

    /**
     * 根据机房id批量查询
     */
    public List<ClinicDeviceRoom> findRoomByIdsIn(String chainId, String clinicId, Collection<String> ids, int withDeleted) {
        if (StringUtils.isEmpty(chainId) || CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        List<ClinicDeviceRoom> deviceRoomList;
        if (StringUtils.isEmpty(clinicId)) {
            deviceRoomList = clinicDeviceRoomRepository.findByChainIdAndIdIn(chainId, ids);
        } else {
            deviceRoomList = clinicDeviceRoomRepository.findByChainIdAndClinicIdAndIdIn(chainId, clinicId, ids);
        }
        if (withDeleted == 1) {
            return deviceRoomList;
        }
        return deviceRoomList.stream()
                .filter(deviceRoom -> deviceRoom.getIsDeleted() == 0)
                .collect(Collectors.toList());
    }

    /**
     * 根据机房id查询机房信息
     */
    public ClinicDeviceRoom findRoomById(String id) {
        if (StringUtils.isEmpty(id)) {
            return null;
        }
        return clinicDeviceRoomRepository.findById(id).orElse(null);
    }

    /**
     * 根据clinicId+机房名称查询
     */
    public List<ClinicDeviceRoom> findRoomByClinicIdAndName(String chainId, String clinicId, String name) {
        if (StringUtils.isEmpty(chainId) || StringUtils.isEmpty(clinicId) || StringUtils.isEmpty(name)) {
            return Collections.emptyList();
        }
        return clinicDeviceRoomRepository.findByChainIdAndClinicIdAndNameAndIsDeleted(chainId, clinicId, name, 0);
    }

    /**
     * 根据机房id查询机房科室关联
     */
    List<ClinicDeviceRoomDepartment> findRoomDepartmentByRoomId(String roomId) {
        if (StringUtils.isEmpty(roomId)) {
            return Collections.emptyList();
        }
        return clinicDeviceRoomDepartmentRepository.findByRoomIdAndIsDeleted(roomId, 0);
    }

    /**
     * 根据机房id集合批量查询机房科室关联
     */
    List<ClinicDeviceRoomDepartment> findRoomDepartmentByRoomIdIn(Collection<String> roomIds) {
        if (CollectionUtils.isEmpty(roomIds)) {
            return Collections.emptyList();
        }
        return clinicDeviceRoomDepartmentRepository.findByRoomIdInAndIsDeleted(roomIds, 0);
    }

    /**
     * 新增
     */
    @Transactional
    public ClinicDeviceRoomView insertRoom(String chainId, String clinicId, String operatorId, ClinicDeviceRoomInsertReq req) {
        // 检查机房名称是否存在
        List<ClinicDeviceRoom> existedRooms = findRoomByClinicIdAndName(chainId, clinicId, req.getName());
        if (!CollectionUtils.isEmpty(existedRooms)) {
            throw new ClinicException(ClinicError.DEVICE_ROOM_ALREADY_EXISTED);
        }
        // 检查科室是否存在
        Department department = departmentService.getDepartmentById(req.getDepartmentId());
        if (department == null
                || Department.Status.DELETED == department.getStatus()) {
            throw new NotFoundException();
        }
        // 创建机房
        ClinicDeviceRoom room = new ClinicDeviceRoom();
        room.setId(abcIdGenerator.getUID());
        room.setChainId(chainId);
        room.setClinicId(clinicId);
        room.setName(req.getName());
        FillUtils.fillCreatedBy(room, operatorId);
        clinicDeviceRoomRepository.save(room);
        // 机房科室关联
        ClinicDeviceRoomDepartment roomDepartment = new ClinicDeviceRoomDepartment();
        roomDepartment.setId(abcIdGenerator.getUID());
        roomDepartment.setChainId(chainId);
        roomDepartment.setClinicId(clinicId);
        roomDepartment.setRoomId(room.getId());
        roomDepartment.setDepartmentId(req.getDepartmentId());
        FillUtils.fillCreatedBy(roomDepartment, operatorId);
        clinicDeviceRoomDepartmentRepository.save(roomDepartment);

        return genDeviceRoomView(room, department);
    }

    /**
     * 更新机房
     */
    @Transactional
    public ClinicDeviceRoomView updateRoom(String id, String chainId, String clinicId, String operatorId, ClinicDeviceRoomInsertReq req) {
        // 检查修改的机房是否存在
        ClinicDeviceRoom room = findRoomById(id);
        if (room == null) {
            throw new NotFoundException();
        }
        // 检查机房名称是否存在
        List<ClinicDeviceRoom> existedRooms = findRoomByClinicIdAndName(chainId, clinicId, req.getName());
        existedRooms.removeIf(existedRoom -> existedRoom.getId().equals(id));
        if (!CollectionUtils.isEmpty(existedRooms)) {
            throw new ClinicException(ClinicError.DEVICE_ROOM_ALREADY_EXISTED);
        }
        // 检查科室是否存在
        Department department = departmentService.getDepartmentById(req.getDepartmentId());
        if (department == null
                || Department.Status.DELETED == department.getStatus()) {
            throw new NotFoundException();
        }
        // 更新机房信息
        if (!TextUtils.equals(room.getName(), req.getName())) {
            room.setName(req.getName());
            FillUtils.fillLastModifiedBy(room, operatorId);
            clinicDeviceRoomRepository.save(room);
        }
        // 更新机房科室关联
        List<ClinicDeviceRoomDepartment> roomDepartmentList = findRoomDepartmentByRoomId(room.getId());
        updateRoomDepartment(room, roomDepartmentList, Collections.singletonList(req.getDepartmentId()), operatorId);
        return genDeviceRoomView(room, department);
    }

    /**
     * 删除机房
     */
    @Transactional
    public void deleteRoom(String id, String chainId, String clinicId, String operatorId) {
        ClinicDeviceRoom room = findRoomById(id);
        if (room == null
                || room.getIsDeleted() == 1
                || !room.getChainId().equals(chainId)
                || !room.getClinicId().equals(clinicId)) {
            throw new NotFoundException();
        }
        // 删除机房
        room.setIsDeleted(1);
        FillUtils.fillLastModifiedBy(room, operatorId);
        // 删除机房科室关联
        List<ClinicDeviceRoomDepartment> roomDepartments = findRoomDepartmentByRoomId(id);
        if (!CollectionUtils.isEmpty(roomDepartments)) {
            roomDepartments.forEach(roomDepartment -> {
                roomDepartment.setIsDeleted(1);
                FillUtils.fillLastModifiedBy(roomDepartment, operatorId);
            });
            clinicDeviceRoomDepartmentRepository.saveAll(roomDepartments);
        }
    }

    /**
     * 更新机房科室关联
     */
    private List<ClinicDeviceRoomDepartment> updateRoomDepartment(ClinicDeviceRoom room,
                                                                  List<ClinicDeviceRoomDepartment> roomDepartmentList,
                                                                  List<String> departmentIds,
                                                                  String operatorId) {
        Assert.notNull(room, "room must not be null");
        if (roomDepartmentList == null) {
            roomDepartmentList = new ArrayList<>();
        }
        if (departmentIds == null) {
            departmentIds = new ArrayList<>();
        }
        if (StringUtils.isEmpty(operatorId)) {
            operatorId = Constants.DEFAULT_OPERATOR_ID;
        }
        String finalOperatorId = operatorId;

        BiFunction<String, ClinicDeviceRoomDepartment, Boolean> isEqualKeyFunc =
                (departmentId, clinicDeviceRoomDepartment) ->
                        TextUtils.equals(departmentId, clinicDeviceRoomDepartment.getDepartmentId());

        Function<String, ClinicDeviceRoomDepartment> insertFunc =
                departmentId -> {
                    ClinicDeviceRoomDepartment deviceRoomDepartment = new ClinicDeviceRoomDepartment();
                    deviceRoomDepartment.setId(abcIdGenerator.getUID());
                    deviceRoomDepartment.setChainId(room.getChainId());
                    deviceRoomDepartment.setClinicId(room.getClinicId());
                    deviceRoomDepartment.setRoomId(room.getId());
                    deviceRoomDepartment.setDepartmentId(departmentId);
                    FillUtils.fillCreatedBy(deviceRoomDepartment, finalOperatorId);
                    return deviceRoomDepartment;
                };

        BiConsumer<String, ClinicDeviceRoomDepartment> updateFunc =
                (departmentId, clinicDeviceRoomDepartment) -> {
                };

        Function<ClinicDeviceRoomDepartment, Boolean> deleteFunc =
                deviceRoomDepartment -> {
                    deviceRoomDepartment.setIsDeleted(1);
                    FillUtils.fillLastModifiedBy(deviceRoomDepartment, finalOperatorId);
                    return false;
                };

        MergeTool<String, ClinicDeviceRoomDepartment> mergeTool = new MergeTool<>();
        mergeTool.setSrc(departmentIds)
                .setDst(roomDepartmentList)
                .setIsEqualKeyFunc(isEqualKeyFunc)
                .setInsertFunc(insertFunc)
                .setUpdateFunc(updateFunc)
                .setDeleteFunc(deleteFunc)
                .doMerge();
        return clinicDeviceRoomDepartmentRepository.saveAll(roomDepartmentList);
    }

    /**
     * 生成 ClinicDeviceRoomView
     */
    private ClinicDeviceRoomView genDeviceRoomView(ClinicDeviceRoom room,
                                                   Department department) {
        return new ClinicDeviceRoomView()
                .fillRoomInfo(room)
                .fillDepartmentInfo(department);
    }

    /**
     * 查询所有机房列表
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public List<ClinicDeviceRoomView> listRoomByClinic(String chainId, String clinicId) {
        List<ClinicDeviceRoom> roomList = findRoomByClinicId(chainId, clinicId);
        return fillRoomDepartment(roomList);
    }

    /**
     * 根据机房id批量查询信息
     */
    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public List<ClinicDeviceRoomView> listRoomByIds(ClinicDeviceRoomQueryByIdsReq req) {
        List<ClinicDeviceRoom> roomList = findRoomByIdsIn(req.getChainId(), req.getClinicId(), req.getRoomIds(), req.getWithDeleted());
        return fillRoomDepartment(roomList);
    }

    /**
     * 填充机房科室信息
     */
    private List<ClinicDeviceRoomView> fillRoomDepartment(List<ClinicDeviceRoom> roomList) {
        if (CollectionUtils.isEmpty(roomList)) {
            return Collections.emptyList();
        }
        List<String> roomIds = roomList.stream().map(ClinicDeviceRoom::getId).collect(Collectors.toList());
        List<ClinicDeviceRoomDepartment> roomDepartmentList = findRoomDepartmentByRoomIdIn(roomIds);
        Map<String, List<ClinicDeviceRoomDepartment>> roomDepartmentMap = ListUtils.groupByKey(roomDepartmentList, ClinicDeviceRoomDepartment::getRoomId);
        Map<String, Department> departmentMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(roomDepartmentList)) {
            List<String> departmentIds = roomDepartmentList.stream()
                    .map(ClinicDeviceRoomDepartment::getDepartmentId)
                    .distinct()
                    .collect(Collectors.toList());
            List<Department> departmentList = departmentService.findDepartmentByIds(departmentIds, 0);
            departmentMap = ListUtils.toMap(departmentList, Department::getId);
        }

        Map<String, Department> finalDepartmentMap = departmentMap;
        return roomList.stream()
                .map(room -> {
                    ClinicDeviceRoomView roomView = new ClinicDeviceRoomView();
                    roomView.fillRoomInfo(room);
                    List<ClinicDeviceRoomDepartment> roomDepartments = roomDepartmentMap.getOrDefault(room.getId(), null);
                    if (CollectionUtils.isEmpty(roomDepartments)) {
                        return roomView;
                    }
                    roomView.fillDepartmentInfo(finalDepartmentMap.get(roomDepartments.get(0).getDepartmentId()));
                    return roomView;
                })
                .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public List<ClinicDeviceRoomView> listRoomsByEmployeeId(String chainId, String clinicId, String employeeId) {
        //查看员工的权限，判断是不是管理员
        ClinicEmployee clinicEmployee = employeeService.findClinicEmployee(clinicId, employeeId);
        if (clinicEmployee == null) {
            throw new NotFoundException("诊所员工不存在");
        }
        List<ClinicDeviceRoom> rooms;
        if (!clinicEmployee.isAdmin()) {
            //查看员工所在的科室
            AbcListPage<DepartmentView> departmentPage = departmentService.queryDepartmentsByEmployeeId(employeeId, clinicId);
            List<String> departmentIds = departmentPage != null ? departmentPage.getRows().stream().map(DepartmentView::getId).collect(Collectors.toList()) : new ArrayList<>();
            if (CollectionUtils.isEmpty(departmentIds)) {
                return Collections.emptyList();
            }

            //查看所有科室包含的机房
            List<ClinicDeviceRoomDepartment> roomDepartmentList = clinicDeviceRoomDepartmentRepository.findByDepartmentIdInAndClinicIdAndIsDeleted(departmentIds, clinicId, 0);
            if (CollectionUtils.isEmpty(roomDepartmentList)) {
                return Collections.emptyList();
            }

            //查询机房信息
            rooms = findRoomByIdsIn(chainId, clinicId, roomDepartmentList.stream().map(ClinicDeviceRoomDepartment::getRoomId).collect(Collectors.toList()), 0);
        } else {
            //管理员查看所有机房
            rooms = clinicDeviceRoomRepository.findByChainIdAndClinicIdAndIsDeleted(chainId, clinicId, 0);
        }
        
        return rooms.stream()
                .map(room -> {
                    ClinicDeviceRoomView roomView = new ClinicDeviceRoomView();
                    roomView.fillRoomInfo(room);
                    return roomView;
                })
                .collect(Collectors.toList());
    }
}
