package cn.abcyun.cis.clinic.service;

import cn.abcyun.bis.rpc.sdk.bp.model.NotifyClinicEditionOrderFinishReq;
import cn.abcyun.bis.rpc.sdk.oa.model.CorpNotifyCustomerLeadsReq;
import cn.abcyun.cis.clinic.api.view.ClinicEditionOrderView;
import cn.abcyun.cis.clinic.client.OaManagementClient;
import cn.abcyun.cis.clinic.service.hystrix.BpEntranceHystrixService;
import cn.abcyun.cis.commons.util.JsonUtils;
import cn.abcyun.region.rpc.sdk.client.bp.BpEntranceFeignClient;
import com.fasterxml.jackson.databind.JsonNode;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class BpEntranceService extends BpEntranceHystrixService {

    @Autowired
    private BpEntranceFeignClient bpEntranceFeignClient;

    @Autowired
    private OaManagementClient oaManagementClient;

    @Autowired
    private BpEntranceService self;

    public void notifyQWClinicEditionOrderFinished(ClinicEditionOrderView clinicEditionOrderView) {
        if (clinicEditionOrderView == null) {
            return;
        }

        JsonNode orderJson = JsonUtils.dumpAsJsonNode(clinicEditionOrderView);

        NotifyClinicEditionOrderFinishReq reqBody = JsonUtils.readValue(orderJson, NotifyClinicEditionOrderFinishReq.class);
        try {
            self.notifyQWClinicEditionOrderFinished(reqBody);
            log.info("notifyQWClinicEditionOrderFinished orderId:{}", clinicEditionOrderView.getId());
        } catch (Exception e) {
            log.error("notifyQWClinicEditionOrderFinished error:", e);
        }

    }

    @HystrixCommand(fallbackMethod = "notifyQWClinicEditionOrderFinishedFallback")
    public void notifyQWClinicEditionOrderFinished(NotifyClinicEditionOrderFinishReq reqBody) {
        bpEntranceFeignClient.notifyClinicEditionOrderFinish(reqBody);
    }

    public void notifyCustomerLeads(String name, String mobile, String organName, String remoteIp) {
        CorpNotifyCustomerLeadsReq reqBody = new CorpNotifyCustomerLeadsReq();
        reqBody.setName(name);
        reqBody.setMobile(mobile);
        reqBody.setOrganName(organName);
        reqBody.setRemoteIp(remoteIp);
        try {
            oaManagementClient.notifyCustomerLeads(reqBody);
        } catch (Exception e) {
            log.error("notifyCustomerLeads error:", e);
        }

    }

}
