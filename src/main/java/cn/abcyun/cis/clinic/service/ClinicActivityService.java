package cn.abcyun.cis.clinic.service;

import cn.abcyun.bis.rpc.sdk.oa.model.ApplyUseRecordView;
import cn.abcyun.cis.clinic.amqp.MQProducer;
import cn.abcyun.cis.clinic.api.view.*;
import cn.abcyun.cis.clinic.client.BpCrmClient;
import cn.abcyun.cis.clinic.client.GlobalAuthClient;
import cn.abcyun.cis.clinic.exception.ClinicError;
import cn.abcyun.cis.clinic.exception.ClinicException;
import cn.abcyun.cis.clinic.model.*;
import cn.abcyun.cis.clinic.mybatis.mapper.ClinicEditionMapper;
import cn.abcyun.cis.clinic.repository.ClinicEditionActivityRepository;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.exception.ParamNotValidException;
import cn.abcyun.cis.commons.util.DateUtils;
import cn.abcyun.cis.commons.util.ListUtils;
import cn.abcyun.cis.commons.util.TextUtils;
import cn.abcyun.cis.core.db.UseReadOnlyDB;
import cn.abcyun.common.log.marker.AbcLogMarker;
import cn.abcyun.common.model.AbcListPage;
import cn.abcyun.region.rpc.sdk.client.auth.model.EmployeeWechatRefererOrderView;
import cn.abcyun.region.rpc.sdk.client.auth.model.ModifyRefererOrderReq;
import cn.abcyun.region.rpc.sdk.client.auth.model.RefererOrderCashRewardStatus;
import cn.abcyun.region.rpc.sdk.client.auth.model.UpdateRefererOrderCashRewardStatusReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/5/6 19:44
 */
@Service
@Slf4j
public class ClinicActivityService {
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    @Autowired
    private EmployeeService employeeService;
    @Autowired
    private OrganService organService;
    @Autowired
    private ClinicEditionService clinicEditionService;
    @Value("${redis-activity-key:activity}")
    private String redisActivityKey;
    @Autowired
    private ClinicEditionMapper clinicEditionMapper;
    @Autowired
    private BpCrmClient bpCrmClient;
    @Autowired
    private GlobalAuthClient globalAuthClient;
    @Autowired
    private ClinicEditionActivityRepository clinicEditionActivityRepository;

    /**
     * 获取活动参与者信息
     */
    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public QueryActivityEmployeeRsp getActivityEmployee(String employeeId, String chainId) {
        Employee employee = employeeService.findEmployeeById(employeeId, chainId);
        if (Objects.isNull(employee)) {
            throw new NotFoundException();
        }

        QueryActivityEmployeeRsp cacheValue = getActivityEmployeeFromRedis(employeeId);
        if (Objects.nonNull(cacheValue)) {
            return cacheValue;
        }

        QueryActivityEmployeeRsp rsp = new QueryActivityEmployeeRsp();
        BeanUtils.copyProperties(employee, rsp);

        boolean isRead = clinicEditionService.checkClinicEditionActivityIsRead(employeeId);
        rsp.setIsRead(isRead ? 1 : 0);

        if (!StringUtils.isEmpty(employee.getMobile())) {
            saveActivityEmployeeToRedis(employeeId, rsp);
        }
        return rsp;
    }

    /**
     * 绑定活动手机号
     */
    @Transactional
    public QueryActivityEmployeeRsp bindActivityEmployeeMobile(String employeeId, BindActivityMobileReq req, String chainId) {
        Employee employee = employeeService.findEmployeeById(employeeId, chainId);
        if (Objects.isNull(employee)) {
            throw new NotFoundException();
        }

        QueryActivityEmployeeRsp cacheValue = getActivityEmployeeFromRedis(employeeId);
        if (Objects.nonNull(cacheValue)) {
            // 更新绑定手机号
            cacheValue.setActivityMobile(req.getActivityMobile());
            saveActivityEmployeeToRedis(employeeId, cacheValue);
            return cacheValue;
        }

        boolean isRead = clinicEditionService.checkClinicEditionActivityIsRead(employeeId);

        QueryActivityEmployeeRsp rsp = new QueryActivityEmployeeRsp();
        BeanUtils.copyProperties(employee, rsp);
        rsp.setActivityMobile(req.getActivityMobile());
        rsp.setIsRead(isRead ? 1 : 0);
        saveActivityEmployeeToRedis(employeeId, rsp);
        return rsp;
    }


    /**
     * 标记活动已读
     */
    @Transactional
    public QueryActivityEmployeeRsp markActivityRead(String employeeId, String chainId) {
        Employee employee = employeeService.findEmployeeById(employeeId, chainId);
        if (Objects.isNull(employee)) {
            throw new NotFoundException();
        }

        clinicEditionService.markClinicEditionActivityRead(employeeId);

        QueryActivityEmployeeRsp cacheValue = getActivityEmployeeFromRedis(employeeId);
        if (Objects.nonNull(cacheValue)) {
            cacheValue.setIsRead(1);
            saveActivityEmployeeToRedis(employeeId, cacheValue);
            return cacheValue;
        }

        QueryActivityEmployeeRsp rsp = new QueryActivityEmployeeRsp();
        BeanUtils.copyProperties(employee, rsp);
        rsp.setIsRead(1);
        return rsp;
    }

    /**
     * 获取活动参与者的门店列表
     */
    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public List<ActivityOrganView> getActivityOrgans(String employeeId, String chainId) {
        QueryActivityEmployeeRsp activityEmployee = getActivityEmployee(employeeId, chainId);
        if (Objects.isNull(activityEmployee)) {
            throw new NotFoundException();
        }

        if (StringUtils.isEmpty(activityEmployee.getActivityMobile())) {
            // 未绑定活动手机号
            return Collections.emptyList();
        }
        Employee employee = employeeService.findByMobile(activityEmployee.getActivityMobile(), chainId);
        if (Objects.isNull(employee)) {
            // 绑定的手机号在系统中没有
            return Collections.emptyList();
        }
        List<ClinicEmployee> clinicEmployeeList = employeeService.findClinicEmployeeByEmployeeId(employee.getId());
        if (CollectionUtils.isEmpty(clinicEmployeeList)) {
            // 未加入门店
            return Collections.emptyList();
        }

        List<String> organIds = clinicEmployeeList.stream().map(ClinicEmployee::getClinicId).collect(Collectors.toList());
        List<Organ> organs = organService.findOrganBatch(organIds)
                .stream()
                .filter(organ -> organ.getNodeType() != Constants.OrganNodeType.CHAIN_HEAD_CLINIC)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(organs)) {
            return Collections.emptyList();
        }

        // 查询已经参与活动的门店
        List<ClinicEditionActivityObtained> obtainedClinic = clinicEditionService.getClinicEditionActivityObtained(organIds);
        Map<String, ClinicEditionActivityObtained> obtainedClinicMap = ListUtils.toMap(obtainedClinic, ClinicEditionActivityObtained::getClinicId);

        return organs.stream()
                .map(organ -> {
                    ActivityOrganView activityOrganView = new ActivityOrganView();
                    BeanUtils.copyProperties(organ, activityOrganView);
                    ClinicEditionActivityObtained activityObtained = obtainedClinicMap.getOrDefault(organ.getId(), null);
                    activityOrganView.setIsParticipated(Objects.nonNull(activityObtained) ? 1 : 0);
                    if (Objects.nonNull(activityObtained)) {
                        activityOrganView.setEquivalentAmount(activityObtained.getEquivalentAmount());
                    }
                    return activityOrganView;
                }).collect(Collectors.toList());

    }

    @Transactional
    public void applyClinicActivity(String employeeId, ClinicActivityApplyReq req, String clientIp) {
        QueryActivityEmployeeRsp employee = getActivityEmployeeFromRedis(employeeId);
        if (Objects.isNull(employee) || StringUtils.isEmpty(employee.getActivityMobile())) {
            log.warn("人员未绑定活动手机号 employeeId = {}, contactName = {}", employeeId, req.getName());
            throw new ClinicException(ClinicError.CLINIC_ACTIVITY_MOBILE_UNBIND);
        }

        clinicEditionService.registerClinicEditionActivity(employeeId, req.getName(), employee.getActivityMobile(), req.getOrganName(), clientIp);
    }

    @Transactional
    public ObtainClinicActivityRsp obtainClinicActivity(String operatorId, ObtainClinicActivityReq req) {
        ObtainClinicActivityRsp rsp = new ObtainClinicActivityRsp();

        QueryActivityEmployeeRsp activityEmployee = getActivityEmployeeFromRedis(operatorId);
        if (Objects.isNull(activityEmployee) || StringUtils.isEmpty(activityEmployee.getActivityMobile())) {
            throw new ClinicException(ClinicError.CLINIC_ACTIVITY_MOBILE_UNBIND);
        }
        Employee realEmployee = employeeService.findByMobile(activityEmployee.getActivityMobile(), null);
        if (Objects.isNull(realEmployee)) {
            rsp.setList(Collections.emptyList());
            return rsp;
        }
        List<ClinicEmployee> clinicEmployeeList = employeeService.findClinicEmployeeByEmployeeId(realEmployee.getId());
        if (CollectionUtils.isEmpty(clinicEmployeeList)) {
            rsp.setList(Collections.emptyList());
            return rsp;
        }

        List<String> joinedChainIds = clinicEmployeeList.stream().map(ClinicEmployee::getChainId).distinct().collect(Collectors.toList());
        List<String> joinedClinicIds = clinicEmployeeList.stream().map(ClinicEmployee::getClinicId).distinct().collect(Collectors.toList());
        List<String> clinicIds = req.getClinicIds().stream().filter(joinedClinicIds::contains).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(clinicIds)) {
            rsp.setList(getActivityClinicByChain(joinedChainIds));
            return rsp;
        }
        List<Organ> organs = organService.findOrganBatch(clinicIds);
        if (CollectionUtils.isEmpty(organs)) {
            rsp.setList(getActivityClinicByChain(joinedChainIds));
            return rsp;
        }

        List<ClinicEditionActivityObtained> activityObtainedList = clinicEditionService.obtainClinicEditionActivity(organs, operatorId);
        printObtainLogAfterTransaction(activityEmployee, activityObtainedList, organs, req.getFromWay());

        rsp.setList(getActivityClinicByChain(joinedChainIds));
        return rsp;
    }

    private void printObtainLogAfterTransaction(QueryActivityEmployeeRsp activityEmployee,
                                                List<ClinicEditionActivityObtained> activityObtainedList,
                                                List<Organ> organs,
                                                int fromWay) {
        if (Objects.isNull(activityEmployee) || CollectionUtils.isEmpty(activityObtainedList)) {
            return;
        }
        organs = CollectionUtils.isEmpty(organs) ? new ArrayList<>() : organs;

        Map<String, Organ> organMap = ListUtils.toMap(organs, Organ::getId);
        MQProducer.doAfterTransactionCommit(() -> {
            activityObtainedList.forEach(activityObtained -> {
                Organ organ = organMap.getOrDefault(activityObtained.getClinicId(), null);
                log.info(AbcLogMarker.MARKER_LONG_TIME,
                        "obtained-activity 领取活动, employeeId = {}, employeeName = {}, activityMobile = {}, " +
                                "wechatNickName = {}, wechatOpenIdMp = {}, " +
                                "organName = {}, " +
                                "fromWay = {}, " +
                                "equivalentAmount = {}, " +
                                "created = {}",
                        activityEmployee.getId(), activityEmployee.getName(), activityEmployee.getActivityMobile(),
                        activityEmployee.getWechatNickName(), activityEmployee.getWechatOpenIdMp(),
                        Objects.nonNull(organ) ? organ.getShortOrganNameIfHave() : null,
                        fromWay,
                        activityObtained.getEquivalentAmount(),
                        activityObtained.getCreated()
                );
            });
        });
    }

    private List<ActivityOrganView> getActivityClinicByChain(List<String> joinedChainIds) {
        if (CollectionUtils.isEmpty(joinedChainIds)) {
            return new ArrayList<>();
        }

        List<Organ> organs = organService.findOrganByParentIdIn(joinedChainIds);
        Map<String, Organ> organMap = ListUtils.toMap(organs, Organ::getId);
        List<String> allClinicIds = organs.stream().map(Organ::getId).collect(Collectors.toList());
        List<ClinicEditionActivityObtained> clinicActivityObtainedList = clinicEditionService.getClinicEditionActivityObtained(allClinicIds);
        return clinicActivityObtainedList
                .stream()
                .map(clinicEditionActivityObtained -> {
                    Organ organ = organMap.getOrDefault(clinicEditionActivityObtained.getClinicId(), null);
                    ActivityOrganView activityOrganView = new ActivityOrganView();
                    BeanUtils.copyProperties(organ, activityOrganView);
                    activityOrganView.setIsParticipated(1);
                    activityOrganView.setEquivalentAmount(clinicEditionActivityObtained.getEquivalentAmount());
                    return activityOrganView;
                }).collect(Collectors.toList());
    }

    private String employeeRedisActivityKey(String employeeId) {
        return String.format("%s_%s", redisActivityKey, employeeId);
    }

    private void saveActivityEmployeeToRedis(String employeeId, QueryActivityEmployeeRsp rsp) {
        redisTemplate.opsForValue().set(employeeRedisActivityKey(employeeId), rsp);
    }

    public QueryActivityEmployeeRsp getActivityEmployeeFromRedis(String employeeId) {
        return (QueryActivityEmployeeRsp) redisTemplate.opsForValue().get(employeeRedisActivityKey(employeeId));
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public AbcListPage<RewardView> listRewards(int limit, int offset, Integer rewardStatus, String keyword, String beginDate, String endDate) {
        AbcListPage<RewardView> rsp = new AbcListPage<>();
        rsp.setRows(Collections.emptyList());
        rsp.setOffset(offset);
        rsp.setLimit(limit);
        rsp.setKeyword(keyword);

        Instant beginOfDay = null;
        Instant endOfDay = null;
        if (!StringUtils.isEmpty(beginDate) && !StringUtils.isEmpty(endDate)) {
            beginOfDay = DateUtils.getBeginOfDay(beginDate);
            endOfDay = DateUtils.getEndOfDay(endDate);
            if (beginOfDay.isAfter(endOfDay)) {
                throw new ParamNotValidException("开始时间能大于结束时间");
            }
        }

        int total = clinicEditionMapper.findRewardsCount(rewardStatus, keyword, beginOfDay, endOfDay);
        rsp.setTotal(total);
        if (total == 0) {
            return rsp;
        }

        List<RewardView> rows = clinicEditionMapper.findRewards(limit, offset, rewardStatus, keyword, beginOfDay, endOfDay);
        rsp.setRows(rows);
        return rsp;
    }

    @Transactional
    public void updateRewardsClue(String editionOrderId, UpdateRewardClueReq req) {
        // 查询版本订单
        ClinicEditionOrder editionOrder = clinicEditionService.findClinicEditionOrderById(editionOrderId);
        if (editionOrder == null || !TextUtils.equals(editionOrder.getBindClinicId(), req.getClinicId())) {
            throw new NotFoundException("版本订单不存在");
        }
        if (editionOrder.getStatus() != ClinicEditionOrder.Status.PAID) {
            throw new ClinicException(ClinicError.EDITION_ORDER_STATUS_NOT_PAID);
        }
        boolean isRemoveClue = StringUtils.isEmpty(req.getClueId());
        boolean isClueChanged = !TextUtils.equals(editionOrder.getClueId(), req.getClueId());

        // 校验线索是否已经关联其他门店
        if (isClueChanged && !isRemoveClue) {
            EmployeeWechatRefererOrderView refererOrder = globalAuthClient.getRefererOrderByClueId(req.getClueId());
            if (refererOrder != null
                    && !TextUtils.equals(editionOrder.getBindClinicId(), refererOrder.getOrderOrganId())) {
                throw new ClinicException(ClinicError.CLUE_ALREADY_ASSOCIATED);
            }
        }

        ApplyUseRecordView applyUseRecord = null;
        if (isClueChanged && !isRemoveClue) {
            // 判断线索是否已经被其他的订单绑定了
            ClinicEditionOrder otherEditionOrder = clinicEditionService.findClinicEditionOrderByClueId(req.getClueId());
            if (otherEditionOrder != null && !otherEditionOrder.getId().equals(editionOrderId)) {
                throw new ParamNotValidException("线索已经被其他的版本订单绑定了");
            }
            // 判断线索id是否正确
            applyUseRecord = bpCrmClient.getApplyUseById(req.getClueId());
            if (applyUseRecord == null) {
                throw new ParamNotValidException("线索id不正确");
            }
        }


        ClinicEditionPayOrder clinicEditionPayOrder = null;
        if (StringUtils.isEmpty(editionOrder.getEditionPayOrderId())) {
            clinicEditionPayOrder = clinicEditionService.findClinicEditionPayOrder(editionOrder.getEditionPayOrderId());
        }
        if (isRemoveClue) {
            // 清空线索
            editionOrder.setReferrerCode(null);
            if (clinicEditionPayOrder != null) {
                clinicEditionPayOrder.setClueId(req.getClueId());
                clinicEditionPayOrder.setReferrerCode(null);
            }
        } else if (isClueChanged) {
            // 添加线索
            editionOrder.setReferrerCode(applyUseRecord.getReferrerCode());
            if (clinicEditionPayOrder != null) {
                clinicEditionPayOrder.setClueId(req.getClueId());
                clinicEditionPayOrder.setReferrerCode(applyUseRecord.getReferrerCode());
            }
        }
        editionOrder.setClueId(req.getClueId());

        //rpc 更新线索
        ModifyRefererOrderReq refererOrderReq = new ModifyRefererOrderReq();
        refererOrderReq.setRefererCode(editionOrder.getReferrerCode());
        refererOrderReq.setOrganId(editionOrder.getBindClinicId());
        refererOrderReq.setPaidTime(editionOrder.getPaidTime());
        refererOrderReq.setClueId(StringUtils.isEmpty(editionOrder.getClueId()) ? null : Long.valueOf(editionOrder.getClueId()));
        globalAuthClient.modifyRefererOrder(refererOrderReq);

        clinicEditionService.updateClinicEditionOrder(editionOrder);
        if (clinicEditionPayOrder != null) {
            clinicEditionService.updateClinicEditionPayOrder(clinicEditionPayOrder);
        }
    }

    public void distributeRewards(String editionOrderId, String clinicId, DistributeRewardsReq req) {
        // 版本订单是否存在
        ClinicEditionOrder editionOrder = clinicEditionService.findClinicEditionOrderById(editionOrderId);
        if (editionOrder == null || !TextUtils.equals(editionOrder.getBindClinicId(), clinicId)) {
            throw new NotFoundException("版本订单不存在");
        }
        // 订单是否已经支付
        if (editionOrder.getStatus() != ClinicEditionOrder.Status.PAID) {
            throw new ClinicException(ClinicError.EDITION_ORDER_STATUS_NOT_PAID);
        }
        // 订单是否已经关联的线索
        if (StringUtils.isEmpty(editionOrder.getClueId())) {
            throw new ClinicException(ClinicError.ORDER_NOT_ASSOCIATED_CLUE);
        }
        // 判断线索是否存在
        ApplyUseRecordView applyUseRecord = bpCrmClient.getApplyUseById(editionOrder.getClueId());
        if (applyUseRecord == null) {
            throw new NotFoundException("线索不存在");
        }
        // 判断推荐人是否是系统内部人员
        if (!StringUtils.isEmpty(applyUseRecord.getReferrerAbcEmployeeId())) {
            SysUser sysUser = employeeService.findSysUser(applyUseRecord.getReferrerAbcEmployeeId());
            if (sysUser != null) {
                throw new ClinicException(ClinicError.REFERER_IS_SYS_USER);
            }
        }

        // rpc 发放奖励
        UpdateRefererOrderCashRewardStatusReq updateRefererOrderCashRewardStatusReq = new UpdateRefererOrderCashRewardStatusReq();
        updateRefererOrderCashRewardStatusReq.setClueId(Long.parseLong(editionOrder.getClueId()));
        updateRefererOrderCashRewardStatusReq.setStatus(RefererOrderCashRewardStatus.AWARDED);
        updateRefererOrderCashRewardStatusReq.setAttachments(req.getAttachments());
        globalAuthClient.updateRefererOrderRewardStatus(updateRefererOrderCashRewardStatusReq);
    }

    /**
     * 根据活动ID查询活动详情
     * @param activityId 活动ID
     * @return 活动详情视图，如果不存在则返回null
     */
    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public ClinicEditionActivityDetailView getActivityDetailById(String activityId) {
        ClinicEditionActivity activity = getActivityById(activityId);
        return convertToActivityDetailView(activity);
    }


    /**
     * 根据活动ID查询活动详情
     * @param activityId 活动ID
     * @return 活动详情，如果不存在则返回null
     */
    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public ClinicEditionActivity getActivityById(String activityId) {
        if (TextUtils.isEmpty(activityId)) {
            return null;
        }
        return clinicEditionActivityRepository.findById(activityId).orElse(null);
    }

    /**
     * 转换活动实体为详情视图对象
     * @param activity 活动实体
     * @return 详情视图对象
     */
    public ClinicEditionActivityDetailView convertToActivityDetailView(ClinicEditionActivity activity) {
        if (activity == null) {
            return null;
        }

        ClinicEditionActivityDetailView view = new ClinicEditionActivityDetailView();
        BeanUtils.copyProperties(activity, view);

        // 设置活动状态信息
        Instant now = Instant.now();
        boolean isActive = activity.getBeginDate().isBefore(now) && activity.getEndDate().isAfter(now);
        boolean isValid = activity.getStatus() == 1 && isActive;

        view.setIsValid(isValid ? 1 : 0);

        return view;
    }
}
