package cn.abcyun.cis.clinic.service;

import cn.abcyun.cis.clinic.api.view.ClinicRegionView;
import cn.abcyun.cis.clinic.api.view.QueryByRegionNameReq;
import cn.abcyun.cis.clinic.api.view.RegionDetailView;
import cn.abcyun.cis.clinic.api.view.SwitchClinicModeRsp;
import cn.abcyun.cis.clinic.model.*;
import cn.abcyun.cis.clinic.repository.ClinicRegionDomainRepository;
import cn.abcyun.cis.clinic.repository.ClinicRegionOrganRepository;
import cn.abcyun.cis.clinic.repository.ClinicRegionRepository;
import cn.abcyun.cis.commons.exception.CisCustomException;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.util.ListUtils;
import cn.abcyun.cis.core.db.UseReadOnlyDB;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/4/12 11:03
 */
@Service
@Slf4j
public class ClinicRegionService {
    private final ClinicRegion defaultClinicRegion = new ClinicRegion();
    @Autowired
    private ClinicRegionRepository clinicRegionRepository;
    @Autowired
    private ClinicRegionOrganRepository clinicRegionOrganRepository;
    @Autowired
    private ClinicRegionDomainRepository clinicRegionDomainRepository;
    @Autowired
    private OrganService organService;
    @Value("${abc.env}")
    protected String env;


    @PostConstruct
    public void init() {
        defaultClinicRegion.setId("1");
        defaultClinicRegion.setName("分区1");
        defaultClinicRegion.setKey("Shanghai");
    }

    /**
     * 根据连锁id获取分区domain信息
     * @param chainId 连锁id
     * @param type {@link ClinicRegionDomain.Type}
     * @return List<ClinicRegionDomain>
     */
    public List<ClinicRegionDomain> findClinicRegionDomainByChainIdAndType(String chainId, int type) {
        if (StringUtils.isEmpty(chainId)) {
            return new ArrayList<>();
        }
        ClinicRegionOrgan clinicRegionOrgan = findClinicRegionOrganByChainId(chainId);
        if (clinicRegionOrgan == null) {
            return new ArrayList<>();
        }
        return clinicRegionDomainRepository.findByRegionIdAndType(clinicRegionOrgan.getRegionId(), type);
    }

    /**
     * 根据连锁id获取分区scrm-domain信息
     * @param chainId 连锁id
     * @return ClinicRegionDomain
     */
    public ClinicRegionDomain findScrmClinicRegionDomainByChainId(String chainId) {
        return findClinicRegionDomainByChainIdAndType(chainId, ClinicRegionDomain.Type.SCRM)
                .stream()
                .findFirst()
                .orElse(null);
    }

    public List<ClinicRegionDomain> findClinicRegionDomainByRegionId(String regionId) {
        if (StringUtils.isEmpty(regionId)) {
            return new ArrayList<>();
        }
        return clinicRegionDomainRepository.findByRegionId(regionId);
    }

    /**
     * 根据连锁id获取分区信息
     *
     * @param chainId
     * @return ClinicRegion
     */
    public ClinicRegion findClinicRegionByChainId(String chainId) {
        if (StringUtils.isEmpty(chainId)) {
            return null;
        }
        ClinicRegionOrgan clinicRegionOrgan = findClinicRegionOrganByChainId(chainId);
        if (clinicRegionOrgan == null) {
            return null;
        }
        return findClinicRegionByRegionId(clinicRegionOrgan.getRegionId());
    }

    public ClinicRegionOrgan findClinicRegionOrganByChainId(String chainId) {
        if (StringUtils.isEmpty(chainId)) {
            return null;
        }
        return clinicRegionOrganRepository.findByChainId(chainId).orElse(null);
    }

    /**
     * 根据连锁id查询分区信息
     */
    public List<ClinicRegionOrgan> findClinicRegionOrganByChainIds(List<String> chainIds) {
        if (CollectionUtils.isEmpty(chainIds)) {
            return new ArrayList<>();
        }
        return clinicRegionOrganRepository.findByChainIdIn(chainIds);
    }

    /**
     * 根据门店id获取分区信息
     *
     * @param clinicId
     * @return ClinicRegion
     */
    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public ClinicRegion findClinicRegionByClinicId(String clinicId) {
        if (StringUtils.isEmpty(clinicId)) {
            return null;
        }
        Organ organ = organService.getOrganById(clinicId);
        if (organ == null || organ.getStatus() >= 90) {
            throw new NotFoundException("门店不存在或已删除");
        }
        return findClinicRegionByChainId(organ.getParentId());
    }

    /**
     * 根据分区id获取分区信息
     *
     * @param regionId
     * @return ClinicRegion
     */
    public ClinicRegion findClinicRegionByRegionId(String regionId) {
        if (StringUtils.isEmpty(regionId)) {
            return null;
        }
        return clinicRegionRepository.findById(regionId).orElse(null);
    }


    /**
     * 查询门店切换方式
     *
     * @param clinicId 门店id
     * @return SwitchClinicModeRsp
     */
    @UseReadOnlyDB
    @Transactional(readOnly = true)
    public SwitchClinicModeRsp getSwitchClinicMode(String clinicId) {
        SwitchClinicModeRsp rsp = new SwitchClinicModeRsp();
        ClinicRegion clinicRegion = findClinicRegionByClinicId(clinicId);
        rsp.setMode(clinicRegion == null ? SwitchClinicModeRsp.Mode.OLD : SwitchClinicModeRsp.Mode.NEW);
        return rsp;
    }

    /**
     * 获取门店分区信息
     */
    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public ClinicRegionView getClinicRegionInfoById(String clinicId) {
        ClinicRegion clinicRegion = findClinicRegionByClinicId(clinicId);
        if (clinicRegion == null) {
            clinicRegion = defaultClinicRegion;
        }
        return ClinicRegionView
                .from(clinicRegion)
                .fillRegionDomainList(findClinicRegionDomainByRegionId(clinicRegion.getId()));

    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public ClinicRegionView getClinicRegionInfoByShortId(String clinicShortId) {
        Organ organ = organService.getOrganByShortId(clinicShortId);
        if (organ == null || organ.getStatus() >= 90) {
            throw new NotFoundException("门店不存在");
        }
        return getClinicRegionInfoById(organ.getId());
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public ClinicRegionView getRegionInfoByRegionId(String regionId) {
        ClinicRegion clinicRegion = findClinicRegionByRegionId(regionId);
        if (clinicRegion == null) {
            throw new NotFoundException("分区不存在");
        }
        return ClinicRegionView
                .from(clinicRegion)
                .fillRegionDomainList(findClinicRegionDomainByRegionId(clinicRegion.getId()));
    }

    /**
     * 保存
     */
    public ClinicRegionOrgan save(ClinicRegionOrgan clinicRegionOrgan) {
        Assert.notNull(clinicRegionOrgan, "clinicRegionOrgan can not be null");
        return clinicRegionOrganRepository.save(clinicRegionOrgan);
    }

    /**
     * 获取所有业务分区
     */
    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public List<ClinicRegionView> getHisRegions() {
        List<ClinicRegion> clinicRegions = clinicRegionRepository.findAll()
                .stream()
                .filter(cr -> Integer.parseInt(cr.getId()) < 10000)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(clinicRegions)) {
            return new ArrayList<>();
        }

        List<String> regionIds = clinicRegions.stream().map(ClinicRegion::getId).collect(Collectors.toList());
        List<ClinicRegionDomain> clinicRegionDomains = findClinicRegionDomainByRegionIdIn(regionIds);
        Map<String, List<ClinicRegionDomain>> regionDomainMap = ListUtils.groupByKey(clinicRegionDomains, ClinicRegionDomain::getRegionId);

        List<ClinicRegionView> clinicRegionViews = new ArrayList<>();
        for (ClinicRegion clinicRegion : clinicRegions) {
            List<ClinicRegionDomain> regionDomains = regionDomainMap.getOrDefault(clinicRegion.getId(), Collections.emptyList());
            clinicRegionViews.add(ClinicRegionView.from(clinicRegion).fillRegionDomainList(regionDomains));
        }
        return clinicRegionViews;
    }

    public List<ClinicRegionDomain> findClinicRegionDomainByRegionIdIn(List<String> regionIds) {
        if (CollectionUtils.isEmpty(regionIds)) {
            return new ArrayList<>();
        }
        return clinicRegionDomainRepository.findByRegionIdIn(regionIds);
    }

    public ClinicRegionDomain findCurEnvClinicRegionDomainByChainIdAndType(String chainId, int type) {
        if (StringUtils.isEmpty(chainId)) {
            return null;
        }
        ClinicRegion clinicRegion = findClinicRegionByChainId(chainId);
        if (clinicRegion == null) {
            return null;
        }
        return findClinicRegionDomainByRegionIdAndTypeAndEnv(clinicRegion.getId(), type, env);
    }

    public ClinicRegionDomain findClinicRegionDomainByRegionIdAndTypeAndEnv(String regionId, int type, String env) {
        return clinicRegionDomainRepository.findFirstByRegionIdAndTypeAndEnv(regionId, type, env);
    }

    /**
     * 默认分区上海
     */
    public ClinicRegionDomain findDefaultHisClinicRegionDomain() {
        return findClinicRegionDomainByRegionIdAndTypeAndEnv(ClinicRegion.IdDefine.REGION_ID_1, ClinicRegionDomain.Type.HIS, env);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public RegionDetailView getRegionsByName(QueryByRegionNameReq reqBody) {
        AddressRegion province = organService.findAddressRegionByName(reqBody.getProvinceName(), null, AddressRegion.Level.PROVINCE);
        if (province == null) {
            List<String> candidateRegionNames = organService.findCandidateRegionNames(null, AddressRegion.Level.PROVINCE);
            throw new CisCustomException(400, "没有找到对应的省份，可选列表为：" + String.join(", ", candidateRegionNames));
        }

        AddressRegion city = organService.findAddressRegionByName(reqBody.getCityName(), province.getId(), AddressRegion.Level.CITY);
        if (city == null) {
            List<String> candidateRegionNames = organService.findCandidateRegionNames(province.getId(), AddressRegion.Level.CITY);
            throw new CisCustomException(400, "没有找到对应的城市，可选列表为：" + String.join(", ", candidateRegionNames));
        }

        AddressRegion district = organService.findAddressRegionByName(reqBody.getDistrictName(), city.getId(), AddressRegion.Level.DISTRICT);
        if (district == null) {
            List<String> candidateRegionNames = organService.findCandidateRegionNames(city.getId(), AddressRegion.Level.DISTRICT);
            throw new CisCustomException(400, "没有找到对应的区，可选列表为：" + String.join(", ", candidateRegionNames));
        }
        RegionDetailView result = new RegionDetailView();
        result.setProvince(cn.abcyun.cis.clinic.api.view.AddressRegionView.from(province));
        result.setCity(cn.abcyun.cis.clinic.api.view.AddressRegionView.from(city));
        result.setDistrict(cn.abcyun.cis.clinic.api.view.AddressRegionView.from(district));
        return result;
    }
}
