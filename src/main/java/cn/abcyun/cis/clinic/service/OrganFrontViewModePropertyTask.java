package cn.abcyun.cis.clinic.service;

import cn.abcyun.bis.rpc.sdk.property.base.PropertyInfo;
import cn.abcyun.bis.rpc.sdk.property.base.PropertyKey;
import cn.abcyun.bis.rpc.sdk.property.base.UpdatePropertyItemReq;
import cn.abcyun.bis.rpc.sdk.property.service.PropertyService;
import cn.abcyun.bis.rpc.sdk.property.service.model.PropertyConfigItem;
import cn.abcyun.cis.clinic.api.view.ClinicCurrentEditionComposeView;
import cn.abcyun.cis.clinic.api.view.ClinicCurrentEditionView;
import cn.abcyun.cis.clinic.model.AddressRegion;
import cn.abcyun.cis.clinic.model.Constants;
import cn.abcyun.cis.clinic.model.Organ;
import cn.abcyun.cis.clinic.repository.OrganRepository;
import cn.abcyun.cis.commons.util.JsonUtils;
import cn.abcyun.common.log.marker.AbcLogMarker;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/9/30 11:50
 */
@Service
@Slf4j
public class OrganFrontViewModePropertyTask {

    @Autowired
    private OrganRepository organRepository;

    @Autowired
    private PropertyService propertyService;

    @Autowired
    private ClinicEditionService clinicEditionService;

    @Value("${abc.env}")
    private String abcEnv;

    @Value("${abc.region-id}")
    private String envRegionId;


    @SchedulerLock(name = "front-view-mode-property-task", lockAtMostFor = "PT30M", lockAtLeastFor = "PT5M")
    public void cronSetFrontViewModeProperty() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        log.info(AbcLogMarker.MARKER_MESSAGE_INDEX, "cronSetFrontViewModeProperty: 开始执行前端视图模型定时任务, 环境: {}, 区域: {}", abcEnv, envRegionId);

        try {
            int totalProcessed = 0;
            int totalUpdated = 0;
            int batchSize = 100;
            String lastId = ""; // 游标，从空字符串开始

            while (true) {
                // 4. 使用游标查询organ（避免深分页）
                Pageable pageable = PageRequest.of(0, batchSize);
                List<String> provinceIds = Arrays.asList(
                        AddressRegion.ProvinceId.CHONG_QING,
                        AddressRegion.ProvinceId.SI_CHUAN,
                        AddressRegion.ProvinceId.SHAN_XI
                );
                List<Organ> organList = organRepository.findByStatusAndIdGreaterThanOrderByIdAsc(Organ.Status.NORMAL, lastId, pageable);

                if (CollectionUtils.isEmpty(organList)) {
                    break;
                }

                log.debug("cronSetFrontViewModeProperty: 从ID {} 开始，查询到{}个门店", lastId, organList.size());

                // 5. 查询property属性设置，过滤出没有设置property属性的门店
                List<Organ> unSetFrontViewModePropertyOrganList = filterOrgansWithoutFrontViewModeProperty(organList);

                if (!CollectionUtils.isEmpty(unSetFrontViewModePropertyOrganList)) {
                    log.info("cronSetFrontViewModeProperty: 找到{}个未设置前端视图模式属性的门店",
                            unSetFrontViewModePropertyOrganList.size());

                    // 6. 处理未设置属性的门店
                    int updated = processFrontViewModeProperty(unSetFrontViewModePropertyOrganList);
                    totalUpdated += updated;
                }

                totalProcessed += organList.size();

                // 更新游标为当前批次的最后一个ID
                lastId = organList.get(organList.size() - 1).getId();

                // 如果返回的数据少于批次大小，说明已经是最后一批
                if (organList.size() < batchSize) {
                    break;
                }
            }

            stopWatch.stop();
            log.info("cronSetFrontViewModeProperty: 前端视图模型定时任务执行完成, 总处理门店数: {}, 总更新门店数: {}, 耗时: {}秒",
                    totalProcessed, totalUpdated, stopWatch.getTotalTimeSeconds());

        } catch (Exception e) {
            stopWatch.stop();
            log.error("cronSetFrontViewModeProperty: 前端视图模型定时任务执行失败, 耗时: {}秒", stopWatch.getTotalTimeSeconds(), e);
        }
    }

    /**
     * 过滤出没有设置前端视图模式属性的门店
     */
    private List<Organ> filterOrgansWithoutFrontViewModeProperty(List<Organ> organList) {
        if (CollectionUtils.isEmpty(organList)) {
            return new ArrayList<>();
        }

        try {
            // 构建批量查询请求
            List<PropertyInfo> propertyInfos = organList.stream()
                    .map(organ -> {
                        PropertyInfo propertyInfo = new PropertyInfo();
                        propertyInfo.setScopeId(organ.getId());
                        propertyInfo.setPropertyKey(PropertyKey.CLINIC_BASIC_STAT_VIEW_MODE);
                        return propertyInfo;
                    })
                    .collect(Collectors.toList());
            // 批量查询属性配置
            List<PropertyConfigItem> propertyList = propertyService.batchGetConfigItems(propertyInfos);
            log.info("propertyService.batchGetConfigItems(propertyInfos) rsp = {}", JsonUtils.dump(propertyList));

            // 获取已设置属性的门店ID集合
            Set<String> setPropertyOrganIds = propertyList.stream()
                    .filter(a -> a.getValue().asInt() != 90)
                    .map(PropertyConfigItem::getScopeId)
                    .collect(Collectors.toSet());

            // 过滤出没有设置属性的门店
            return organList.stream()
                    .filter(organ -> !setPropertyOrganIds.contains(organ.getId()))
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("filterOrgansWithoutFrontViewModeProperty: 查询门店前端视图模式属性失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 处理前端视图模式属性设置
     */
    private int processFrontViewModeProperty(List<Organ> unSetFrontViewModePropertyOrganList) {
        int updatedCount = 0;

        for (Organ organ : unSetFrontViewModePropertyOrganList) {
            try {
                // 6.2 根据版本设置property属性
                int frontViewModeValue = determineFrontViewModeValue(organ);

                // 更新属性值
                UpdatePropertyItemReq<Integer> updateReq = new UpdatePropertyItemReq<>();
                updateReq.setPropertyKey(PropertyKey.CLINIC_BASIC_STAT_VIEW_MODE);
                updateReq.setScopeId(organ.getId());
                updateReq.setValue(frontViewModeValue);
                updateReq.setOperatorId(Constants.DEFAULT_OPERATOR_ID);
                updateReq.setChainId(organ.getParentId());
                updateReq.setClinicId(organ.getId());

                propertyService.updatePropertyValueByKey(updateReq);
                updatedCount++;
                Thread.sleep(50);
            } catch (Exception e) {
                log.error("processFrontViewModeProperty: 门店 {} 设置前端视图模式属性失败", organ.getId(), e);
            }
        }

        return updatedCount;
    }

    // 摘要视图
    private static final int FRONT_VIEW_MODE_BASIC = 0;
    // 详情视图
    private static final int FRONT_VIEW_MODE_DETAIL = 10;

    /**
     * 根据版本信息确定前端视图模式值
     * 如果是基础版设置为0，其他设置为10
     */
    private int determineFrontViewModeValue(Organ organ) {
        // 6.1 查询门店版本信息
        ClinicCurrentEditionComposeView editionComposeView = clinicEditionService.getClinicCurrentEditionComposeView(organ);

        if (editionComposeView == null || editionComposeView.getEdition() == null) {
            log.warn("processFrontViewModeProperty: 门店 {} 版本信息为空，跳过处理", organ.getId());
            return FRONT_VIEW_MODE_DETAIL;
        }

        ClinicCurrentEditionView edition = editionComposeView.getEdition();
        int editionId = Integer.parseInt(edition.getId());
        // 判断是否为基础版
        if (editionId == Constants.ClinicEditionId.BASIC ||
                editionId == Constants.ClinicEditionId.EYE_BASIC ||
                editionId == Constants.ClinicEditionId.PHARMACY_BASIC
        ) {
            return FRONT_VIEW_MODE_BASIC; // 基础版设置为0
        } else {
            return FRONT_VIEW_MODE_DETAIL; // 其他版本设置为10
        }
    }
}
