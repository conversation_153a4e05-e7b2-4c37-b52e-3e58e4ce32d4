package cn.abcyun.cis.clinic.service;

import cn.abcyun.bis.rpc.sdk.cis.client.AbcCisMallGoodsFeignClient;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.OpsCommonRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.mall.goods.CreateGoodsReduceRuleReq;
import cn.abcyun.cis.clinic.service.hystrix.MallGoodsHystrixService;
import cn.abcyun.cis.core.util.JsonUtils;
import cn.abcyun.common.model.AbcServiceResponseBody;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/7/1 下午2:57
 * @description TODO
 */
@Slf4j
@Service
public class MallGoodsService extends MallGoodsHystrixService {

    @Autowired
    private AbcCisMallGoodsFeignClient mallGoodsFeignClient;

    @HystrixCommand(fallbackMethod = "createGoodsReduceRuleFallBack")
    public boolean createGoodsReduceRule(CreateGoodsReduceRuleReq req) {
        log.info("调用商品库存扣减规则请求参数: {}", JsonUtils.dump(req));
        AbcServiceResponseBody<OpsCommonRsp> rsp = mallGoodsFeignClient.createGoodsReduceRule(req);
        if (Objects.isNull(rsp) || Objects.isNull(rsp.getData())) {
            return false;
        }
        OpsCommonRsp data = rsp.getData();
        return data.getCode() == OpsCommonRsp.SUCC;
    }

}
