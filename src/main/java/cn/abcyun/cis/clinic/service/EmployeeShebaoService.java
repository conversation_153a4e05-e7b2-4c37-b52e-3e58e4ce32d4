package cn.abcyun.cis.clinic.service;

import cn.abcyun.cis.clinic.api.view.ShebaoEmployeeQualificationReq;
import cn.abcyun.cis.clinic.api.view.invoice.ShebaoEmployeeQualification;
import cn.abcyun.cis.clinic.model.ClinicEmployee;
import cn.abcyun.cis.commons.util.DateUtils;
import cn.abcyun.cis.commons.util.ListUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * EmployeeService 拆分
 * 人员和医保相关的服务
 * <AUTHOR>
 * @date 2025/1/2 14:48
 */
@Service
@Slf4j
public class EmployeeShebaoService {
    @Autowired
    private EmployeeService employeeService;


    @Transactional(rollbackFor = Exception.class)
    public void updateShebaoQualification(String clinicId, ShebaoEmployeeQualificationReq req) {
        List<ClinicEmployee> clinicEmployeeList = employeeService.findClinicEmployee(clinicId).stream().filter(ce -> !StringUtils.isEmpty(ce.getNationalDoctorCode())).collect(Collectors.toList());
        Map<String, ClinicEmployee> clinicEmployeeMap = ListUtils.toMap(clinicEmployeeList, ClinicEmployee::getNationalDoctorCode);

        List<ClinicEmployee> needUpdateList = new ArrayList<>();
        for (ShebaoEmployeeQualification qualification : req.getQualifications()) {
            ClinicEmployee clinicEmployee = clinicEmployeeMap.getOrDefault(qualification.getDrCode(), null);
            if (clinicEmployee == null) {
                log.error("updateShebaoQualification clinicEmployee is null, clinicId:{}, drCode:{}", clinicId, qualification.getDrCode());
                continue;
            }
            clinicEmployee.setShebaoScoreSum(qualification.getScoreSum());
            clinicEmployee.setShebaoDrStatus(qualification.getStatus());
            clinicEmployee.setShebaoScoreYear(qualification.getScoreYear());
            clinicEmployee.setShebaoScoreUpdateTime(DateUtils.toInstant(req.getShebaoScoreUpdateTime(), DateUtils.sFormatterDateTime));
            needUpdateList.add(clinicEmployee);
        }
        employeeService.updateBatch(needUpdateList);
    }

}
