package cn.abcyun.cis.clinic.client;

import cn.abcyun.bis.rpc.sdk.cis.model.ec.EcAuthRpcReq;
import cn.abcyun.bis.rpc.sdk.cis.model.ec.EcAuthRsp;
import cn.abcyun.cis.clinic.api.view.ClinicEcAuthReq;
import cn.abcyun.cis.clinic.service.hystrix.EcHystrixService;
import cn.abcyun.cis.core.util.FeignClientRpcTemplate;
import cn.abcyun.region.rpc.sdk.client.cis.AbcCisEcRegionFeignClient;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @create 2024-04-15 11:06
 * @Description
 */

@Service
public class EcClient extends EcHystrixService {

    @Autowired
    private AbcCisEcRegionFeignClient ecRegionFeignClient;

    @HystrixCommand(fallbackMethod = "ecAuthFallBack")
    public EcAuthRsp ecAuth(String chainId, ClinicEcAuthReq req, String employeeId) {
        EcAuthRpcReq rpcReq = new EcAuthRpcReq();
        rpcReq.setChainId(chainId);
        rpcReq.setEcType(req.getEcType());
        rpcReq.setAuthCode(req.getAuthCode());
        rpcReq.setEmployeeId(employeeId);
        rpcReq.setEcMallId(req.getEcMallId());
        return FeignClientRpcTemplate.dealRpcClientMethod("auth",
                () -> ecRegionFeignClient.auth(chainId, rpcReq), chainId, rpcReq);
    }
}
