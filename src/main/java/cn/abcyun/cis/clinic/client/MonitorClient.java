package cn.abcyun.cis.clinic.client;

import cn.abcyun.bis.rpc.sdk.cis.client.AbcCisMonitorFeignClient;
import cn.abcyun.bis.rpc.sdk.cis.model.monitor.ServiceAlertMessage;
import cn.abcyun.cis.clinic.service.hystrix.MonitorHystrixService;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/7/31 18:05
 */
@Service
public class MonitorClient extends MonitorHystrixService {
    @Autowired
    private AbcCisMonitorFeignClient monitorClient;

    @HystrixCommand(fallbackMethod = "sendNotifyFallback")
    public void sendNotify(String title, String content) {
        ServiceAlertMessage serviceAlertMessage = new ServiceAlertMessage();
        serviceAlertMessage.setServiceName("abc-cis-sc-clinic-service");
        serviceAlertMessage.setTitle(title);
        serviceAlertMessage.setContent(content);
        monitorClient.sendServiceAlertMessage(serviceAlertMessage);
    }
}
