<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.abcyun.cis.clinic.mybatis.mapper.OrganMapper">

    <resultMap id="organMap" type="cn.abcyun.cis.clinic.model.Organ">
        <result column="practice_subject" javaType="com.fasterxml.jackson.databind.JsonNode" property="practiceSubject" typeHandler="cn.abcyun.cis.clinic.mybatis.JacksonTypeHandler"/>
        <result column="config" javaType="com.fasterxml.jackson.databind.JsonNode" property="config" typeHandler="cn.abcyun.cis.clinic.mybatis.JacksonTypeHandler"/>
        <result column="certs" javaType="com.fasterxml.jackson.databind.JsonNode" property="certs" typeHandler="cn.abcyun.cis.clinic.mybatis.JacksonTypeHandler"/>
    </resultMap>

    <select id="findOrganForQW" resultType="cn.abcyun.cis.clinic.api.view.QWOrganAbstract">
        select
        a.id as id,
        a.`name` as name,
        a.node_type as nodeType,
        a.his_type as hisType,
        a.view_mode as viewMode,
        a.`level` as level,
        a.short_name as shortName,
        a.address_province_id as addressProvinceId,
        a.address_province_name as addressProvinceName,
        a.address_city_id as addressCityId,
        a.address_city_name as addressCityName,
        a.address_district_id as addressDistrictId,
        a.address_district_name as addressDistrictName,
        a.address_detail as addressDetail
        from organ as a
        <if test="abcEmployeeId != null and abcEmployeeId != ''">
            left join abc_employee_organ_relation as b on a.id = b.organ_id
        </if>
        <if test="editionStatus != null">
            left join v2_clinic_current_edition as c on a.id = c.clinic_id
        </if>
        where a.status in (1, 70)
        <if test="keyword != null and keyword != ''">
            and (a.name like concat('%', #{keyword}, '%') or a.short_name like concat('%', #{keyword}, '%'))
        </if>
        <if test="abcEmployeeId != null and abcEmployeeId != ''">
            and b.abc_employee_id = #{abcEmployeeId}
        </if>
        <if test="nodeType != null">
            <choose>
                <when test="nodeType == 20">
                    and a.node_type IN (0, 2)
                </when>
                <otherwise>
                    and a.node_type = #{nodeType}
                </otherwise>
            </choose>
        </if>
        <if test="editionStatus != null">
            <choose>
                <when test="editionStatus == 1">
                    and c.id is not null
                </when>
                <when test="editionStatus == 2">
                    and c.id is null
                </when>
                <otherwise></otherwise>
            </choose>
        </if>
        order by a.created_date desc
        limit #{offset}, #{limit}
    </select>


    <select id="findOrganForQWCount" resultType="java.lang.Integer">
        select
        count(a.id)
        from organ as a
        <if test="abcEmployeeId != null and abcEmployeeId != ''">
            left join abc_employee_organ_relation as b on a.id = b.organ_id
        </if>
        <if test="editionStatus != null">
            left join v2_clinic_current_edition as c on a.id = c.clinic_id
        </if>
        where a.status in (1, 70)
        <if test="keyword != null and keyword != ''">
            and (a.name like concat('%', #{keyword}, '%') or a.short_name like concat('%', #{keyword}, '%'))
        </if>
        <if test="abcEmployeeId != null and abcEmployeeId != ''">
            and b.abc_employee_id = #{abcEmployeeId}
        </if>
        <if test="nodeType != null">
            <choose>
                <when test="nodeType == 20">
                    and a.node_type IN (0, 2)
                </when>
                <otherwise>
                    and a.node_type = #{nodeType}
                </otherwise>
            </choose>
        </if>
        <if test="editionStatus != null">
            <choose>
                <when test="editionStatus == 1">
                    and c.id is not null
                </when>
                <when test="editionStatus == 2">
                    and c.id is null
                </when>
                <otherwise></otherwise>
            </choose>
        </if>
    </select>


    <select id="findOrganForOperation" resultType="cn.abcyun.cis.clinic.api.view.OperationOrganAbstract">
        select a.id as id,
        a.short_id as shortId,
        a.name as name,
        a.node_type as nodeType,
        a.view_mode as viewMode,
        a.his_type as hisType,
        a.short_name as shortName,
        b.name as chainName,
        b.id as chainId,
        a.address_province_id as addressProvinceId,
        a.address_province_name as addressProvinceName,
        a.address_city_id as addressCityId,
        a.address_city_name as addressCityName,
        a.address_district_id as addressDistrictId,
        a.address_district_name as addressDistrictName,
        a.address_detail as addressDetail,
        a.inner_flag as innerFlag,
        c.edition_id as editionId,
        a.created_date as createdDate,
        c.begin_date as editionBeginDate,
        c.end_date as editionEndDate,
        d.begin_date as trialBeginDate
        from organ as a
        left join organ as b on a.parent_id = b.id and a.node_type = 2
        left join v2_clinic_current_edition as c on c.clinic_id = a.id and c.is_deleted = 0
        left join v2_clinic_edition_order as d on d.bind_clinic_id = a.id and d.is_deleted = 0 and d.type = 0
        where a.status = 1
        <if test="hisType != null">
            and a.his_type = #{hisType}
        </if>
        <if test="keyword != null and keyword != ''">
            <choose>
                <when test="clinicIds !=null and clinicIds.size() > 0">
                    and a.id in
                    <foreach collection="clinicIds" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    and (a.id = #{keyword} or (a.name like concat('%', #{keyword}, '%') or a.short_name like concat('%', #{keyword}, '%')))
                </otherwise>
            </choose>
        </if>
        <if test="editionStatus != null">
            <choose>
                <when test="editionStatus == 0">
                    and c.id is null
                </when>
                <when test="editionStatus == 10">
                    and c.id is not null and c.begin_date &gt; now()
                </when>
                <when test="editionStatus == 20">
                    and c.id is not null and c.begin_date &lt; now() and c.end_date &gt; now()
                </when>
                <when test="editionStatus == 30">
                    and c.id is not null and c.end_date &lt; now()
                </when>
                <otherwise></otherwise>
            </choose>
        </if>
        <if test="editionId != null and editionId != ''">
            and c.edition_id = #{editionId}
        </if>
        <if test="addressId != null and addressId != ''">
            and (a.address_province_id = #{addressId} or a.address_city_id = #{addressId} or a.address_district_id =
            #{addressId})
        </if>
        <if test="nodeType != null">
            <choose>
                <when test="nodeType == 1">
                    and a.node_type = 1 and a.view_mode = 0
                </when>
                <otherwise>
                    and a.node_type = #{nodeType}
                </otherwise>
            </choose>
        </if>
        <if test="nodeTypeFilter != null">
            <choose>
                <when test="nodeTypeFilter == 1">
                    and a.view_mode = 0
                </when>
                <when test="nodeTypeFilter == 2">
                    and a.view_mode = 1
                </when>
                <when test="nodeTypeFilter == 4">
                    and a.node_type = 1
                </when>
                <when test="nodeTypeFilter == 5">
                    and a.node_type = 1 and a.view_mode = 0
                </when>
                <when test="nodeTypeFilter == 6">
                    and a.node_type = 1 and a.view_mode = 1
                </when>
                <when test="nodeTypeFilter == 8">
                    and a.node_type = 2
                </when>
                <when test="nodeTypeFilter == 9">
                    and a.node_type = 2 and a.view_mode = 0
                </when>
                <when test="nodeTypeFilter == 10">
                    and a.node_type = 2 and a.view_mode = 1
                </when>
                <otherwise></otherwise>
            </choose>
        </if>
        <if test="source != null and source != ''">
            and a.source = #{source}
        </if>
        <if test="isTrial != null">
            <choose>
                <when test="isTrial == 1">
                    and c.is_trial = 1
                </when>
                <otherwise>
                    and (c.is_trial = 0 or c.is_trial is null)
                </otherwise>
            </choose>
        </if>
        order by a.created_date desc
        limit #{offset}, #{limit}
    </select>

    <select id="findOrganForOperationCount" resultType="java.lang.Integer">
        select
        count(a.id)
        from organ as a
        left join v2_clinic_current_edition as c on c.clinic_id = a.id and c.is_deleted = 0
        where a.status = 1
        <if test="hisType != null">
            and a.his_type = #{hisType}
        </if>
        <if test="keyword != null and keyword != ''">
            <choose>
                <when test="clinicIds !=null and clinicIds.size() > 0">
                    and a.id in
                    <foreach collection="clinicIds" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </when>
                <otherwise>
                    and (a.id = #{keyword} or (a.name like concat('%', #{keyword}, '%') or a.short_name like concat('%', #{keyword}, '%')))
                </otherwise>
            </choose>
        </if>
        <if test="editionStatus != null">
            <choose>
                <when test="editionStatus == 0">
                    and c.id is null
                </when>
                <when test="editionStatus == 10">
                    and c.id is not null and c.begin_date &gt; now()
                </when>
                <when test="editionStatus == 20">
                    and c.id is not null and c.begin_date &lt; now() and c.end_date &gt; now()
                </when>
                <when test="editionStatus == 30">
                    and c.id is not null and c.end_date &lt; now()
                </when>
                <otherwise></otherwise>
            </choose>
        </if>
        <if test="editionId != null and editionId != ''">
            and c.edition_id = #{editionId}
        </if>
        <if test="addressId != null and addressId != ''">
            and (a.address_province_id = #{addressId} or a.address_city_id = #{addressId} or a.address_district_id =
            #{addressId})
        </if>
        <if test="nodeType != null">
            <choose>
                <when test="nodeType == 1">
                    and a.node_type = 1 and a.view_mode = 0
                </when>
                <otherwise>
                    and a.node_type = #{nodeType}
                </otherwise>
            </choose>
        </if>
        <if test="nodeTypeFilter != null">
            <choose>
                <when test="nodeTypeFilter == 1">
                    and a.view_mode = 0
                </when>
                <when test="nodeTypeFilter == 2">
                    and a.view_mode = 1
                </when>
                <when test="nodeTypeFilter == 4">
                    and a.node_type = 1
                </when>
                <when test="nodeTypeFilter == 5">
                    and a.node_type = 1 and a.view_mode = 0
                </when>
                <when test="nodeTypeFilter == 6">
                    and a.node_type = 1 and a.view_mode = 1
                </when>
                <when test="nodeTypeFilter == 8">
                    and a.node_type = 2
                </when>
                <when test="nodeTypeFilter == 9">
                    and a.node_type = 2 and a.view_mode = 0
                </when>
                <when test="nodeTypeFilter == 10">
                    and a.node_type = 2 and a.view_mode = 1
                </when>
                <otherwise></otherwise>
            </choose>
        </if>
        <if test="source != null and source != ''">
            and a.source = #{source}
        </if>
        <if test="isTrial != null">
            <choose>
                <when test="isTrial == 1">
                    and c.is_trial = 1
                </when>
                <otherwise>
                    and (c.is_trial = 0 or c.is_trial is null)
                </otherwise>
            </choose>
        </if>
    </select>

    <select id="findOpenedAirPharmacyClinics" resultType="cn.abcyun.cis.clinic.api.view.ClinicAirPharmacyOperationView">
        SELECT cap.id,
        chain.id AS chainId,
        chain.name AS chainName,
        clinic.id AS clinicId,
        clinic.name AS clinicName,
        clinic.node_type AS clinicType,
        clinic.address_city_name AS city,
        cap.gross_profit AS grossProfit,
        cap.order_amount AS orderAmount,
        cap.patient_paid AS patientPaid,
        cap.pre_deposit AS preDeposit
        FROM v2_clinic_air_pharmacy AS cap
        LEFT OUTER JOIN organ AS chain ON cap.chain_id = chain.id
        INNER JOIN organ AS clinic
        ON cap.clinic_id = clinic.id AND clinic.node_type IN (0, 2)
        and clinic.status &lt; 90
        <if test="keyword != null and keyword != ''">
            and (clinic.name like concat('%', #{keyword}, '%') or clinic.short_name like concat('%', #{keyword}, '%'))
        </if>
        limit #{offset}, #{limit}
    </select>

    <select id="findOpenedAirPharmacyClinicsCount" resultType="java.lang.Integer">
        SELECT count(cap.id)
        FROM v2_clinic_air_pharmacy AS cap
        LEFT OUTER JOIN organ AS chain ON cap.chain_id = chain.id
        INNER JOIN organ AS clinic
        ON cap.clinic_id = clinic.id AND clinic.node_type IN (0, 2)
        and clinic.status &lt; 90
        <if test="keyword != null and keyword != ''">
            and (clinic.name like concat('%', #{keyword}, '%') or clinic.short_name like concat('%', #{keyword}, '%'))
        </if>
    </select>

    <select id="findMallOrgan" resultType="cn.abcyun.cis.clinic.api.view.MallOrganView">
        select distinct a.id as id,
        a.short_id as shortId,
        a.view_mode as viewMode,
        a.`name` as name,
        b.`name` as parentName,
        b.short_name as parentShortName,
        a.node_type as nodeType,
        a.`level` as level,
        a.short_name as shortName,
        a.address_province_id as addressProvinceId,
        a.address_province_name as addressProvinceName,
        a.address_city_id as addressCityId,
        a.address_city_name as addressCityName,
        a.address_district_id as addressDistrictId,
        a.address_district_name as addressDistrictName,
        a.address_detail as addressDetail
        from organ as a
        left join v2_clinic_current_edition e on e.clinic_id = a.id
        left join organ b on b.id = a.parent_id
        where a.status = 1
        <if test="isFilterExpired == 1">
            and e.end_date > now()
            and e.is_deleted = 0
        </if>
        <if test="hisTypeTags != null and hisTypeTags.size() > 0">
            and a.id in
            (
            select distinct ta.clinic_id from v2_clinic_tag_relation ta
            inner join v2_clinic_tag_relation tb on (ta.clinic_id = tb.clinic_id)
            <where>
                <foreach collection="hisTypeTags" item="hisTypeTag" open="(" separator=" or " close=")">
                    <choose>
                        <when test="hisTypeTag == 1">
                            (ta.tag_key = 'hisType.oral' and tb.tag_key = 'hisType.oral')
                        </when>
                        <when test="hisTypeTag == 2">
                            (ta.tag_key = 'hisType.eye' and tb.tag_key = 'hisType.eye')
                        </when>
                        <when test="hisTypeTag == 3">
                            (ta.tag_key = 'category.cm_wm' and tb.tag_key = 'hisType.common')
                        </when>
                        <when test="hisTypeTag == 4">
                            (ta.tag_key = 'category.cm' and tb.tag_key = 'hisType.common')
                        </when>
                        <when test="hisTypeTag == 5">
                            (ta.tag_key = 'category.wm' and tb.tag_key = 'hisType.common')
                        </when>
                    </choose>
                </foreach>
            </where>
            )
        </if>
        <if test="editionTags != null and editionTags.size() > 0">
            and a.id in (
            select distinct t.clinic_id from v2_clinic_tag_relation t
            <where>
                <foreach collection="editionTags" item="editionTag" open="(" separator=" or " close=")">
                    <choose>
                        <when test="editionTag == 1">
                            (t.tag_key = 'editionLevel.basic')
                        </when>
                        <when test="editionTag == 2">
                            (t.tag_key = 'editionLevel.professional')
                        </when>
                        <when test="editionTag == 3">
                            (t.tag_key = 'editionLevel.ultimate')
                        </when>
                        <when test="editionTag == 4">
                            (t.tag_key = 'editionLevel.vip')
                        </when>
                        <when test="editionTag == 5">
                            (t.tag_key = 'editionLevel.eye-basic')
                        </when>
                        <when test="editionTag == 6">
                            (t.tag_key = 'editionLevel.eye-standard')
                        </when>
                        <when test="editionTag == 7">
                            (t.tag_key = 'editionLevel.eye-professional')
                        </when>
                        <when test="editionTag == 8">
                            (t.tag_key = 'editionLevel.eye-ultimate')
                        </when>
                    </choose>
                </foreach>
            </where>
            )
        </if>
        <if test="organShortIds != null and organShortIds.size() > 0">
            and a.short_id in
            <foreach item="id" collection="organShortIds" index="index" open="(" separator="," close=")">
                #{id,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="excludeOrganShortIds != null and excludeOrganShortIds.size() > 0">
            and a.short_id not in
            <foreach item="id" collection="excludeOrganShortIds" index="index" open="(" separator="," close=")">
                #{id,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="name != null and name != ''">
            and (a.name like concat('%', #{name}, '%') or a.short_name like concat('%', #{name}, '%'))
        </if>
        <if test="keyword != null and keyword != ''">
            and (a.name like concat('%', #{keyword}, '%') or a.short_name like concat('%', #{keyword}, '%') or a.id =
            #{keyword} or a.short_id = #{keyword})
        </if>
        <if test="addressProvinceId != null and addressProvinceId != ''">
            and a.address_province_id = #{addressProvinceId,jdbcType=VARCHAR}
        </if>
        <if test="addressCityId != null and addressCityId != ''">
            and a.address_city_id = #{addressCityId}
        </if>
        <if test="isFilterInner == 1">
            and a.inner_flag = 0
        </if>
        <if test="nodeType != null and nodeType.size() > 0">
            and a.node_type in
            <foreach item="type" collection="nodeType" index="index" open="(" separator="," close=")">
                #{type,jdbcType=INTEGER}
            </foreach>
        </if>
        limit #{offset}, #{limit}
    </select>

    <select id="countMallOrgan" resultType="java.lang.Integer">
        select count(distinct a.id)
        from organ as a
        left join v2_clinic_current_edition e on e.clinic_id = a.id
        where a.status = 1
        <if test="isFilterExpired == 1">
            and e.end_date > now()
            and e.is_deleted = 0
        </if>
        <if test="hisTypeTags != null and hisTypeTags.size() > 0">
            and a.id in
            (
            select distinct ta.clinic_id from v2_clinic_tag_relation ta
            inner join v2_clinic_tag_relation tb on (ta.clinic_id = tb.clinic_id)
            <where>
                <foreach collection="hisTypeTags" item="hisTypeTag" open="(" separator=" or " close=")">
                    <choose>
                        <when test="hisTypeTag == 1">
                            (ta.tag_key = 'hisType.oral' and tb.tag_key = 'hisType.oral')
                        </when>
                        <when test="hisTypeTag == 2">
                            (ta.tag_key = 'hisType.eye' and tb.tag_key = 'hisType.eye')
                        </when>
                        <when test="hisTypeTag == 3">
                            (ta.tag_key = 'category.cm_wm' and tb.tag_key = 'hisType.common')
                        </when>
                        <when test="hisTypeTag == 4">
                            (ta.tag_key = 'category.cm' and tb.tag_key = 'hisType.common')
                        </when>
                        <when test="hisTypeTag == 5">
                            (ta.tag_key = 'category.wm' and tb.tag_key = 'hisType.common')
                        </when>
                    </choose>
                </foreach>
            </where>
            )
        </if>
        <if test="editionTags != null and editionTags.size() > 0">
            and a.id in (
            select distinct t.clinic_id from v2_clinic_tag_relation t
            <where>
                <foreach collection="editionTags" item="editionTag" open="(" separator=" or " close=")">
                    <choose>
                        <when test="editionTag == 1">
                            (t.tag_key = 'editionLevel.basic')
                        </when>
                        <when test="editionTag == 2">
                            (t.tag_key = 'editionLevel.professional')
                        </when>
                        <when test="editionTag == 3">
                            (t.tag_key = 'editionLevel.ultimate')
                        </when>
                        <when test="editionTag == 4">
                            (t.tag_key = 'editionLevel.vip')
                        </when>
                        <when test="editionTag == 5">
                            (t.tag_key = 'editionLevel.eye-basic')
                        </when>
                        <when test="editionTag == 6">
                            (t.tag_key = 'editionLevel.eye-standard')
                        </when>
                        <when test="editionTag == 7">
                            (t.tag_key = 'editionLevel.eye-professional')
                        </when>
                        <when test="editionTag == 8">
                            (t.tag_key = 'editionLevel.eye-ultimate')
                        </when>
                    </choose>
                </foreach>
            </where>
            )
        </if>
        <if test="organShortIds != null and organShortIds.size() > 0">
            and a.short_id in
            <foreach item="id" collection="organShortIds" index="index" open="(" separator="," close=")">
                #{id,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="excludeOrganShortIds != null and excludeOrganShortIds.size() > 0">
            and a.short_id not in
            <foreach item="id" collection="excludeOrganShortIds" index="index" open="(" separator="," close=")">
                #{id,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="name != null and name != ''">
            and (a.name like concat('%', #{name}, '%') or a.short_name like concat('%', #{name}, '%'))
        </if>
        <if test="keyword != null and keyword != ''">
            and (a.name like concat('%', #{keyword}, '%') or a.short_name like concat('%', #{keyword}, '%') or a.id =
            #{keyword} or a.short_id = #{keyword})
        </if>
        <if test="addressProvinceId != null and addressProvinceId != ''">
            and a.address_province_id = #{addressProvinceId,jdbcType=VARCHAR}
        </if>
        <if test="addressCityId != null and addressCityId != ''">
            and a.address_city_id = #{addressCityId}
        </if>
        <if test="isFilterInner == 1">
            and a.inner_flag = 0
        </if>
        <if test="nodeType != null and nodeType.size() > 0">
            and a.node_type in
            <foreach item="type" collection="nodeType" index="index" open="(" separator="," close=")">
                #{type,jdbcType=INTEGER}
            </foreach>
        </if>
    </select>

    <select id="pageListOrganByTag" resultType="cn.abcyun.cis.clinic.api.view.OrganBasicView">
        select a.id,
        a.short_id,
        a.name,
        a.short_name as shortName,
        a.parent_id as parentId,
        a.parent_short_id as parentShortId,
        a.node_type as nodeType,
        a.view_mode as viewMode,
        a.his_type as hisType
        from organ a
        where
        a.status = 1
        <if test="cursor != null and cursor != ''">
            and a.id > #{cursor}
        </if>
        <if test="excludeClinicIdList != null and excludeClinicIdList.size() > 0">
            and a.id not in
            <foreach item="id" collection="excludeClinicIdList" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="excludeClinicShortIdList != null and excludeClinicShortIdList.size() > 0">
            and a.short_id not in
            <foreach item="id" collection="excludeClinicShortIdList" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="areaIds != null and areaIds.size() > 0">
            and (
            a.address_province_id in
            <foreach item="areaId" collection="areaIds" index="index" open="(" separator="," close=")">
                #{areaId}
            </foreach>
            or a.address_city_id in
            <foreach item="areaId" collection="areaIds" index="index" open="(" separator="," close=")">
                #{areaId}
            </foreach>
            )
        </if>
        <if test="groupList != null and groupList.size() > 0">
            <foreach item="group" collection="groupList" index="index" open="" separator=" " close="">
                and exists(
                select b.id
                from v2_clinic_tag_relation b
                where b.clinic_id = a.id
                and b.tag_key in
                <foreach item="tagKey" collection="group.tagKeyList" index="index" open="(" separator="," close=")">
                    #{tagKey}
                </foreach>
                )
            </foreach>
        </if>
        <if test="excludeTagKeyList != null and excludeTagKeyList.size() > 0">
            and a.id not in (
            select distinct c.clinic_id
            from v2_clinic_tag_relation c
            where c.tag_key in
            <foreach item="tagKey" collection="excludeTagKeyList" index="index" open="(" separator="," close=")">
                #{tagKey}
            </foreach>
            )
        </if>
        <if test="organShortIds != null and organShortIds.size() > 0">
            and a.short_id in
            <foreach collection="organShortIds" item="organShortId" open="(" separator="," close=")">
                #{organShortId}
            </foreach>
        </if>
        order by a.id
        limit #{offset}, #{limit}
    </select>

    <select id="countPageListOrganByTag" resultType="java.lang.Integer">
        select count(a.id)
        from organ a
        where
        a.status = 1
        <if test="excludeClinicIdList != null and excludeClinicIdList.size() > 0">
            and a.id not in
            <foreach item="id" collection="excludeClinicIdList" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="excludeClinicShortIdList != null and excludeClinicShortIdList.size() > 0">
            and a.short_id not in
            <foreach item="id" collection="excludeClinicShortIdList" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="areaIds != null and areaIds.size() > 0">
            and (
            a.address_province_id in
            <foreach item="areaId" collection="areaIds" index="index" open="(" separator="," close=")">
                #{areaId}
            </foreach>
            or a.address_city_id in
            <foreach item="areaId" collection="areaIds" index="index" open="(" separator="," close=")">
                #{areaId}
            </foreach>
            )
        </if>
        <if test="groupList != null and groupList.size() > 0">
            <foreach item="group" collection="groupList" index="index" open="" separator=" " close="">
                and exists(
                select b.id
                from v2_clinic_tag_relation b
                where b.clinic_id = a.id
                and b.tag_key in
                <foreach item="tagKey" collection="group.tagKeyList" index="index" open="(" separator="," close=")">
                    #{tagKey}
                </foreach>
                )
            </foreach>
        </if>
        <if test="excludeTagKeyList != null and excludeTagKeyList.size() > 0">
            and a.id not in (
            select distinct c.clinic_id
            from v2_clinic_tag_relation c
            where c.tag_key in
            <foreach item="tagKey" collection="excludeTagKeyList" index="index" open="(" separator="," close=")">
                #{tagKey}
            </foreach>
            )
        </if>
        <if test="organShortIds != null and organShortIds.size() > 0">
            and a.short_id in
            <foreach collection="organShortIds" item="organShortId" open="(" separator="," close=")">
                #{organShortId}
            </foreach>
        </if>
    </select>


    <select id="pageFindGrayOrganListByRegion" resultType="cn.abcyun.cis.clinic.api.view.GrayOrganView">
        select a.chain_id as chainId,
        a.env as env
        from v2_clinic_gray_organ a
        left join v2_clinic_region_organ b on a.chain_id = b.chain_id
        where
        b.region_id = #{regionId}
        and a.env in (1,2)
        <if test="offset != null and limit != null">
            limit #{offset}, #{limit}
        </if>
    </select>

    <select id="countGrayOrganListByRegion" resultType="java.lang.Integer">
        select count(a.id)
        from v2_clinic_gray_organ a
        where a.region_id = #{regionId}
          and a.env in (1, 2)
    </select>
    <select id="countEnvRegionCount" resultType="java.lang.Integer">
        select count(a.id)
        from v2_clinic_gray_organ a
        where  a.env = #{env}
          and ( a.remark is null or a.remark not like '%测试%')
        and a.zone = #{fromZone}
    </select>
    <!-- 获取分区连锁的数量 -->
    <select id="getRegionChainCount" resultType="cn.abcyun.bis.rpc.sdk.cis.model.clinic.GetRegionEnvZoneCountRsp">
        select  a.env as env, a.zone as zone, count(a.id) as chainCount
        from v2_clinic_gray_organ a
            where ( a.remark is null or a.remark not like '%测试%')
        group by  a.env, a.zone
    </select>
    <!-- 获取分区任务需要回退连锁的列表 -->
    <select id="getRollBackChainIdList" resultType="String">
        select distinct a.chain_id as chainId
        from v2_clinic_gray_organ a
        where a.zone_task_id = #{zoneTaskId}
          and a.zone = #{toZone}
          and a.env = #{env}
    </select>
    <update id="rollBackZoneTaskId">
        update v2_clinic_gray_organ a
        set zone =#{fromZone},
        last_modified = now()
        where a.region_id = #{regionId}
           and a.zone_task_id = #{zoneTaskId}
           and a.zone = #{toZone}
    </update>
    <update id="cleanGrayOrganZoneTaskId">
        update v2_clinic_gray_organ a
        set zone_task_id = null,
            last_modified = now()
        where zone_task_id = #{zoneTaskId}
    </update>
    <update id="rollBackZoneTaskIdByChainIds">
        update v2_clinic_gray_organ a
        set a.zone = #{fromZone},
            a.zone_task_id = null,
            a.zone_detail_id = null,
            a.last_modified = now()
        where a.region_id = #{regionId}
          and a.zone_task_id = #{zoneTaskId}
          and a.zone = #{toZone}
          and a.env = #{env}
          and a.chain_id in
          <foreach collection="chainIds" item="chainId" open="(" separator="," close=")">
              #{chainId}
          </foreach>
    </update>
    <update id="updateEnvRegionZoneLimitCount">
        update v2_clinic_gray_organ a
        set last_modified = now(),
        zone_task_id = #{zoneTaskId},
        zone_detail_id = #{zoneDetailId}
        where
        a.env = #{env}
        <if test="normalChainOnly == 1">
          <!-- 没加备注的是普通门店 -->
          and  a.remark is null
        </if>
        <if test="normalChainOnly == 0">
            <!-- 没加备注的是普通门店 || 加了备注的 大店 -->
            and  ( a.remark is null or a.remark not like '%测试%')
        </if>
          and a.zone = #{fromZone}
        <if test="regionId != -1">
          and a.region_id = #{regionId}
        </if>
          and zone_task_id is null
          <if test="limitCount != null">
                limit #{limitCount}
          </if>
    </update>


    <select id="findExpiredOrganList" resultType="java.lang.String">
        SELECT DISTINCT parent_id
            FROM organ o
            <if test="regionId != null and regionId != '' ">
                INNER JOIN v2_clinic_region_organ ro ON o.parent_id = ro.chain_id AND ro.region_id = #{regionId}
            </if>
        WHERE NOT EXISTS(
            SELECT 1
                FROM organ o2
            where o2.status = 1
                and o.parent_id = o2.parent_id
        ) or NOT EXISTS(
                SELECT 1
                    FROM v2_clinic_current_edition b
                WHERE b.is_deleted = 0
                    AND b.end_date > now()
                    AND b.chain_id = o.parent_id
            )
    </select>

    <select id="findOrgansByArea" resultMap="organMap">
        select a.*
        from organ a
        <if test="regionId != null and regionId != '' ">
            inner join v2_clinic_region_organ b on a.parent_id = b.chain_id and b.region_id = #{regionId}
        </if>
        where a.status = #{status}
          and a.supervision_flag = #{supervisionFlag}
        <if test="addressProvinceIds != null and addressProvinceIds.size() > 0">
            and a.address_province_id in
            <foreach collection="addressProvinceIds" item="addressProvinceId" open="(" separator="," close=")">
                #{addressProvinceId}
            </foreach>
        </if>
        <if test="addressCityIds != null and addressCityIds.size() > 0">
            and a.address_city_id in
            <foreach collection="addressCityIds" item="addressCityId" open="(" separator="," close=")">
                #{addressCityId}
            </foreach>
        </if>
        <if test="addressDistrictIds != null and addressDistrictIds.size() > 0">
            and a.address_district_id in
            <foreach collection="addressDistrictIds" item="addressDistrictId" open="(" separator="," close=")">
                #{addressDistrictId}
            </foreach>
        </if>
    </select>

    <select id="queryOrganByRegion" resultMap="organMap">
        select a.*
        from organ a
        <if test="regionIds != null and regionIds.size() > 0">
            inner join v2_clinic_region_organ b on a.parent_id = b.chain_id
        </if>
        <where>
            <if test="regionIds != null and regionIds.size() != 0">
                b.region_id in
                <foreach collection="regionIds" item="regionId" open="(" separator="," close=")">
                    #{regionId}
                </foreach>
            </if>
            <if test="nodeTypes != null and nodeTypes.size() != 0">
                and a.node_type in
                <foreach collection="nodeTypes" item="nodeType" open="(" separator="," close=")">
                    #{nodeType}
                </foreach>
            </if>
            <if test="status != null">
                and a.status = #{status}
            </if>
        </where>
        order by a.created_date desc
        limit #{offset}, #{limit}
    </select>

    <select id="queryOrganCountByRegion" resultType="int">
        select count(a.id)
        from organ a
        <if test="regionIds != null and regionIds.size() > 0">
            inner join v2_clinic_region_organ b on a.parent_id = b.chain_id
        </if>
        <where>
            <if test="regionIds != null and regionIds.size() != 0">
                b.region_id in
                <foreach collection="regionIds" item="regionId" open="(" separator="," close=")">
                    #{regionId}
                </foreach>
            </if>
            <if test="nodeTypes != null and nodeTypes.size() != 0">
                and a.node_type in
                <foreach collection="nodeTypes" item="nodeType" open="(" separator="," close=")">
                    #{nodeType}
                </foreach>
            </if>
            <if test="status != null">
                and a.status = #{status}
            </if>
        </where>
    </select>

    <select id="countExpireClinic" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM (
        SELECT asorf.clinic_id
        FROM abc_seller_organ_relation_fixed asorf
        INNER JOIN v2_clinic_current_edition cce ON cce.is_deleted = 0 AND cce.is_trial = 0 AND cce.clinic_id =
        asorf.clinic_id
        <where>
            <if test="sellerIds != null and sellerIds.size() > 0">
                AND asorf.seller_id in
                <foreach collection="sellerIds" item="sellerId" open="(" separator="," close=")">
                    #{sellerId}
                </foreach>
            </if>
            <if test="comingExpireMonth == 0">
                AND cce.end_date &lt; NOW()
            </if>
            <if test="comingExpireMonth > 0">
                AND cce.end_date &lt;= #{comingExpireDate}
                AND cce.end_date &gt;= NOW()
            </if>
        </where>
        GROUP BY asorf.clinic_id
        ) tmp;

    </select>
    <select id="listExpireClinic" resultType="java.lang.String">
        SELECT asorf.clinic_id
        FROM abc_seller_organ_relation_fixed asorf
        INNER JOIN v2_clinic_current_edition cce ON cce.is_deleted = 0 AND cce.is_trial = 0 AND cce.clinic_id =
        asorf.clinic_id
        <where>
            <if test="sellerIds != null and sellerIds.size() > 0">
                AND asorf.seller_id in
                <foreach collection="sellerIds" item="sellerId" open="(" separator="," close=")">
                    #{sellerId}
                </foreach>
            </if>
            <if test="comingExpireMonth == 0">
                AND cce.end_date &lt; NOW()
            </if>
            <if test="comingExpireMonth > 0">
                AND cce.end_date &lt;= #{comingExpireDate}
                AND cce.end_date &gt;= NOW()
            </if>
        </where>
        GROUP BY asorf.clinic_id, cce.end_date
        ORDER BY cce.end_date
        LIMIT #{offset}, #{limit}
    </select>

    <select id="listClinicIds" resultType="java.lang.String">
        select id
            from organ where parent_id in
        <foreach collection="chainIds" item="chainId" index="index" open="(" separator="," close=")">
            #{chainId}
        </foreach>
         and status = 1
    </select>

    <select id="findOrganWithEdition" resultMap="organMap">
        select a.*
        from organ a
#                  inner join v2_clinic_current_edition b on a.id = b.clinic_id and b.is_deleted = 0 and b.is_trial = 0
        where a.status = 1
          and ((a.node_type = 2 and a.view_mode = 1) or (a.view_mode = 0))
        <if test="clinicIds != null and  clinicIds.size() > 0">
            and a.id in
            <foreach collection="clinicIds" item="clinicId" index="index" open="(" separator="," close=")">
                #{clinicId}
            </foreach>
        </if>
        limit #{offset}, #{limit}
    </select>

    <select id="countOrganWithEdition" resultType="java.lang.Integer">
        select count(a.id)
        from organ a
#                  inner join v2_clinic_current_edition b on a.id = b.clinic_id and b.is_deleted = 0 and b.is_trial = 0
        where a.status = 1
        and ((a.node_type = 2 and a.view_mode = 1) or (a.view_mode = 0))
        <if test="clinicIds != null and  clinicIds.size() > 0">
            and a.id in
            <foreach collection="clinicIds" item="clinicId" index="index" open="(" separator="," close=")">
                #{clinicId}
            </foreach>
        </if>
    </select>
</mapper>
