package cn.abcyun.cis.clinic;

import cn.abcyun.bis.rpc.sdk.cis.message.clinic.ClinicEditionMessage;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.ClinicCurrentEditionView;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Organ;
import cn.abcyun.bis.rpc.sdk.cis.model.wallet.PayOrderCallbackReq;
import cn.abcyun.bis.rpc.sdk.oa.message.ClinicTicketFinishMessage;
import cn.abcyun.bis.rpc.sdk.oa.model.ClinicTicketListView;
import cn.abcyun.bis.rpc.sdk.oa.model.OaConstant;
import cn.abcyun.cis.clinic.amqp.MQProducer;
import cn.abcyun.cis.clinic.api.controller.ClinicRpcController;
import cn.abcyun.cis.clinic.api.view.*;
import cn.abcyun.cis.clinic.api.view.invoice.ClinicInvoiceOrderView;
import cn.abcyun.cis.clinic.api.view.micromart.*;
import cn.abcyun.cis.clinic.api.view.purchase.IndependentPurchaseItemConfigBatchReq;
import cn.abcyun.cis.clinic.api.view.purchase.IndependentPurchaseItemPayOrderView;
import cn.abcyun.cis.clinic.event.ClinicEditionOrderPaidEvent;
import cn.abcyun.cis.clinic.model.ClinicEditionOrder;
import cn.abcyun.cis.clinic.repository.ClinicEditionOrderRepository;
import cn.abcyun.cis.clinic.repository.micromart.MicroMartOrganRepository;
import cn.abcyun.cis.clinic.service.*;
import cn.abcyun.cis.clinic.service.purchase.IndependentPurchaseItemFacadeService;
import cn.abcyun.cis.commons.amqp.message.OrganMessage;
import cn.abcyun.cis.commons.util.JsonUtils;
import cn.abcyun.cis.idgenerator.AbcIdGenerator;
import cn.abcyun.common.model.AbcListPage;
import cn.abcyun.common.model.AbcServiceResponse;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

@SpringBootTest
@ActiveProfiles("local")
@Transactional
class ClinicApplicationTests {
    @Autowired
    private ClinicEditionOrderRepository clinicEditionOrderRepository;
    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;
    @Autowired
    private ClinicEditionService clinicEditionService;
    @Autowired
    private MQProducer mqProducer;
    @Autowired
    private EmployeeService employeeService;
    @Autowired
    private ScheduleService scheduleService;
    @Autowired
    private ClinicRpcController clinicRpcController;
    @Autowired
    private OrganService organService;
    @Autowired
    private ClinicService clinicService;

    @Autowired
    private MicroMartService microMartService;

    @Autowired
    private MicroMartOrganRepository repository;

    @Autowired
    private AbcIdGenerator idGenerator;

    @Autowired
    private ClinicInvoiceService clinicInvoiceService;

    @Autowired
    private IndependentPurchaseItemFacadeService independentPurchaseItemFacadeService;

    @Test
    public void testClinicEditionOrderPaidEvent() throws InterruptedException {
        ClinicEditionOrder clinicEditionOrder = clinicEditionOrderRepository.findById("3788353540611162112").orElse(null);
        applicationEventPublisher.publishEvent(new ClinicEditionOrderPaidEvent(clinicEditionOrder));
        Thread.sleep(1000000);
    }

    @Test
    public void testGetClinicEditionCenter() {
        ClinicEditionOrder clinicEditionOrder = clinicEditionOrderRepository.findById("3788353540611162112").orElse(null);
        if (clinicEditionOrder == null) {
            return;
        }
        ClinicEditionCenterView rsp = clinicEditionService.getClinicEditionCenter(clinicEditionOrder.getBindChainId());
        System.out.println(rsp);
    }

    @Test
    public void testClearClinicChainEditionCache() {
        ClinicEditionOrder clinicEditionOrder = clinicEditionOrderRepository.findById("3788353540611162112").orElse(null);
        if (clinicEditionOrder == null) {
            return;
        }
        clinicEditionService.clearClinicChainEditionCache(clinicEditionOrder.getBindChainId());
    }

    @Test
    public void testSendClearOrganCacheMessage() {
        OrganMessage organMessage = new OrganMessage();
        organMessage.setType(1);
        organMessage.setClinicId("1");
        mqProducer.sendClearOrganCacheMessage(organMessage);
    }

    @Test
    public void testSendClinicEditionActivityObtainedMessage() throws InterruptedException {
        mqProducer.sendClinicEditionOrderTimeoutMessage("3789930405645647872", "ffffffff00000000146b11580c614000", 20);
    }

    @Test
    public void testSendEditionUpdateMessage() {
        ClinicEditionMessage message = new ClinicEditionMessage();
        message.setChainId("ffffffff00000000146b11580c614000");
        message.setClinicId("ffffffff00000000146b11580c614000");
        message.setType(1);
        message.setChainEditionAvailableBefore(false);
        message.setClinicEditionAvailableBefore(false);
        message.setClinicCurrentEdition(new ClinicCurrentEditionView());
        message.setChainCurrentEdition(new ClinicCurrentEditionView());
        Organ organ = new Organ();
        organ.setName("haha");
        organ.setParentId("1");
        organ.setId("1");
        message.setOrgan(organ);
        ClinicEditionMessage.ReferralInfo referralInfo = new ClinicEditionMessage.ReferralInfo();
        referralInfo.setReferrerCode("000008");
        message.setReferralInfo(referralInfo);
        message.setPaidTime(Instant.now());
        mqProducer.sendEditionUpdateMessage(message);
    }


    @Test
    public void countClinicEmployeeByEmployeeId()  {
        int count = employeeService.countClinicEmployeeByEmployeeId("a69cf447a64d457295efe4fd6c6eaae7", "50942bdd59a54d21b0309ef4131b8c38");
        System.out.println(count);
    }

    @Test
    public void cronSendClinicEditionExpiredNotifyMessage() {
        scheduleService.cronSendClinicEditionExpiredNotifyMessage();
    }

    @Test
    @Rollback(value = false)
    public void upsertGrayOrgan () {
        UpsertGrayOrganReq req = new UpsertGrayOrganReq();
        req.setChainId("6a869c22abee4ffbaef3e527bbb70aeb");
        req.setEnv(1);
        clinicRpcController.upsertGrayOrgan(req);
    }

    @Test
    public void pageFindGrayOrganListByRegion() {
        QueryGrayOrganByRegionRsp res1 = organService.getGrayOrganListByRegion(0, 10, "1");
        QueryGrayOrganByRegionRsp res2 = organService.getGrayOrganListByRegion(null, null, "1");
        System.out.println(res1);
        System.out.println(res2);
    }

    @Test
    public void getSalePerformance() {
        AbcServiceResponse<QuerySalePerformanceRsp> salePerformance = clinicRpcController.getSalePerformance("2023-05-22", "2024-05-23");
        System.out.println(salePerformance);
    }

    @Test
    public void handleClinicTicketFinishMessage() {
        ClinicTicketFinishMessage message = new ClinicTicketFinishMessage();
        message.setTitle("营收统计看不到单独的退款金额，希望可以增加对退款金额的统计和查看");
        message.setCode("B123787778837");
        message.setType(OaConstant.ClinicTicket.Type.BUG);
        message.setClinicId("fff730ccc5ee45d783d82a85b8a0e52d");
        message.setEmployeeId("ffffffff00000000146b11580c614000");
        message.setStatus(OaConstant.ClinicTicket.Status.SOLVED);
        message.setLastModified(Instant.now());
        employeeService.handleClinicTicketFinishMessage(message);
    }

    @Test
    public void testClinicTicketByClinicId() {
        AbcListPage<ClinicTicketListView> page =
                clinicService.listClinicTicketByClinicId("ffffffff00000000346e00c217164004", 0, 10);

        System.out.println(page);
    }

    @Test
    public void getBuyView() {
        BuyMicroMartOrganView view = microMartService.getBuyMartOrganView("6a869c22abee4ffbaef3e527bbb70aeb", "6a869c22abee4ffbaef3e527bbb70aeb");
        System.out.println(view);
    }

    @Test
    public void getRenewView() {
        RenewMicroMartOrganView renewMartOrganView = microMartService.getRenewMartOrganView("6a869c22abee4ffbaef3e527bbb70aeb", "6a869c22abee4ffbaef3e527bbb70aeb");
        System.out.println(renewMartOrganView);
    }

    @Test
    public void testCalcuate() {
        CalculateMicroMartOrganOrderFeeReq req = new CalculateMicroMartOrganOrderFeeReq();
        req.setBuyMethod(0);
        req.setBuyServiceOrganNum(1);
        req.setBuyCloudOrganNum(1);
        req.setYears(2);
        CalculateMicroMartOrganOrderFeeView calculate = microMartService.calculate("6a869c22abee4ffbaef3e527bbb70aeb", "6a869c22abee4ffbaef3e527bbb70aeb", req);
        System.out.println(calculate);
    }

    @Test
    public void testOrganPaidActivate() {
        String chainId = "6a869c22abee4ffbaef3e527bbb70aeb";
        String clinicId = "6a869c22abee4ffbaef3e527bbb70aeb";
        String operationId = "ffffffff000000000600eaa002706000";
        MicroMartOrganPaidActivateReq req = new MicroMartOrganPaidActivateReq();
        req.setBuyMethod(0);
        req.setBuyCloudOrganNum(1);
        req.setBuyServiceOrganNum(3);
        req.setYears(1);
        req.setPayAmount(new BigDecimal("7800"));
        req.setBuyOrderTime(LocalDateTime.now());
        req.setChainId(chainId);
        req.setClinicId(clinicId);
        req.setEmployeeId(operationId);

        IndependentPurchaseItemPayOrderView independentPurchaseItemPayOrderView = microMartService.organPaidActivate(req);
        System.out.println(independentPurchaseItemPayOrderView);
    }

    @Test
    public void testScheduleTask() {
        PayOrderCallbackReq req = new PayOrderCallbackReq();
        req.setId("3807696416905068544");
        req.setAccountId("********");
        req.setTotalFee(new BigDecimal("4999"));
        req.setStatus(10);
        req.setPayMode(1);
        req.setPayTime(Instant.now());
        PayOrderCallbackReq.PayOrderItem item = new PayOrderCallbackReq.PayOrderItem();
        item.setId("3807696416905068545");
        item.setBusinessId("3807696417064796160");
        item.setFee(new BigDecimal("4999"));
        req.setItems(Collections.singletonList(item));
        microMartService.purchaseWalletPayCallback(req);
    }

    @Test
    public void testNotifySms() {
        microMartService.clearExpireChainClinicScheduledTask();
    }

    @Test
    public void getAllMicroList() {
        MicroMartOrganView microMartOrganView = microMartService.listMicroMartOrganByChainId("ffffffff0000000010602b7009ed4000");
        System.out.println(microMartOrganView);
    }

    private void doAfterTransactionCommit(Runnable runnable) {
        // 需要等待事务结束后才通知消息
        // 这是实现方式之一
        // 还可以使用TransactionalEventListener，参考文章https://plu.one/%E5%90%8E%E7%AB%AF%E5%BC%80%E5%8F%91/2018/06/04/spring-transactional-event-listener/
        if (TransactionSynchronizationManager.isActualTransactionActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    runnable.run();
                }
            });
        } else {
            runnable.run();
        }
    }

    @Test
    public void getModuleList() {
        employeeService.getEmployeeComposeInfoById("ffffffff00000000146808c695534000", "ffffffff00000000146808c695534000", false, "6e45706922a74966ab51e4ed1e604641", true,true);
    }

    @Test
    @Rollback(value = false)
    public void cronCheckDowngradeClinicEdition() {
        clinicEditionService.cronCheckDowngradeClinicEdition();
    }

    @Test
    public void testPayOrder() {
        PurchaseItemPayOrderView view = independentPurchaseItemFacadeService.getWaitingPaidClinicIndependentPurchaseItemOrder("6a869c22abee4ffbaef3e527bbb70aeb", "b0aef05be62b4ef186bafb694c5bae3a", "invoice-service");
        System.out.println(JsonUtils.dump(view));
    }

    @Test
    public void testEdit() {
        String chainId = "6a869c22abee4ffbaef3e527bbb70aeb";
        String clinicId = "fff730ccc5ee45d783d82a85b8a0e52d";
        int nodeType = 2;
        int hisType = 0;
        IndependentPurchaseItemConfigBatchReq batchReq = new IndependentPurchaseItemConfigBatchReq();
        IndependentPurchaseItemConfigBatchReq.IndependentPurchaseItemConfigReq req = new IndependentPurchaseItemConfigBatchReq.IndependentPurchaseItemConfigReq();
        req.setKey("invoice-service");
        batchReq.setKeyList(Collections.singletonList(req));

        independentPurchaseItemFacadeService.getIndependentPurchaseConfigList(chainId, clinicId, nodeType, hisType, batchReq);
    }

    @Test
    public void testQueryClinicInvoiceApply() {
        String chainId = "6a869c22abee4ffbaef3e527bbb70aeb";
        String clinicId = "fff730ccc5ee45d783d82a85b8a0e52d";
        int viewMode = 0;
        int clinicType = 0;
        List<ClinicInvoiceOrderView> clinicInvoiceOrderViews = clinicInvoiceService.queryClinicInvoiceApply(chainId, clinicId, viewMode, clinicType);
        System.out.println(JsonUtils.dump(clinicInvoiceOrderViews));
    }
}
