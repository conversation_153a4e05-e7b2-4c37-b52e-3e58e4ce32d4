# oa->oaManagement -> rpc scClinic新建/控制灰度任务
#jenkins定时发现任务要执行调用两个分区rpc
alter table v2_clinic_gray_organ
    add zone_task_id bigint unsigned null comment '可用区v2_clinic_zone_task 放量taskid ',
    add zone_detail_id bigint unsigned null comment '可用区 v2_clinic_zone_task_detail详情 taskid ';
create table v2_clinic_zone_task
(
    id        bigint unsigned primary key,
    name      varchar(64)                           null,
    region_id       int         default 0                 not null comment '分区 1 上海 2 杭州',
    env       int         default 0                 not null comment '环境（0 正式 1 灰度，2 预发布）',
    from_zone varchar(64) default 'primary'         not null comment '环境对应的可用区',
    to_zone   varchar(64) default 'primary'         not null comment '环境对应的可用区',
    status    int         default 0                 not null comment '0 新建 10 生效 20 完成 30 手动结束',
    finished  timestamp                             null comment '完成时间',
    last_modified  timestamp                        null comment '最近修改时间',
    created   timestamp   default current_timestamp not null comment '创建时间',
    created_by   varchar(32)     null comment '操作人'
) comment '通过oa新建，管理任务，任务的执行通过jenkins定时轮训，调用两个分区的clinic的rpc';

create table v2_clinic_zone_task_detail
(
    id            bigint unsigned primary key comment '详细任务',
    zone_task_id  bigint unsigned                          null comment '可用区 放量taskid',
    name          varchar(64)                              null,
    region_count DECIMAL(15, 4) default 0.0000            not null comment '灰度放量比例',
    realRegionCount DECIMAL(15, 4)null comment '实际放量门店数量',
    status        int            default 0                 not null comment '0 新建 10 生效 20 完成 30 手动结束',
    count_type    int            default 0                 not null comment ' 1 比例 100% 2数量',
    started       timestamp                                null comment '最早启动时间',
    finished      timestamp                                null comment '完成时间',
    created       timestamp      default current_timestamp not null comment '创建时间'
);
