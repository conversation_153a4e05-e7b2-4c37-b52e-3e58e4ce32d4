INSERT INTO abc_cis_message.v2_message_template (id, template_code, name, event_type, usage_type, content, status,
                                                     chain_id, service_provider, created_by, created, last_modified_by,
                                                     last_modified, param_keys, channel, approve_status, clinic_id,
                                                     use_count, international_template_code)
VALUES (SUBSTR(UUID_SHORT(), 2), '', '商城发货通知(客户经理侧)', 1900001, 1, '客户（${clinicName}）购买的${goodsName}已寄出，预计2天左右到货，请做好对接准备以免客户等待。', 1,
        '00000000000000000000000000000000', 1, 'sys', NOW(), '', NOW(), '["clinicName", "goodsName"]', 0, 3, '00000000000000000000000000000000', 0, null),
       (SUBSTR(UUID_SHORT(), 2), '', '商城收货通知(客户经理侧)', 1900002, 1, '客户（${clinicName}）购买的${goodsName}已到店，请尽快联系客户进行对接。', 1,
        '00000000000000000000000000000000', 1, 'sys', NOW(), '', NOW(), '["clinicName", "goodsName"]', 0, 3, '00000000000000000000000000000000', 0, null),
       (SUBSTR(UUID_SHORT(), 2), '', '商城收货通知(客户侧)', 1900003, 1, '${goodsName}已签收，专属客户经理${sellerName}（${sellerMobile}）会尽快联系您进行设备对接，请留意来电信息。', 1,
        '00000000000000000000000000000000', 1, 'sys', NOW(), '', NOW(), '["goodsName", "sellerName", "sellerMobile"]', 0, 3, '00000000000000000000000000000000', 0, null);
