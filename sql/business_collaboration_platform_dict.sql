-- 业务协同平台字典表
create table v2_business_collaboration_platform_dict
(
    id                     bigint auto_increment comment '主键ID'
        primary key,
    platform_type          int default 0 not null comment '平台类型：0协同平台',
    dict_type              int           not null comment '字典类型：0医疗机构科室代码，1就诊卡类型，2身份证件类别代码表，3性别代码表，4疾病诊断类别代码，5中医疾病诊断类别代码，6药品分类与编码规范，7药物剂型代码表，8药物使用频率代码表，9用药途径代码表，10体检指标代码表，11标本字典，12检验报告单类别，13检查类型字典表，14手术/操作体表部位代码，15入院途径，16入院时情况代码表，17出院情况代码表，18医疗付费方式，19检查部位代码表，20过敏源代码表',
    dict_desc              varchar(128)  null comment '字典类型描述',
    platform_value         varchar(256)  not null comment '平台值',
    platform_value_meaning text          not null comment '平台值含义',
    his_system_value       varchar(256)  not null comment 'HIS（ABC）系统值',
    is_deleted             int default 0 not null comment '是否删除：0未删除，1已删除'
)
    comment '业务协同平台字典表';

-- 创建索引
CREATE INDEX idx_dict_type ON v2_business_collaboration_platform_dict (platform_type, dict_type);
