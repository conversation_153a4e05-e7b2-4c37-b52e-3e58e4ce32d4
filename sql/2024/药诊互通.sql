alter table v2_clinic_current_edition
    add column max_cooperation_clinic int null comment '仅药店用：最大合作诊所数量';

alter table v2_clinic_edition_order
    add column max_cooperation_clinic int null comment '仅药店用：最大合作诊所数量';

create table v2_clinic_cooperation_clinic
(
    id                    bigint                             not null primary key,
    chain_id              varchar(32)                        not null comment '药店连锁id',
    clinic_id             varchar(32)                        not null comment '药店门店id',
    cooperation_chain_id  varchar(32)                        not null comment '合作连锁id',
    cooperation_clinic_id varchar(32)                        not null comment '合作诊所id',
    status                int                                not null default 0 comment '0正常 99已删除',
    created_by            varchar(32)                        not null comment '创建人',
    created               datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    last_modified_by      varchar(32)                        not null comment '最后修改人',
    last_modified         datetime default CURRENT_TIMESTAMP not null comment '最后修改时间',
    index idx_chain_clinic_status (chain_id, clinic_id, status),
    index idx_co_chain_clinic_status (cooperation_chain_id, cooperation_clinic_id, status)
) comment '药店合作诊所';

create table v2_clinic_cooperation_clinic_order
(
    id                   bigint                               not null primary key,
    chain_id             varchar(32)                          not null comment '连锁id',
    clinic_id            varchar(32)                          not null comment '门店id',
    edition_pay_order_id bigint                               null comment '版本支付订单id',
    type                 int                                  not null comment '订单类型（1: 增购；2: 续费；）',
    begin_date           datetime   default CURRENT_TIMESTAMP not null comment '生效开始时间',
    end_date             datetime   default CURRENT_TIMESTAMP not null comment '生效结束时间',
    unit                 varchar(32)                          not null comment '单位（year）',
    unit_price           decimal(15, 2)                       not null comment '单位定价',
    count                int                                  not null comment '人数',
    years                int                                  not null comment '年数',
    total_price          decimal(15, 2)                       not null comment '原价总价',
    paid_fee             decimal(15, 2)                       null comment '支付费用',
    status               int        default 0                 not null comment '状态(0: 未支付；10: 已支付)',
    is_deleted           tinyint(1) default 0                 not null comment '是否删除',
    created              datetime   default CURRENT_TIMESTAMP not null comment '创建时间',
    created_by           varchar(32)                          not null comment '创建人',
    last_modified_by     varchar(32)                          not null comment '最后修改人',
    last_modified        datetime   default CURRENT_TIMESTAMP not null comment '最后修改时间',
    index idx_chain_clinic (chain_id, clinic_id),
    index ix_edition_pay_order_id (edition_pay_order_id)
)
    comment '药店合作诊所购买订单';


# 刷数据
update
    organ a inner join v2_clinic_current_edition b on a.id = b.clinic_id and b.edition_id = 2020
set b.max_cooperation_clinic = 1
where a.his_type = 10
  and b.max_cooperation_clinic is null;