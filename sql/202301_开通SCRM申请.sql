create table v2_clinic_scrm_open_application
(
    id               bigint                              not null comment '主键ID' primary key,
    chain_id         varchar(32)                         not null comment '连锁ID',
    clinic_id        varchar(32)                         not null comment '门店ID',
    applicant_id     varchar(32)                         not null comment '申请人ID',
    application_time timestamp                           not null comment '申请时间',
    created          timestamp default CURRENT_TIMESTAMP not null comment '创建时间',
    created_by       varchar(32)                         not null comment '创建人',
    last_modified_by varchar(32)                         not null comment '最后修改人',
    last_modified    timestamp default CURRENT_TIMESTAMP not null comment '最后修改时间'
)
    comment 'SCRM 开通申请';

create index ix_clinic_id_applicant_id on v2_clinic_scrm_open_application (clinic_id, applicant_id);