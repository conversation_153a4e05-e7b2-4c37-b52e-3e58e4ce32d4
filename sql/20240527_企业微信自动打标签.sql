
INSERT INTO abc_cis_basic_dev.v2_clinic_tag_type
(id, name, type_key, is_mutex, is_deleted, created)
VALUES(13, '药品推广线索', 'goodsPromotionType', 1, 0, '2023-11-03 10:25:08');

INSERT INTO abc_cis_basic_dev.v2_clinic_tag_define
(id, name, `type`, tag_key, priority, min_re_tag_duration, generate_strategy, generate_rule, match_symbol, min_value, eq_value, max_value, eq_value_datatype, is_value_pass, is_deleted, created)
VALUES(48, '近一日加购', 13, 'goodsPromotionType.addPurchase', 0, NULL, 'BY_SQL', '{"sqlRule": {"targetSql": "select exists(select id from abc_bis_dev.v1_mall_order_shopping_cart where clinic_id = :shortId and is_deleted = 0 and last_modified >= current_date - interval 1 day) as value", "paramNameList": ["shortId"]}}', '=', NULL, '1', NULL, 'String', 0, 0, '2024-03-05 16:10:08');
INSERT INTO abc_cis_basic_dev.v2_clinic_tag_define
(id, name, `type`, tag_key, priority, min_re_tag_duration, generate_strategy, generate_rule, match_symbol, min_value, eq_value, max_value, eq_value_datatype, is_value_pass, is_deleted, created)
VALUES(49, '商城活跃', 9, 'mallOrderCount.moreThanOne', 0, NULL, 'BY_SQL', '{"sqlRule": {"targetSql": "select exists(select m.id from abc_bis_dev.v1_mall_order m inner join abc_bis_dev.v1_mall_order_item n on m.id = n.order_id and n.category_id in (''10004'', ''10001'') where m.clinic_id = :shortId and m.vendor_id = ''10035'' and m.pay_time >= current_date - interval 1 month) as value", "paramNameList": ["shortId"]}}', '=', NULL, '1', NULL, 'Integer', 0, 0, '2024-03-05 16:10:08');
INSERT INTO abc_cis_basic_dev.v2_clinic_tag_define
(id, name, `type`, tag_key, priority, min_re_tag_duration, generate_strategy, generate_rule, match_symbol, min_value, eq_value, max_value, eq_value_datatype, is_value_pass, is_deleted, created)
VALUES(50, '商城流失', 9, 'mallOrderCount.monthEqualZero', 0, NULL, 'BY_SQL', '{"sqlRule": {"targetSql": "select exists(select m.id from abc_bis_dev.v1_mall_order m inner join abc_bis_dev.v1_mall_order_item n on m.id = n.order_id and n.category_id in (''10004'', ''10001'') where m.clinic_id = :shortId and m.vendor_id = ''10035'' and m.pay_time >= current_date - interval 2 month) as value", "paramNameList": ["shortId"]}}', '=', NULL, '0', NULL, 'Integer', 0, 0, '2024-03-05 16:10:08');
INSERT INTO abc_cis_basic_dev.v2_clinic_tag_define
(id, name, `type`, tag_key, priority, min_re_tag_duration, generate_strategy, generate_rule, match_symbol, min_value, eq_value, max_value, eq_value_datatype, is_value_pass, is_deleted, created)
VALUES(51, '库存预警', 13, 'goodsPromotionType.stockWarning', 0, NULL, 'BY_FIELD', '{"fieldRule": {"targetField": "hisType"}}', '=', NULL, '1', NULL, 'Integer', 0, 0, '2024-03-05 16:10:08');
INSERT INTO abc_cis_basic_dev.v2_clinic_tag_define
(id, name, `type`, tag_key, priority, min_re_tag_duration, generate_strategy, generate_rule, match_symbol, min_value, eq_value, max_value, eq_value_datatype, is_value_pass, is_deleted, created)
VALUES(52, '价格更低', 13, 'goodsPromotionType.priceLower', 0, NULL, 'BY_FIELD', '{"fieldRule": {"targetField": "hisType"}}', '=', NULL, '1', NULL, 'Integer', 0, 0, '2024-03-05 16:10:08');
INSERT INTO abc_cis_basic_dev.v2_clinic_tag_define
(id, name, `type`, tag_key, priority, min_re_tag_duration, generate_strategy, generate_rule, match_symbol, min_value, eq_value, max_value, eq_value_datatype, is_value_pass, is_deleted, created)
VALUES(53, '近一日点击、搜索', 13, 'goodsPromotionType.searchClick', 0, NULL, 'BY_FIELD', '{"fieldRule": {"targetField": "hisType"}}', '=', NULL, '1', NULL, 'Integer', 0, 0, '2024-03-05 16:10:08');
INSERT INTO abc_cis_basic_dev.v2_clinic_tag_define
(id, name, `type`, tag_key, priority, min_re_tag_duration, generate_strategy, generate_rule, match_symbol, min_value, eq_value, max_value, eq_value_datatype, is_value_pass, is_deleted, created)
VALUES(54, '商城意向', 9, 'mallOrderCount.equalZero', 0, NULL, 'BY_SQL', '{"sqlRule": {"targetSql": "select exists(select id from abc_bis_dev.v1_mall_order_item where clinic_id = :shortId and category_id in (''10004'',''10001'') and is_deleted = 0 ) as value", "paramNameList": ["shortId"]}}', '=', NULL, '0', NULL, 'String', 0, 0, '2024-05-27 17:22:13');
INSERT INTO abc_cis_basic_dev.v2_clinic_tag_define
(id, name, `type`, tag_key, priority, min_re_tag_duration, generate_strategy, generate_rule, match_symbol, min_value, eq_value, max_value, eq_value_datatype, is_value_pass, is_deleted, created)
VALUES(55, '商城流失预警', 9, 'mallOrderCount.monthEqualZeroWar', 0, NULL, 'BY_SQL', '{"sqlRule": {"targetSql": "select case when c.a = 1 and c.b = 0 then 1 else 0 end as value from (select (select count(m.id) > 0 from abc_bis_dev.v1_mall_order m inner join abc_bis_dev.v1_mall_order_item n on m.id = n.order_id and n.category_id in (''10004'', ''10001'') where m.clinic_id = :shortId and m.vendor_id = ''10035'' and m.pay_time between current_date - interval 2 month and current_date - interval 1 month) as a, (select count(m.id) > 0 from abc_bis_dev.v1_mall_order m inner join abc_bis_dev.v1_mall_order_item n on m.id = n.order_id and n.category_id in (''10004'', ''10001'') where m.clinic_id = :shortid and m.vendor_id = ''10035'' and m.pay_time between current_date - interval 1 month and current_date) as b) c", "paramNameList": ["shortId"]}}', '=', NULL, '1', NULL, 'Integer', 0, 0, '2024-06-04 20:45:37');
