# 连锁是否开启CA电子签名
INSERT INTO v2_property_register_info (id, name, `key`, required, type, value_type, `order`, `values`, default_value,
                                       comment, node_type, is_deleted, created_by, created, last_modified_by,
                                       last_modified, scope, key_first, key_second, key_third, key_fourth, key_fifth,
                                       build_in)
VALUES (substr(uuid_short(), 4), '是否开启CA电子签名', 'clinicBasic.isEnableCA', 0, 'switch', 'number', 0,
        '[{"label": "不开启", "value": 0}, {"label": "开启", "value": 1}]', '0', '是否开启CA电子签名', 1, 0,
        '00000000000000000000000000000000', '2023-06-28 16:51:02', '00000000000000000000000000000000',
        '2023-06-28 16:51:02', 'clinic', 'clinicBasic', 'isEnableCA', null, null, null, 0);

# 百安开启CA电子签名
# INSERT INTO v2_property_config_item (id, `key`, value, scope, scope_id, is_deleted, created_by,
#                                                        created, last_modified_by, last_modified, key_first, key_second,
#                                                        key_third, key_fourth, key_fifth, v2_scope_id)
# VALUES ('ffffffff0000000006f345300303e002', 'clinicBasic.isEnableCA', '1', 'clinic', null, 0,
#         '00000000000000000000000000000000', '2023-06-29 13:54:30', '00000000000000000000000000000000',
#         '2023-06-29 13:54:30', 'clinicBasic', 'isEnableCA', null, null, null,
#         'fff730ccc5ee45d783d82a85b8a0e52d');

alter table v2_clinic_online_doctor
    add prescription_use_ca int default 0 not null comment '使用电子签名开具处方 0:否 1:是';


create table v2_clinic_reminder_read
(
    id          bigint unsigned                       not null
        primary key,
    chain_id    varchar(32)                           not null comment '连锁ID',
    clinic_id   varchar(32) default ''                not null comment '门店ID',
    employee_id varchar(32)                           not null comment '创建人',
    scene       varchar(32)                           not null comment '场景',
    created     timestamp   default CURRENT_TIMESTAMP not null comment '创建时间'
)
    comment '提醒已读';

create index ix_chain_id_clinic_id_employee_id
    on v2_clinic_reminder_read (chain_id, clinic_id, employee_id);

